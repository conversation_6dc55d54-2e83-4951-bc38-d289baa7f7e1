#### 概述

solr中创建索引的步骤为：

1. 预先添加相关filed schema
2. 创建collection，关联schema config
3. 创建索引字段映射文件，将hbase表中cf内的字段与schema中定义的field的关联
4. 根据索引字段文件创建索引，关联相关的hbase表和 solr的collection

#### 详细描述

##### 一.创建filed schema config

​	命令行创建schema需要借助阿里云提供的 solr工具：==solr-7.3.1-ali-1.1.tgz==

​    schema配置目录必须在solr工具包固定的目录下：~/hbasetool/solr-7.3.1-ali-1.1/server/solr/configsets	

​	如：本项目需要的订单，流水schema配置对应的目录分别是：

​	~/hbasetool/solr-7.3.1-ali-1.1/server/solr/configsets/upay_order

 	~/hbasetool/solr-7.3.1-ali-1.1/server/solr/configsets/upay_tx

![image-20190916153112501](https://clearance-file.oss-cn-hangzhou-internal.aliyuncs.com/files/image-20190916153112501.png)



##### 1.订单schema配置

 ```shell
configsets/upay_order/conf/managed-schema
 ```

创建订单schema:

```shell
$ solr-7.3.1-ali-1.1/bin/solr zk upconfig -d upay_order -n upay_order_config
```

​	

##### 2.流水schema配置

```shell
configsets/upay_tx/conf/managed-schema
```

创建流水schema

```shell
$ solr-7.3.1-ali-1.1/bin/solr zk upconfig -d upay_tx -n upay_tx_config
```



##### 二.创建collection

登录solr 管理后台，add collections 

##### 1.创建订单collection

创建collection  ,名称为： upay_order，选择config: upay_order_config



##### 2.创建流水collection

创建collection,名称为： upay_tx ，config选择： upay_tx_config



##### 三.创建索引映射字段：

##### 1.订单表索引映射文件：

 ```shell
$ solr/idxConf/upay_order.xml
 ```

创建订单索引 idx_order

```shell
./solr-indexer add -n idx_order -f /home/<USER>/hbasetool/solr-7.3.1-ali-1.1/example/files/conf/upay_order.xml -c upay_order
```



##### 2.流水表索引映射文件

```shell
$ solr/idxConf/upay_tx.xml
```

 创建流水索引  idx_tx

```shell
./solr-indexer add -n idx_tx -f /home/<USER>/hbasetool/solr-7.3.1-ali-1.1/example/files/conf/upay_tx.xml -c upay_tx
```



##### 备注：

查看索引信息：

```shell
./solr-indexer list -dump
```

索引rebuild：

```shell
//订单
./solr-indexer rebuild -n idx_order -t upay:o -r 10


//流水
./solr-indexer rebuild -n idx_tx -t upay:tx -r 10
```



查看索引rebuild状态

```shell
./solr-indexer rebuild-status -n idx_tx  -t upay:tx

```



​	

#### 生产环境：hbase,solr相关表，schema配置，索引名对应关系



| hbase表名 | solr-collection名 | 选用的config      | 索引名    |
| --------- | ----------------- | ----------------- | --------- |
| upay:tx   | upay_tx           | upay_tx_config    | idx_tx    |
| upay:o    | upay_order        | upay_order_config | idx_order |

