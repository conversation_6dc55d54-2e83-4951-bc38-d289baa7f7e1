#### 概述

​      收钱吧冷热数据分离存储方案基于阿里云Hbase的冷热自动分离来实现Hbase中冷热数据的分离存储。

我们定义三个月之内的交易数据为热数据，存在热表(upay:o, upay:tx)中；三个月以前的数据为冷数据,存在冷表(upay:o_cold, upay:tx_cold)中。

#### 方案细节解释

​		收钱吧冷热数据分离存储方案使用Hbase存数据，使用solr做全文索引。在离线数据同步以及索引创建过程中，solr会反查Hbase,因此会出现以下情况：冷数据在写入Hbase的热表中后，由于TTL设置，会被Hbase自动flush到冷表中，这个时候solr来反查热表，会发现数据不存在。导致solr同步出错，而无法继续创建索引。基于此背景，采用的解决思路是：

1. 创建好热表，冷表；

2. 关闭热表的replication_scope，减少数据大量写入时候的实时日志WAL

```sql
--订单
alter 'upay:o',{NAME => 'f1',REPLICATION_SCOPE=> '0'}
--流水
alter 'upay:tx',{NAME => 'f1',REPLICATION_SCOPE=> '0'}
```

3.  将冷数据全部写入Hbase热表;
4. 等待solr将冷数据索引全部建好；
5. 修改热表表结构，数据TTL，关联到冷表；
6. 手动对热表执行flush 和 major compact 将数据刷入到冷表；
7. 待冷数据刷入冷表后，开启REPLICATION_SCOPE，导入热数据

```sql
--订单
alter 'upay:o',{NAME => 'f1',REPLICATION_SCOPE=> '1'}
--流水
alter 'upay:tx',{NAME => 'f1',REPLICATION_SCOPE=> '2'}
```





#### 建表语句：

##### 1.订单

```mysql
--热表
create 'upay:o',{NAME => 'f1',COMPRESSION => 'snappy',REPLICATION_SCOPE=> '1' }, { NUMREGIONS => 640, SPLITALGO => 'HexStringSplit' }

--冷表
create 'upay:o_cold',{NAME => 'f1',COMPRESSION => 'snappy',REPLICATION_SCOPE=> '1' }, { NUMREGIONS => 640, SPLITALGO => 'HexStringSplit' },CONFIGURATION => {'HFILE_STORAGE_POLICY'=>'COLD'}

--关联：
alter 'upay:o',CONFIGURATION => {'hbase.hstore.engine.class' => 'org.apache.hadoop.hbase.regionserver.TransferStoreEngine','hbase.transfercompactor.sink.table'=> 'upay:o_cold', 'hbase.transfercompactor.source.ttl' => 7776000}

--设置hbase.transfercompactor.source.table ，否则无法使用hbase-2.x-client的 临界时间点冷热表自动查数据的特性

alter 'upay:o_cold',CONFIGURATION => {'hbase.transfercompactor.source.table'=> 'upay:o'}
```

##### 2.流水

```mysql
--热表
create 'upay:tx',{NAME => 'f1',COMPRESSION => 'snappy',REPLICATION_SCOPE=> '1' }, { NUMREGIONS => 640, SPLITALGO => 'HexStringSplit' }

--冷表
create 'upay:tx_cold',{NAME => 'f1',COMPRESSION => 'snappy',REPLICATION_SCOPE=> '1' }, { NUMREGIONS => 640, SPLITALGO => 'HexStringSplit' },CONFIGURATION => {'HFILE_STORAGE_POLICY'=>'COLD'}

--关联：
alter 'upay:tx',CONFIGURATION => {'hbase.hstore.engine.class' => 'org.apache.hadoop.hbase.regionserver.TransferStoreEngine','hbase.transfercompactor.sink.table'=> 'upay:tx_cold', 'hbase.transfercompactor.source.ttl' => 7776000}


--设置hbase.transfercompactor.source.table ，否则无法使用hbase-2.x-client的 临界时间点冷热表自动查数据的特性

alter 'upay:tx_cold',CONFIGURATION => {'hbase.transfercompactor.source.table'=> 'upay:tx'}
```



