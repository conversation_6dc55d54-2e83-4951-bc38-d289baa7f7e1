#### 概述

冷热数据分离存储项目使用阿里官方工具datax来实现历史数据导入hbase

####一.datax介绍

​	见github  [datax](https://github.com/alibaba/DataX)

####二.实现步骤

#####1. 配置订单导入job模板

​	见文件 **order_mysql_2_hbase.json**

#####2.  配置流水导入job模板

​	见文件 **tx_mysql_2_hbase.json**

备注：

`	1.配置文件需要自行将数据库字段编号，类型做转换`

`2.具体字段含义看见datax说明，此处不再赘述`

#####3. 开发脚本，实现指定日期数据内的订单流水数据导入功能

#### 三.其他说明

#####1.导入速度

​	导入速度与datax job配置，执行脚本的服务器以及hbase集群有关；

   1).datax job配置：

​		可适当调大channel个数；

​        启动的时候适当调大内存；		

​	2).服务器

​		 任务执行过程中CPU和网络IO比较大，因此建议使用网络增强型ECS；

​    3）hbase集群：

​		 a.hbase集群core节点越多导入速度越快；

​		 b.开启solr索引实时同步的时候会发生反压现象，导致数据导入hbase速度下降

​		说明：目前hbase集群中有16个core节点，datax channel配置为120，ECS 16核32G网络增强型配置下， 不开启solr ，平均导入速度可以达到52000条/秒；开启solr后平均导入速度在35000条/秒



##### 2.其他

 订单流水自动导入脚本见本目录下两个php文件。