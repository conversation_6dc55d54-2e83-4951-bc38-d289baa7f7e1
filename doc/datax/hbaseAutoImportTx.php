<?php
date_default_timezone_set("Asia/Shanghai");

/**
 * Created by PhpStorm.
 * 自动调用datax，将数据从离线数据库mysql导入到hbase
 * dataX使用命令实例： python datax.py  --jvm="-Xms4G -Xmx4G"   ../job/tx_mysql_2_ots_20190110.json
 * User: maoyu
 * Date: 2019-06-18
 * Time: 10:45
 */
//$cmd = "awk '{gsub(/order_20190111/,\"order_%s\"); print $0}' order_mysql_2_ots.json > order_mysql_2_ots_%s.json";
$cmd = "awk '{gsub(/transaction_20190101/,\"transaction_%s\"); print $0}' %s > %s";
$GLOBALS['dingtalkRobot'] = 'https://oapi.dingtalk.com/robot/send?access_token=31e0e5c6013e88d19949c2e735d7a69ac7c8aefa6527ea811aa2bc32217ec363';
$startDate = '20191011';
$endDate = '20191013';
$currentDate = $startDate;
$workDir = '/home/<USER>/datax/datax';
$GLOBALS['workDir'] = $workDir;
$runCmd = '/usr/bin/python '.$workDir.DIRECTORY_SEPARATOR.'bin'.DIRECTORY_SEPARATOR.'datax.py  --jvm="-Xms24G -Xmx24G" %s &';

$jobConfDir   = $workDir.DIRECTORY_SEPARATOR. 'job';
$sourceTemplate = $jobConfDir.DIRECTORY_SEPARATOR.'tx_mysql_2_hbase.json';
$fileTemplate = $jobConfDir.DIRECTORY_SEPARATOR.'tx_mysql_2_hbase_%s.json';
$outputLogFile = date('YmdHis').'dataxImportRtn.log';
$start;
$end;
while(strtotime($currentDate) < strtotime($endDate)){
    $nowDt = date('Y-m-d');
    $currentDate =  date("Ymd",strtotime($currentDate)+86400);
    $msg = "  开始导入 {$currentDate} 的流水".PHP_EOL;
    writeLog($msg,$outputLogFile,true);
    $start = time()+0;
    $curJobConf = sprintf($fileTemplate,$currentDate);
    if (!file_exists($curJobConf)){
        $curCmd = sprintf($cmd,$currentDate,$sourceTemplate, $curJobConf);
        $cOutput = [];
        $cRtn = 1;
        exec($curCmd,$cOutput,$cRtn);

    }
    $importCmd = sprintf($runCmd,$curJobConf);
    $rOutput=[];
    $rRtn = 1;
    exec($importCmd,$rOutput,$rRtn);
    if ($rRtn !==0){
        $rMsg = "执行任务 {$importCmd} 出错".PHP_EOL;
        writeLog($rMsg,$outputLogFile,true);
        exit("执行命令 $importCmd 出错");
    }
    while(true){
        $i = 1;
        $monitorOutput = [];
        $execRtn = 1;
        $monitorCmd = 'ps aux | grep datax | grep -v grep';
        exec($monitorCmd,$monitorOutput,$execRtn);
//       if ($execRtn !== 0){
//           file_put_contents($outputLogFile,"执行监控命令 {$monitorOutput} 出错".PHP_EOL, FILE_APPEND);
//           exit("执行命令 $monitorOutput 出错");
//       }
        if (!empty($monitorOutput)){
            //任务没处理完，sleep 半分钟后；
            $mInprogMsg =  "任务 {$importCmd} 执行中......当前已检查{$i} 次".PHP_EOL;
            writeLog($mInprogMsg,$outputLogFile,false);
            $i++;
            sleep(15);
            continue;
        }else{
            break;
        }
    }
    $end = time()+0;
    $cost = $end - $start;
    $monMsg = " {$currentDate}流水导入成功 ".PHP_EOL;
   $cntCmd = 'tail '.$GLOBALS['workDir'].'/log/'.$nowDt.'/_hbase_'.$currentDate.'_json*.log';
    $outpt = [];
    exec($cntCmd,$outpt,$execRtn);
    $monMsg .= implode($outpt,PHP_EOL);
    
    writeLog($monMsg,$outputLogFile,true);

}

function writeLog($msg,$outputLogFile,$sendDingtalkMsg = false){
    $msg = date("Ymd H:i:s")." ".$msg;
    file_put_contents($outputLogFile,$msg, FILE_APPEND);
    if($sendDingtalkMsg){
        sendDingTalkMsg($msg);
    }
}

//Dingtalk Robot :https://oapi.dingtalk.com/robot/send?access_token=e8aec6f3093868e33910fad029ab4b18801d3664eccc2b136373f0e41c775bd7
function sendDingTalkMsg($msg){
    $data3 = array('msgtype' => 'text', 'text' => array('content' => $msg));
    $result3 = request_by_curl($GLOBALS['dingtalkRobot'], json_encode($data3,JSON_UNESCAPED_UNICODE));
}

function request_by_curl($remote_server, $post_string)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $remote_server);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=utf-8'));
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_string);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    // 线下环境不用开启curl证书验证, 未调通情况可尝试添加该代码
    // curl_setopt ($ch, CURLOPT_SSL_VERIFYHOST, 0);
    // curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER, 0);
//    var_dump($post_string);
    $data = curl_exec($ch);
    curl_close($ch);

    return $data;
}
