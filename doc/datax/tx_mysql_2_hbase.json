{"job": {"setting": {"speed": {"channel": 120}}, "content": [{"reader": {"name": "mysqlreader", "parameter": {"username": "", "password": "", "splitPk": "id", "column": ["id", "tsn", "client_tsn", "type", "subject", "body", "status", "effective_amount", "original_amount", "paid_amount", "received_amount", "items", "buyer_uid", "buyer_login", "merchant_id", "store_id", "terminal_id", "operator", "order_sn", "order_id", "provider", "payway", "sub_payway", "trade_no", "product_flag", "extra_params", "extra_out_fields", "extended_params", "reflect", "config_snapshot", "finish_time", "channel_finish_time", "biz_error_code", "provider_error_info", "ctime", "mtime", "deleted", "version", "nfc_card"], "connection": [{"table": ["transaction_20190101"], "jdbcUrl": ["********************************"]}]}}, "writer": {"name": "hbase11xwriter", "parameter": {"hbaseConfig": {"hbase.zookeeper.quorum": ""}, "table": "upay:tx", "mode": "normal", "rowkeyColumn": [{"index": 14, "type": "string"}, {"index": 34, "type": "long"}, {"index": 0, "type": "string"}], "column": [{"index": 0, "name": "f1:id", "type": "string"}, {"index": 1, "name": "f1:tsn", "type": "string"}, {"index": 2, "name": "f1:client_tsn", "type": "string"}, {"index": 3, "name": "f1:type", "type": "int"}, {"index": 4, "name": "f1:subject", "type": "string"}, {"index": 5, "name": "f1:body", "type": "string"}, {"index": 6, "name": "f1:status", "type": "int"}, {"index": 7, "name": "f1:effective_amount", "type": "long"}, {"index": 8, "name": "f1:original_amount", "type": "long"}, {"index": 9, "name": "f1:paid_amount", "type": "long"}, {"index": 10, "name": "f1:received_amount", "type": "long"}, {"index": 11, "name": "f1:items", "type": "string"}, {"index": 12, "name": "f1:buyer_uid", "type": "string"}, {"index": 13, "name": "f1:buyer_login", "type": "string"}, {"index": 14, "name": "f1:merchant_id", "type": "string"}, {"index": 15, "name": "f1:store_id", "type": "string"}, {"index": 16, "name": "f1:terminal_id", "type": "string"}, {"index": 17, "name": "f1:operator", "type": "string"}, {"index": 18, "name": "f1:order_sn", "type": "string"}, {"index": 19, "name": "f1:order_id", "type": "string"}, {"index": 20, "name": "f1:provider", "type": "int"}, {"index": 21, "name": "f1:payway", "type": "int"}, {"index": 22, "name": "f1:sub_payway", "type": "int"}, {"index": 23, "name": "f1:trade_no", "type": "string"}, {"index": 24, "name": "f1:product_flag", "type": "string"}, {"index": 25, "name": "f1:extra_params", "type": "string"}, {"index": 26, "name": "f1:extra_out_fields", "type": "string"}, {"index": 27, "name": "f1:extended_params", "type": "string"}, {"index": 28, "name": "f1:reflect", "type": "string"}, {"index": 29, "name": "f1:config_snapshot", "type": "string"}, {"index": 30, "name": "f1:finish_time", "type": "long"}, {"index": 31, "name": "f1:channel_finish_time", "type": "long"}, {"index": 32, "name": "f1:biz_error_code", "type": "string"}, {"index": 33, "name": "f1:provider_error_info", "type": "string"}, {"index": 34, "name": "f1:ctime", "type": "long"}, {"index": 35, "name": "f1:mtime", "type": "long"}, {"index": 36, "name": "f1:deleted", "type": "int"}, {"index": 37, "name": "f1:version", "type": "long"}, {"index": 38, "name": "f1:nfc_card", "type": "string"}], "versionColumn": {"index": 34}, "encoding": "utf-8", "autoflush": false}}}]}}