{"job": {"setting": {"speed": {"channel": 120}}, "content": [{"reader": {"name": "mysqlreader", "parameter": {"username": "", "password": "", "splitPk": "id", "column": ["id", "sn", "client_sn", "subject", "body", "items", "net_items", "status", "tcp_modified", "original_total", "net_original", "effective_total", "net_effective", "total_discount", "net_discount", "buyer_uid", "buyer_login", "merchant_id", "store_id", "terminal_id", "operator", "provider", "payway", "sub_payway", "trade_no", "reflect", "ctime", "mtime", "deleted", "version", "nfc_card"], "connection": [{"table": ["order_20190101"], "jdbcUrl": ["********************************"]}]}}, "writer": {"name": "hbase11xwriter", "parameter": {"hbaseConfig": {"hbase.zookeeper.quorum": ""}, "table": "upay:o", "mode": "normal", "rowkeyColumn": [{"index": 17, "type": "string"}, {"index": 26, "type": "long"}, {"index": 0, "type": "string"}], "column": [{"index": 0, "name": "f1:id", "type": "string"}, {"index": 1, "name": "f1:sn", "type": "string"}, {"index": 2, "name": "f1:client_sn", "type": "string"}, {"index": 3, "name": "f1:subject", "type": "string"}, {"index": 4, "name": "f1:body", "type": "string"}, {"index": 5, "name": "f1:items", "type": "string"}, {"index": 6, "name": "f1:net_items", "type": "string"}, {"index": 7, "name": "f1:status", "type": "int"}, {"index": 8, "name": "f1:tcp_modified", "type": "int"}, {"index": 9, "name": "f1:original_total", "type": "long"}, {"index": 10, "name": "f1:net_original", "type": "long"}, {"index": 11, "name": "f1:effective_total", "type": "long"}, {"index": 12, "name": "f1:net_effective", "type": "long"}, {"index": 13, "name": "f1:total_discount", "type": "long"}, {"index": 14, "name": "f1:net_discount", "type": "long"}, {"index": 15, "name": "f1:buyer_uid", "type": "string"}, {"index": 16, "name": "f1:buyer_login", "type": "string"}, {"index": 17, "name": "f1:merchant_id", "type": "string"}, {"index": 18, "name": "f1:store_id", "type": "string"}, {"index": 19, "name": "f1:terminal_id", "type": "string"}, {"index": 20, "name": "f1:operator", "type": "string"}, {"index": 21, "name": "f1:provider", "type": "int"}, {"index": 22, "name": "f1:payway", "type": "int"}, {"index": 23, "name": "f1:sub_payway", "type": "int"}, {"index": 24, "name": "f1:trade_no", "type": "string"}, {"index": 25, "name": "f1:reflect", "type": "string"}, {"index": 26, "name": "f1:ctime", "type": "long"}, {"index": 27, "name": "f1:mtime", "type": "long"}, {"index": 28, "name": "f1:deleted", "type": "int"}, {"index": 29, "name": "f1:version", "type": "long"}, {"index": 30, "name": "f1:nfc_card", "type": "string"}], "versionColumn": {"index": 26}, "encoding": "utf-8", "autoflush": false}}}]}}