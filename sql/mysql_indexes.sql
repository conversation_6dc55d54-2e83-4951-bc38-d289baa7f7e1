-- MySQL indexes for transaction table performance optimization
-- Created for HBase/Solr to MySQL migration

-- Primary index on transaction table
CREATE INDEX idx_transaction_merchant_ctime ON transaction (merchant_id, ctime DESC);
CREATE INDEX idx_transaction_store_ctime ON transaction (store_id, ctime DESC);
CREATE INDEX idx_transaction_terminal_ctime ON transaction (terminal_id, ctime DESC);
CREATE INDEX idx_transaction_tsn_merchant_ctime ON transaction (tsn, merchant_id, ctime DESC);

-- Composite indexes for common query patterns
CREATE INDEX idx_transaction_merchant_status_ctime ON transaction (merchant_id, status, ctime DESC);
CREATE INDEX idx_transaction_merchant_type_ctime ON transaction (merchant_id, type, ctime DESC);
CREATE INDEX idx_transaction_merchant_payway_ctime ON transaction (merchant_id, payway, ctime DESC);
CREATE INDEX idx_transaction_merchant_provider_ctime ON transaction (merchant_id, provider, ctime DESC);

-- Indexes for summary queries
CREATE INDEX idx_transaction_type_status_ctime ON transaction (type, status, ctime DESC);
CREATE INDEX idx_transaction_store_type_ctime ON transaction (store_id, type, ctime DESC);
CREATE INDEX idx_transaction_terminal_type_ctime ON transaction (terminal_id, type, ctime DESC);

-- Index for channel finish time queries
CREATE INDEX idx_transaction_channel_finish_time ON transaction (channel_finish_time DESC);
CREATE INDEX idx_transaction_merchant_channel_finish ON transaction (merchant_id, channel_finish_time DESC);

-- Indexes for original amount range queries
CREATE INDEX idx_transaction_original_amount ON transaction (original_amount);
CREATE INDEX idx_transaction_merchant_amount_ctime ON transaction (merchant_id, original_amount, ctime DESC);

-- Indexes for order table
CREATE INDEX idx_order_merchant_ctime ON `order` (merchant_id, ctime DESC);
CREATE INDEX idx_order_store_ctime ON `order` (store_id, ctime DESC);
CREATE INDEX idx_order_terminal_ctime ON `order` (terminal_id, ctime DESC);
CREATE INDEX idx_order_sn_merchant_ctime ON `order` (sn, merchant_id, ctime DESC);

-- Composite indexes for order queries
CREATE INDEX idx_order_merchant_status_ctime ON `order` (merchant_id, status, ctime DESC);
CREATE INDEX idx_order_merchant_payway_ctime ON `order` (merchant_id, payway, ctime DESC);
CREATE INDEX idx_order_merchant_subpayway_ctime ON `order` (merchant_id, sub_payway, ctime DESC);
CREATE INDEX idx_order_merchant_provider_ctime ON `order` (merchant_id, provider, ctime DESC);

-- Indexes for original total amount queries
CREATE INDEX idx_order_original_total ON `order` (original_total);
CREATE INDEX idx_order_merchant_total_ctime ON `order` (merchant_id, original_total, ctime DESC);

-- Indexes for buyer queries
CREATE INDEX idx_order_buyer_uid_ctime ON `order` (buyer_uid, ctime DESC);
CREATE INDEX idx_order_buyer_login_ctime ON `order` (buyer_login, ctime DESC);

-- Indexes for wallet change log table
CREATE INDEX idx_wallet_change_log_merchant_ctime ON wallet_change_log (merchant_id, ctime DESC);
CREATE INDEX idx_wallet_change_log_store_ctime ON wallet_change_log (store_id, ctime DESC);
CREATE INDEX idx_wallet_change_log_terminal_ctime ON wallet_change_log (terminal_id, ctime DESC);
CREATE INDEX idx_wallet_change_log_wallet_ctime ON wallet_change_log (wallet_id, ctime DESC);

-- Composite indexes for wallet change log queries
CREATE INDEX idx_wallet_change_log_merchant_wallet_ctime ON wallet_change_log (merchant_id, wallet_id, ctime DESC);
CREATE INDEX idx_wallet_change_log_merchant_change_type_ctime ON wallet_change_log (merchant_id, change_type, ctime DESC);
CREATE INDEX idx_wallet_change_log_wallet_type_ctime ON wallet_change_log (wallet_type, ctime DESC);

-- Indexes for related transaction/order queries
CREATE INDEX idx_wallet_change_log_related_tsn ON wallet_change_log (related_tsn);
CREATE INDEX idx_wallet_change_log_related_order_sn ON wallet_change_log (related_order_sn);

-- Indexes for amount range queries
CREATE INDEX idx_wallet_change_log_amount ON wallet_change_log (amount);
CREATE INDEX idx_wallet_change_log_merchant_amount_ctime ON wallet_change_log (merchant_id, amount, ctime DESC);

-- Indexes for buyer queries
CREATE INDEX idx_wallet_change_log_buyer_uid_ctime ON wallet_change_log (buyer_uid, ctime DESC);
CREATE INDEX idx_wallet_change_log_buyer_login_ctime ON wallet_change_log (buyer_login, ctime DESC);

-- Partitioning considerations (if using MySQL partitioning)
-- ALTER TABLE transaction PARTITION BY HASH(merchant_id) PARTITIONS 16;
-- ALTER TABLE `order` PARTITION BY HASH(merchant_id) PARTITIONS 16;
-- ALTER TABLE wallet_change_log PARTITION BY HASH(merchant_id) PARTITIONS 16;

-- Additional composite indexes for complex queries
CREATE INDEX idx_transaction_merchant_store_terminal_ctime ON transaction (merchant_id, store_id, terminal_id, ctime DESC);
CREATE INDEX idx_order_merchant_store_terminal_ctime ON `order` (merchant_id, store_id, terminal_id, ctime DESC);
CREATE INDEX idx_wallet_change_log_merchant_store_terminal_ctime ON wallet_change_log (merchant_id, store_id, terminal_id, ctime DESC);

-- Indexes for status and time queries
CREATE INDEX idx_transaction_status_ctime ON transaction (status, ctime DESC);
CREATE INDEX idx_order_status_ctime ON `order` (status, ctime DESC);

-- Indexes for provider and payway queries
CREATE INDEX idx_transaction_provider_payway_ctime ON transaction (provider, payway, ctime DESC);
CREATE INDEX idx_order_provider_payway_ctime ON `order` (provider, payway, ctime DESC);

-- Indexes for finish time queries
CREATE INDEX idx_transaction_finish_time ON transaction (finish_time DESC);
CREATE INDEX idx_transaction_merchant_finish_time ON transaction (merchant_id, finish_time DESC);