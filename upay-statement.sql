CREATE SCHEMA IF NOT EXISTS `upay_transaction` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
USE `upay_transaction`;

--对账单导出任务表
CREATE TABLE `statement_task_log` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '申请任务类型，1：商户对账单下载;2：订单下载;3：集团对账单下载;',
  `apply_system` int(11) NOT NULL DEFAULT '1' COMMENT '请求系统，1:商户平台; 2:SP运营平台; 3:CRM; 4: 服务商平台; 5:推广者平台; 6:OSP1.0; 7:MSP1.0; 9:其他内部服务, 10: 未知',
  `title` varchar(200) DEFAULT NULL COMMENT '导出任务标题',
  `task_signature` varchar(32) DEFAULT NULL COMMENT '导出任务数字签名md5(类型+条件)',
  `payload` blob COMMENT '申请参数（JSON）',
  `user_id` varchar(36) DEFAULT NULL COMMENT '用户id',
  `apply_status` int(11) NOT NULL DEFAULT '0' COMMENT '任务申请状态，0：新申请，1：执行中，2：执行成功，3：执行失败',
  `apply_date` date NOT NULL COMMENT '申请日期',
  `apply_result` text COMMENT '申请结果',
  `operate_time` bigint(20) DEFAULT NULL COMMENT '执行时间, 毫秒数',
  `statement_size` bigint(20) DEFAULT NULL COMMENT '文件大小，字节数',
  `statement_row` bigint(20) DEFAULT NULL COMMENT '对账行数',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  KEY `idx_task_signature` (`task_signature`),
  KEY `idx_apply_date` (`apply_date`),
  KEY `idx_user_id_ctime` (`user_id`,`ctime`),
  KEY `idx_ctime_apply_status` (`ctime`,`apply_date`),
  KEY `idx_mtime` (`mtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='对账单下载任务申请日志表';

--对账单配置参数
CREATE TABLE `upay_transaction`.`statement_config` (
  `type` varchar(200) NOT NULL  COMMENT '配置字段属性名称',
  `key` varchar(200) NOT NULL  COMMENT '配置字段属性值',
  `name` varchar(200) DEFAULT NULL COMMENT '配置字段属性值中文含义',
  `extra_info` blob COMMENT '额外信息',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='对账单配置参数';


ALTER TABLE `upay_transaction`.`statement_object_config` ADD split_type int(11) NOT NULL DEFAULT '0' COMMENT '对账文件拆分设置：0：汇总与明细拆分为多文件并压缩；1:50万条记录以内汇总与明细合并单文件不压缩';
ALTER TABLE `upay_transaction`.`statement_object_config` ADD sheet_type int(11) NOT NULL DEFAULT '0' COMMENT '对账单汇总版式设置:0:详细版；1：简洁版；2：自定义版';
ALTER TABLE `upay_transaction`.`statement_object_config` ADD sheet_params blob COMMENT '汇总样式参数配置';


insert into statement_config(`type`,`key`,`name`) values
('payway','1',"支付宝"),
('payway','2',"支付宝2.0"),
('payway','3',"微信"),
('payway','4',"百度钱包"),
('payway','5',"京东钱包"),
('payway','6',"qq钱包"),
('payway','8',"拉卡拉钱包"),
('payway','17',"银联云闪付");
