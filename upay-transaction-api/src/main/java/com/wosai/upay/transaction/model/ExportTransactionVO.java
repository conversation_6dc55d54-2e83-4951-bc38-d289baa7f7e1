package com.wosai.upay.transaction.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExportTransactionVO {
    private List<String> edgeRecordIds;

    private List<Map<String, Object>> transactionList;

    private boolean flagContinue;

    private boolean flagBreak;

    private int scanSize;

    private long nextStart;
}
