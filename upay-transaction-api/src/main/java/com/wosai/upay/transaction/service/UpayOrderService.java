package com.wosai.upay.transaction.service;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.annotation.DBSelectService;
import com.wosai.upay.transaction.model.ChangeShiftsParam;

@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "rpc/upayorder")
public interface UpayOrderService {

    /**
     * 查询交易明细
     *
     * @param merchantId
     * @param storeId
     * @param pageInfo
     * @param queryFilter status
     *                    type
     *                    removeExtraOutFields
     *                    storeName
     *                    orderSn
     *                    orderSns
     *                    storeIds
     * @return
     */
    ListResult getTransactionList(String merchantId, String storeId, PageInfo pageInfo, Map queryFilter);

    /**
     * 查询收钱吧pc桌面版的订单明细
     *
     * @param terminalSn
     * @param pageInfo
     * @param queryFilter orderSn
     * @return
     */
    ListResult getOrderListForPcDesktop(String terminalSn, PageInfo pageInfo, Map queryFilter);


    /**
     * 查询pc版本的流水明细
     *
     * @param terminalSn
     * @param pageInfo
     * @param queryFilter orderSn
     * @return
     */
    ListResult getTransactionListForPcDesktop(String terminalSn, PageInfo pageInfo, Map queryFilter);

    /**
     * 通过订单号获取交易流水详情
     *
     * @param orderSn
     * @return
     */
    List getTransactionListByOrderSn(String orderSn);

    /**
     * 通过订单号获取原始交易流水详情
     * 
     * 注意：返回数据内容和getTransactionListByOrderSn接口不一样，不能改为getTransactionListByOrderSn接口
     *
     * @param orderSn
     * @return
     */
    List<Map<String,Object>> getOriginalTransactionListByOrderSn(String orderSn);

    /**
     * 获取订单详情
     *
     * @param orderSn
     * @return
     */
    Map getOrderDetailByOrderSn(String orderSn);

    /**
     * 查询终端结算统计信息
     *
     * @param terminalSn 终端号
     * @param batchSn    批次号，不填默认查询终端签到最后一笔
     * @return start_date 签到时间
     * end_date    签退时间
     * batch_sn   批次号
     * cashier_no 收银员编号（签退时上送）
     * pay_amount 总支付交易金额
     * pay_count  总支付交易笔数
     * pay_detail 支付交易详细信息
     * name     支付方式
     * fee      总支付交易金额
     * cnt      总支付交易笔数
     * refund_amount 总退款交易金额
     * refund_count 总退款交易笔数
     * refund_detail 退款交易详细信息
     * name     支付方式
     * fee      总退款交易金额
     * cnt      总退款交易笔数
     */
    @Deprecated
    Map getTerminalChangeShiftsStatisticsInfo(@NotNull(message = "终端号不能为空") String terminalSn, String batchSn);

    /**
     * 分页查询终端结算统计信息
     *
     * @param terminalSn 终端号
     * @param startDate  签到区间起始时间(unix时间戳)
     * @param endDate    签到区间结束时间(unix时间戳)
     * @param PageInfo   分页信息
     * @return total  总数量
     * records 详细信息
     * start_date 签到时间
     * end_date    签退时间
     * batch_sn   批次号
     * cashier_no 收银员编号（签退时上送）
     * pay_amount 总支付交易金额
     * pay_count  总支付交易笔数
     * pay_detail 支付交易详细信息
     * name     支付方式
     * fee      总支付交易金额
     * cnt      总支付交易笔数
     * refund_amount 总退款交易金额
     * refund_count 总退款交易笔数
     * refund_detail 退款交易详细信息
     * name     支付方式
     * fee      总退款交易金额
     * cnt      总退款交易笔数
     */
    @Deprecated
    ListResult getTerminalChangeShiftsStatisticsList(@NotNull(message = "终端号不能为空") String terminalSn, Long startDate, Long endDate, PageInfo pageInfo);


    /**
     * 查询结算统计信息
     *
     * @param terminal_sn 终端号
     * @param cash_desk_id 收银台id
     * @param batchSn    批次号，不填默认查询终端签到最后一笔
     * @return start_date 签到时间
     * end_date    签退时间
     * batch_sn   批次号
     * cashier_no 收银员编号（签退时上送）
     * pay_amount 总支付交易金额
     * pay_count  总支付交易笔数
     * pay_detail 支付交易详细信息
     * name     支付方式
     * fee      总支付交易金额
     * cnt      总支付交易笔数
     * refund_amount 总退款交易金额
     * refund_count 总退款交易笔数
     * refund_detail 退款交易详细信息
     * name     支付方式
     * fee      总退款交易金额
     * cnt      总退款交易笔数
     */
    Map getChangeShiftsStatisticsInfo(ChangeShiftsParam param);

    /**
     * 分页查询终端结算统计信息
     *
     * @param terminalSn 终端号
     * @param startDate  签到区间起始时间(unix时间戳)
     * @param endDate    签到区间结束时间(unix时间戳)
     * @param PageInfo   分页信息
     * @return total  总数量
     * records 详细信息
     * start_date 签到时间
     * end_date    签退时间
     * batch_sn   批次号
     * cashier_no 收银员编号（签退时上送）
     * pay_amount 总支付交易金额
     * pay_count  总支付交易笔数
     * pay_detail 支付交易详细信息
     * name     支付方式
     * fee      总支付交易金额
     * cnt      总支付交易笔数
     * refund_amount 总退款交易金额
     * refund_count 总退款交易笔数
     * refund_detail 退款交易详细信息
     * name     支付方式
     * fee      总退款交易金额
     * cnt      总退款交易笔数
     */
    ListResult getChangeShiftsStatisticsList(ChangeShiftsParam param);

    /**
     * 获取订单信息
     *
     * @param merchantId
     * @param orderSn
     * @param clientSn
     * @return
     */
    Map<String, Object> getOrderBySn(String merchantId, String orderSn, String clientSn);

    /**
     * 更新订单信息
     *
     * @param order
     * @return
     */
    boolean updateOrder(Map<String, Object> order);
    
    /**
     * 更新流水信息
     *
     * @param order
     * @return
     */
    boolean updateTransaction(Map<String, Object> transaction);
}
