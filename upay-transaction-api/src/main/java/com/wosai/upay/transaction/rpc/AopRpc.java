package com.wosai.upay.transaction.rpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.model.SwipeCardReceiveRequest;
import com.wosai.upay.transaction.model.SwipeCardReceiveResponse;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 对接 aop 服务
 *
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "/rpc/aop")
public interface AopRpc {

    /**
     * 获取今天的刷卡收款金额
     */
    SwipeCardReceiveResponse getSwipeCardOriginalAmount(@Valid @NotNull(message = "请求参数不能为空") SwipeCardReceiveRequest request);

}
