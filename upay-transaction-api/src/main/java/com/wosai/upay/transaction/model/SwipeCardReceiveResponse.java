package com.wosai.upay.transaction.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 刷卡收款 response
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class SwipeCardReceiveResponse {

    private String state_code;

    private String target_id;

    private Integer target_type;

    private Data data;

    private Long timestamp;

    @Accessors(chain = true)
    @lombok.Data
    public static class Data {

        private String trade_count;

        private String original_amount;

    }

}
