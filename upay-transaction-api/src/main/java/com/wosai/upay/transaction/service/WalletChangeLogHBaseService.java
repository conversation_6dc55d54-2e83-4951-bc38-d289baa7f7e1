package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.model.ExportWalletChangeLogVO;
import com.wosai.upay.transaction.model.param.WalletChangeLogParam;

import java.util.List;

@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/walletChangeLogHBase")
public interface WalletChangeLogHBaseService {


    List<ExportWalletChangeLogVO> getWalletChangeLogList(WalletChangeLogParam walletChangeLogParam);


    long getWalletChangeLogCount(WalletChangeLogParam walletChangeLogParam);

}
