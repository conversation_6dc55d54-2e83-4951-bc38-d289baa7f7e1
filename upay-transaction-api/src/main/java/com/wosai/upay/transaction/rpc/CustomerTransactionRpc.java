package com.wosai.upay.transaction.rpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.annotation.DBSelectService;
import com.wosai.upay.transaction.model.customer.ListMerchantTradesRequest;
import com.wosai.upay.transaction.model.customer.SumCustomerDetailRequest;
import com.wosai.upay.transaction.model.customer.SumCustomerDetailResponse;
import com.wosai.upay.transaction.model.customer.MerchantTradesResponse;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 顾客维度 Service
 *
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "rpc/customer")
public interface CustomerTransactionRpc {

    /**
     * 顾客维度汇总
     *
     * @param merchantId 商户id
     * @param request    CustomerSummaryRequest
     * @return CustomerSummaryResponse
     */
    SumCustomerDetailResponse sumCustomerDetail(@NotEmpty(message = "商户id不能为空") String merchantId,
                                                @Valid @NotNull(message = "请求参数不能为空") SumCustomerDetailRequest request);

    /**
     * 查询商户交易列表
     *
     * @param merchantId 商户id
     * @param request    ListMerchantTradesRequest
     * @return 商户交易列表
     */
    List<MerchantTradesResponse> listMerchantTrades(@NotEmpty(message = "商户id不能为空") String merchantId,
                                                    @Valid @NotNull(message = "请求参数不能为空") ListMerchantTradesRequest request);

}
