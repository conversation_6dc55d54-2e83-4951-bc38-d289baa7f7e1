package com.wosai.upay.transaction.model.param;

import com.wosai.upay.transaction.annotation.DBSelectField;
import com.wosai.upay.transaction.constant.PageInfoConst;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class AccountRecordParam extends BaseParam {

    private final static Long MAX_TIME_INTERVAL = 1000 * 3600 * 24 * 94L;

    private final static Long MAX_TIME_INTERVAL_FOR_SORT = ********L; // 24时(h)=********毫秒(ms)


    /**
     * 门店ids 逗号分隔
     */
    private String storeIds;

    /**
     * 交易状态
     */
    private String tradeStatus;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 终端ids 逗号分隔
     */
    private String terminals;

    /**
     * 不包含终端ids 逗号分隔
     */
    private String notContainTerminals;

    private String version;

    /**
     * 商户id
     */
    private String merchant_id;

    private String merchant_user_id;

    private String group_id;

    private String group_user_id;

    /**
     * 交易sn
     */
    private String transactionSn;

    /**
     * 交易sns
     */
    private String transactionSns;

    /**
     * 是否简易账本
     */
    private Boolean simple;

    /**
     * token
     */
    private String token;

    /**
     * 操作人员 例mobile_21162f45-3689-4c8a-b35f-e00fef324e61
     */
    private String mobile;

    /**
     * qrcode_XXX
     */
    private String qrcode;

    /**
     * pos_XXX
     */
    private String pos;

    /**
     * 玛雅 maya_XXXX
     */
    private String maya;

    /**
     * posplus_XXX
     */
    private String posplus;

    /**
     * pcplugin_XXX
     */
    private String pcplugin;

    /**
     * 扫码设备列表，逗号分割
     */
    private String faceScan;

    private String groupBys;

    @DBSelectField
    private int upayQueryType;

    /**
     * 是否门店账本  收款
     */
    private boolean isStoreAccount = true;


    /**
     * 是否按天汇总数据
     */
    private boolean isSummary = true;


    /**
     * 自助点餐码
     */
    private String socode;

    /**
     * npos2.0
     */
    private String npos2;

    /**
     * 小白盒增强版2
     */
    private String groupmeal;

    /**
     * 付款类型
     */
    private String productFlag;



    /**
     * 商户集 集团商户使用 逗号分割
     */
    private String merchantIds;

    /**
     * 部门集
     */
    private String departmentIds;

    /**
     * 收银台集
     */
    private String cashDesk;


    /**
     * 是否需要今天默认数据
     */
    private boolean needTodayDefault = true;

    private boolean validTimeSpan = true;

    private String orderSns;

    private int offsetHour;

    private String buyerUids;

    private String summaryExts;

    private int permissionType;

    /**
     * 筛选金额范围
     */
    private Long minTotalAmount;
    private Long maxTotalAmount;

    public Long getMinTotalAmount() {
        return minTotalAmount;
    }

    public void setMinTotalAmount(Long minTotalAmount) {
        this.minTotalAmount = minTotalAmount;
    }

    public Long getMaxTotalAmount() {
        return maxTotalAmount;
    }

    public void setMaxTotalAmount(Long maxTotalAmount) {
        this.maxTotalAmount = maxTotalAmount;
    }

    public int getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(int permissionType) {
        this.permissionType = permissionType;
    }

    public String getBuyerUids() {
        return buyerUids;
    }

    public void setBuyerUids(String buyerUids) {
        this.buyerUids = buyerUids;
    }

    public String getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(String storeIds) {
        this.storeIds = storeIds;
    }

    public String getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(String tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTerminals() {
        return terminals;
    }

    public void setTerminals(String terminals) {
        this.terminals = terminals;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getTransactionSn() {
        return transactionSn;
    }

    public void setTransactionSn(String transactionSn) {
        this.transactionSn = transactionSn;
    }

    public Boolean getSimple() {
        return simple;
    }

    public void setSimple(Boolean simple) {
        this.simple = simple;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getQrcode() {
        return qrcode;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode;
    }

    public String getPos() {
        return pos;
    }

    public void setPos(String pos) {
        this.pos = pos;
    }

    public String getMaya() {
        return maya;
    }

    public void setMaya(String maya) {
        this.maya = maya;
    }

    public String getPosplus() {
        return posplus;
    }

    public void setPosplus(String posplus) {
        this.posplus = posplus;
    }

    public String getPcplugin() {
        return pcplugin;
    }

    public void setPcplugin(String pcplugin) {
        this.pcplugin = pcplugin;
    }

    public String getFaceScan() {
        return faceScan;
    }

    public void setFaceScan(String faceScan) {
        this.faceScan = faceScan;
    }

    public String getNotContainTerminals() {
        return notContainTerminals;
    }

    public void setNotContainTerminals(String notContainTerminals) {
        this.notContainTerminals = notContainTerminals;
    }

    public String getGroupBys() {
        return groupBys;
    }

    public void setGroupBys(String groupBys) {
        this.groupBys = groupBys;
    }

    public boolean getIsStoreAccount() {
        return isStoreAccount;
    }

    public void setIsStoreAccount(boolean storeAccount) {
        isStoreAccount = storeAccount;
    }

    public boolean getIsSummary() {
        return isSummary;
    }

    public void setIsSummary(boolean summary) {
        isSummary = summary;
    }

    public boolean getNeedTodayDefault() {
        return needTodayDefault;
    }

    public void setNeedTodayDefault(boolean needTodayDefault) {
        this.needTodayDefault = needTodayDefault;
    }


    public int getUpayQueryType() {
        return upayQueryType;
    }

    public void setUpayQueryType(int upayQueryType) {
        this.upayQueryType = upayQueryType;
    }


    public String getSocode() {
        return socode;
    }

    public void setSocode(String socode) {
        this.socode = socode;
    }

    public String getNpos2() {
        return npos2;
    }

    public void setNpos2(String npos2) {
        this.npos2 = npos2;
    }

    public String getGroupmeal() {
        return groupmeal;
    }

    public void setGroupmeal(String groupmeal) {
        this.groupmeal = groupmeal;
    }


    public String getProductFlag() {
        return productFlag;
    }

    public void setProductFlag(String productFlag) {
        this.productFlag = productFlag;
    }

    public boolean getValidTimeSpan() {
        return validTimeSpan;
    }

    public void setValidTimeSpan(boolean validTimeSpan) {
        this.validTimeSpan = validTimeSpan;
    }


    public String getOrderSns() {
        return orderSns;
    }

    public void setOrderSns(String orderSns) {
        this.orderSns = orderSns;
    }

    public String getTransactionSns() {
        return transactionSns;
    }

    public void setTransactionSns(String transactionSns) {
        this.transactionSns = transactionSns;
    }


    public int getOffsetHour() {
        return offsetHour;
    }

    public void setOffsetHour(int offsetHour) {
        this.offsetHour = offsetHour;
    }

    public String getMerchantIds() {
        return merchantIds;
    }

    public void setMerchantIds(String merchantIds) {
        this.merchantIds = merchantIds;
    }

    public String getMerchant_user_id() {
        return merchant_user_id;
    }

    public void setMerchant_user_id(String merchant_user_id) {
        this.merchant_user_id = merchant_user_id;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getGroup_user_id() {
        return group_user_id;
    }

    public void setGroup_user_id(String group_user_id) {
        this.group_user_id = group_user_id;
    }

    public String getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(String departmentIds) {
        this.departmentIds = departmentIds;
    }

    public String getCashDesk() {
        return cashDesk;
    }

    public void setCashDesk(String cashDesk) {
        this.cashDesk = cashDesk;
    }

    public String getSummaryExts() {
        return summaryExts;
    }

    public void setSummaryExt(String summaryExts) {
        this.summaryExts = summaryExts;
    }

    public static void commonParam(AccountRecordParam param) {
        if (Objects.isNull(param)) {
            throw ErrorMessageEnum.MISSING_PARAM.getBizException();
        }

        if (param.getToken() == null || param.getToken().isEmpty()) {
            throw ErrorMessageEnum.MISSING_TOKEN.getBizException();
        }

        if ((param.getMerchant_id() == null || param.getMerchant_id().isEmpty()) 
                && (param.getMerchantIds() == null || param.getMerchantIds().isEmpty())) {
            throw ErrorMessageEnum.MISSING_MERCHANT_ID.getBizException();
        }

        if (param.getPageSize() != null && param.getPageSize() > PageInfoConst.PAGE_SIZE_LIMIT) {
            throw ErrorMessageEnum.PAGE_SIZE_TOO_LARGE.getBizException();
        }

        if (param.getValidTimeSpan() && !Objects.isNull(param.getStartTime()) && !Objects.isNull(param.getEndTime())) {
            if (param.getEndTime().getTime() - param.getStartTime().getTime() > MAX_TIME_INTERVAL) {
                throw ErrorMessageEnum.ILLEGAL_TIME_SPAN.getBizException();
            }
        }

        if (!Objects.isNull(param.getMinTotalAmount()) && !Objects.isNull(param.getMaxTotalAmount())) {
            if (param.getMinTotalAmount() > param.getMaxTotalAmount()) {
                throw ErrorMessageEnum.ILLEGAL_AMOUNT_SPAN.getBizException();
            }
        }

        if (param.getOrderBy() != null) {
            // 判断是否支持排序的类型
            if (!AccountBookOrderByType.TYPES.contains(param.getOrderBy())) {
                throw ErrorMessageEnum.UNSUPPORTED_ACCOUNT_BOOK_SORT_TYPE.getBizException();
            }

            // 有排序时，时间不能为空
            if (param.getStartTime() == null || param.getEndTime() == null) {
                throw ErrorMessageEnum.START_AND_END_TIME_CANNOT_BE_NULL.getBizException();
            }
            // 有排序时，后端需要严格控制筛选的时间范围3个月以内
            if (param.getOrderBy() != AccountBookOrderByType.CTIME_ASC && param.getOrderBy() != AccountBookOrderByType.CTIME_DESC) {
                long timeSpan = param.getEndTime().getTime() - param.getStartTime().getTime();
                if (timeSpan < 0 || timeSpan > MAX_TIME_INTERVAL) {
                    throw ErrorMessageEnum.TIME_SPAN_CANNOT_EXCEED_3_MONTHS.getBizException();
                }
            }
        }
    }

    @Override
    public String toString() {
        return "AccountRecordParam{" +
                "storeIds='" + storeIds + '\'' +
                ", tradeStatus='" + tradeStatus + '\'' +
                ", tradeType='" + tradeType + '\'' +
                ", terminals='" + terminals + '\'' +
                ", notContainTerminals='" + notContainTerminals + '\'' +
                ", version='" + version + '\'' +
                ", merchant_id='" + merchant_id + '\'' +
                ", group_id='" + group_id + '\'' +
                ", group_user_id='" + group_user_id + '\'' +
                ", transactionSn='" + transactionSn + '\'' +
                ", simple=" + simple +
                ", token='" + token + '\'' +
                ", mobile='" + mobile + '\'' +
                ", qrcode='" + qrcode + '\'' +
                ", pos='" + pos + '\'' +
                ", maya='" + maya + '\'' +
                ", posplus='" + posplus + '\'' +
                ", pcplugin='" + pcplugin + '\'' +
                ", faceScan='" + faceScan + '\'' +
                ", groupBys='" + groupBys + '\'' +
                ", upayQueryType=" + upayQueryType +
                ", isStoreAccount=" + isStoreAccount +
                ", isSummary=" + isSummary +
                ", socode='" + socode + '\'' +
                ", npos2.0='" + npos2 + '\'' +
                ", groupmeal='" + groupmeal + '\'' +
                ", productFlag='" + productFlag + '\'' +
                ", merchantIds='" + merchantIds + '\'' +
                ", departmentIds='" + departmentIds + '\'' +
                ", cashDeskIds='" + cashDesk + '\'' +
                ", needTodayDefault=" + needTodayDefault +
                ", validTimeSpan=" + validTimeSpan +
                ", orderSns='" + orderSns + '\'' +
                ", offsetHour=" + offsetHour +
                ", summaryExts=" + summaryExts +
                "} " + super.toString();
    }
}
