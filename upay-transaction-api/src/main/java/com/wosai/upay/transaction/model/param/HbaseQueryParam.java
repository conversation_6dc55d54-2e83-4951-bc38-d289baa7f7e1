package com.wosai.upay.transaction.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HbaseQueryParam {

    private List<String> cashDeskIds;

    private boolean queryCashDesk;

    private Long startTime;

    private Long endTime;

    private Integer limit;

    /**
     * 0 表示降序 1表示升序
     */
    int []orders;

    private List<Integer> notStatusList;
}
