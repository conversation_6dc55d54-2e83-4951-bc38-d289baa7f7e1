package com.wosai.upay.transaction.model.param;

import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.transaction.annotation.DBSelectField;
import com.wosai.upay.transaction.enums.UpayQueryType;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * GetTransactionDetailByIdParam
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GetTransactionDetailByIdParam {

    /**
     * 商户id
     */
    private String merchantId;


    /**
     * 集团账号 查询流水明细商户id
     */
    private String merchantIdForGroupQuery;

    /**
     * 交易创建时间戳
     */
    @NotNull(message = "ctime 不能为空")
    private Long ctime;

    /**
     * 交易id
     */
    @NotEmpty(message = "transactionId 不能为空")
    private String transactionId;

    /**
     * 对于正向收款交易，是否需要查询有关退款信息（不限于退款列表，比如分账退款等）
     * <br>
     * true 表示是，反之表示否，默认为 true
     */
    private Boolean needRefundInfo;

    /**
     * 查询类型，{@link UpayQueryType} 0表示扫码交易，1表示刷卡交易
     */
    @DBSelectField
    private Integer upayQueryType;

}
