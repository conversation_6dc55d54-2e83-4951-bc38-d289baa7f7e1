package com.wosai.upay.transaction.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.common.exception.CommonException;
import com.wosai.upay.transaction.constant.PageInfoConst;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;
import com.wosai.upay.transaction.exception.BizException;

public class ChangeShiftsParam{
    public static final int ORDER_BY_CTIME_DESC = 1;
    public static final int ORDER_BY_CTIME_ASC = 2;
    public static final int ORDER_BY_END_DATE_DESC = 3;
    public static final int ORDER_BY_END_DATE_ASC = 4;

    public ChangeShiftsParam() {
    }

    /**
     * 终端号
     */
    @JsonProperty("terminal_sn")
    private String terminalSn;

    /**
     * 收银台id
     */
    @JsonProperty("cash_desk_id")
    private String cashDeskId;

    /**
     * 收银台id
     */
    @JsonProperty("cs_store_id")
    private String csStoreId;

    /**
     * 批次号
     */
    @JsonProperty("batch_sn")
    private String batchSn;

    /**
     * 使用指定批次号查询
     */
    @JsonProperty("use_batch_sn")
    private Boolean useBatchSn;

    /**
     * 开始时间
     */
    @JsonProperty("start_date")
    private Long startDate;

    /**
     * 结束时间
     */
    @JsonProperty("end_date")
    private Long endDate;

    /**
     * 分页查询页码，从 1 开始
     */
    private Integer page;

    /**
     * 每页笔数
     */
    @JsonProperty("page_size")
    private Integer pageSize;

    /**
     * 排序类型：1表示按金额降序、2表示按金额升序、3表示按时间降序、4表示按时间升序
     */
    @JsonProperty("order_by")
    private Integer orderBy;

    /**
     * 是否返回未签退批次
     */
    @JsonProperty("return_uncheckout")
    private Boolean returnUnCheckout;

    /**
     * 是否接入收银台
     */
    @JsonProperty("access_cash_desk")
    private boolean accessCashDesk = false;

    /**
     * 汇总扩展信息
     */
    @JsonProperty("summary_exts")
    private String summaryExts;

    public String getCashDeskId() {
        return cashDeskId;
    }

    public void setCashDeskId(String cashDeskId) {
        this.cashDeskId = cashDeskId;
    }

    public String getBatchSn() {
        return batchSn;
    }

    public void setBatchSn(String batchSn) {
        this.batchSn = batchSn;
    }

    public String getTerminalSn() {
        return terminalSn;
    }

    public void setTerminalSn(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public Boolean getUseBatchSn() {
        return useBatchSn;
    }

    public void setUseBatchSn(Boolean useBatchSn) {
        this.useBatchSn = useBatchSn;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(Integer orderBy) {
        this.orderBy = orderBy;
    }

    public String getCsStoreId() {
        return csStoreId;
    }

    public void setCsStoreId(String csStoreId) {
        this.csStoreId = csStoreId;
    }

    public Boolean getReturnUnCheckout() {
        return returnUnCheckout;
    }

    public void setReturnUnCheckout(Boolean returnUnCheckout) {
        this.returnUnCheckout = returnUnCheckout;
    }

    public boolean isAccessCashDesk() {
        return accessCashDesk;
    }

    public void setAccessCashDesk(boolean accessCashDesk) {
        this.accessCashDesk = accessCashDesk;
    }

    public String getSummaryExts() {
        return summaryExts;
    }

    public void setSummaryExts(String summaryExts) {
        this.summaryExts = summaryExts;
    }

    public static void commonParam(ChangeShiftsParam changeShiftsParam) {
        if (changeShiftsParam == null) {
            throw ErrorMessageEnum.MISSING_PARAM.getBizException();
        }
        if (changeShiftsParam.getPageSize() != null && changeShiftsParam.getPageSize() > PageInfoConst.PAGE_SIZE_LIMIT) {
            throw ErrorMessageEnum.PAGE_SIZE_TOO_LARGE.getBizException();
        }
        if (changeShiftsParam.getTerminalSn() == null && changeShiftsParam.getCashDeskId() == null && changeShiftsParam.getCsStoreId() == null) {
            throw new BizException(CommonException.CODE_INVALID_PARAMETER, "终端、收银台或门店id不能同时为空");
        }
        if (changeShiftsParam.getOrderBy() != null 
                && changeShiftsParam.getOrderBy() != ORDER_BY_CTIME_DESC
                && changeShiftsParam.getOrderBy() != ORDER_BY_CTIME_ASC) {
            throw new BizException(CommonException.CODE_INVALID_PARAMETER, "排序方式错误");
        }
    }



}
