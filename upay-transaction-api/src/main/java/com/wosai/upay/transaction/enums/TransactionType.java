package com.wosai.upay.transaction.enums;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public enum TransactionType implements BaseEnum<Integer, String> {

    /**
     * 收款
     */
    PAYMENT(30, "收款"),

    /**
     * 退款撤销
     */
    REFUND_REVOKE(31, "退款撤销"),

    /**
     * 退款
     */
    REFUND(11, "退款"),

    /**
     * 预授权完成撤销
     */
    DEPOSIT_CONSUME_CANCEL(14, "预授权完成撤销"),

    /**
     * 撤单
     */
    CANCEL(10, "撤单"),

    //--------------------------------------------------

    /**
     * 预授权
     */
    DEPOSIT_FREEZE(32, "预授权"),

    /**
     * 预授权撤销
     */
    DEPOSIT_CANCEL(12, "预授权撤销"),

    //收款
    DEPOSIT_CONSUME(13, "预授权完成"),

    CHARGE(15, "记账"),
    CHARGE_REFUND(21, "记账退款"),
    ORDER_TAKE(16, "点单外卖"),
    ORDER_TAKE_REFUND(22, "点单外卖退款"),
    STORE_PAY(17, "储值核销"),
    STORE_REFUND(20, "储值核销退款"),
    STORE_IN(18, "储值充值"),
    IN_REFUND(19, "储值充值退款");



    /**
     * 收款类型
     * </p>
     * {@see https://confluence.wosai-inc.com/pages/viewpage.action?pageId=1182225}
     */
    public static Set<Integer> PAYMENT_TYPES = new HashSet<>();

    static {
//        13, 30,15,16,17,18
        PAYMENT_TYPES.add(TransactionType.DEPOSIT_CONSUME.getCode());
        PAYMENT_TYPES.add(TransactionType.PAYMENT.getCode());
        PAYMENT_TYPES.add(TransactionType.CHARGE.getCode());
        PAYMENT_TYPES.add(TransactionType.ORDER_TAKE.getCode());
        PAYMENT_TYPES.add(TransactionType.STORE_PAY.getCode());
        PAYMENT_TYPES.add(TransactionType.STORE_IN.getCode());
    }

    /**
     * 退款类型
     * </p>
     * {@see https://confluence.wosai-inc.com/pages/viewpage.action?pageId=1182225}
     */
    public static Set<Integer> REFUND_TYPES = new HashSet<>();

    static {
        REFUND_TYPES.add(TransactionType.CANCEL.getCode());
        REFUND_TYPES.add(TransactionType.REFUND.getCode());
        REFUND_TYPES.add(TransactionType.DEPOSIT_CONSUME_CANCEL.getCode());
        REFUND_TYPES.add(TransactionType.CHARGE_REFUND.getCode());
        REFUND_TYPES.add(TransactionType.ORDER_TAKE_REFUND.getCode());
        REFUND_TYPES.add(TransactionType.STORE_REFUND.getCode());
        REFUND_TYPES.add(TransactionType.IN_REFUND.getCode());
    }


    private int code;

    private String desc;

    private TransactionType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
