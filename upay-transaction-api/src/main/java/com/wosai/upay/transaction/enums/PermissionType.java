package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum PermissionType implements BaseEnum<Integer, String> {


    /**
     * app流水账本数据权限
     */
    APP_TRANS(1, "app流水账本数据权限"),

    /**
     * app报表数据权限
     */
    APP_REPORT(2, "app报表数据权限"),

    /**
     * msp流水账本数据权限
     */
    MSP_TRANS(3, "msp流水账本数据权限"),

    /**
     * msp报表数据权限
     */
    MSP_REPORT(4, "msp报表数据权限");

    private int code;

    private String desc;

    PermissionType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
