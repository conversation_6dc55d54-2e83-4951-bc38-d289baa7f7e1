package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * crm 专用
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/3/21.
 */

@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "rpc/crm")
public interface CrmService {

    ListResult getTransactionList(@NotNull(message = "分页参数信息不能为空") PageInfo pageInfo, Map<String, Object> queryFilter);

}
