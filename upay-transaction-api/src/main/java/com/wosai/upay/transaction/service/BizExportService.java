package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/11/8 5:28 下午
 */
@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/biz_export")
public interface BizExportService {

    /**
     * date_start
     * date_end
     * merchant_id
     * survey_store_id 为空 默认此商户下所有门店
     * email
     *
     * @param param
     */
    void survey(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}不可为空"),
            @PropNotEmpty(value = "date_start", message = "{value}不可为空"),
            @PropNotEmpty(value = "date_end", message = "{value}不可为空"),
            @PropNotEmpty(value = "email", message = "{value}不可为空"),
    }) Map<String, Object> param);
}
