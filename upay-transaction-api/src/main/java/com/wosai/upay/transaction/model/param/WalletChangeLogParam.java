package com.wosai.upay.transaction.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletChangeLogParam {

    /**
     * 商户 id
     */
    private String merchantId;

    /**
     * 类型列表
     * <p/>
     * <a href="https://confluence.wosai-inc.com/pages/viewpage.action?pageId=240812269">总资产通用账本<a/>
     */
    private List<Integer> types;

    /**
     * 起始时间戳（毫秒）（包含）
     */
    private Long startTime;

    /**
     * 截止时间戳（毫秒）（不含）
     */
    private Long endTime;

    /**
     * 排序列表，依次按序 0表示降序 1表示升序
     */
    private int[] orders;


    /**
     * 限制最大条目数（rows）
     */
    private Integer limit;

}
