package com.wosai.upay.transaction.model.param;

import lombok.Data;
import lombok.experimental.Accessors;

/***
 * @ClassName: TransactionDetailParam
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/2/28 4:48 PM
 */
@Data
@Accessors(chain = true)
public class TransactionDetailParam {

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 集团账号 查询流水明细商户id
     */
    private String merchantIdForGroupQuery;

    /**
     * 交易订单号
     */
    private String transactionSn;

    /**
     * 支付通道流水号
     */
    private String tradeNo;

    /**
     * 对于正向收款交易，是否需要查询有关退款信息（不限于退款列表，比如分账退款等）
     * <br>
     * true 表示是，反之表示否，默认为 true
     */
    private Boolean needRefundInfo;
    /**
     * 预授权扫码查询
     */
    private Boolean depositScan;

}
