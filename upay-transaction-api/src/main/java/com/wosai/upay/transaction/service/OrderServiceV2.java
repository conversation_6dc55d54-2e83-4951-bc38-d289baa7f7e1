package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;

import java.util.Map;

/**
 * OrderServiceOpen
 *
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/order-v2")
public interface OrderServiceV2 {

    /**
     * 获取订单信息
     * <br/>
     * 用户替换 app-api-gateway：/V2/AccountBookV2/getRecordForDetail 接口
     *
     * @param params 请求参数
     * @return 订单信息
     */
    Map<String, Object> getOrder(Map<String, Object> params);

}
