package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;

@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/check")
public interface CheckService {

    /**
     * 功能检查
     */
    public void check();

    /**
     * AccoutBookService 功能检查
     */
    public void checkAccountBook();

    /**
     * AccoutBookOpenService 功能检查
     */
    public void checkAccountBookOpen();

    /**
     * GatewaySupport 功能检查
     */
    public void checkGatewaySupport();
}
