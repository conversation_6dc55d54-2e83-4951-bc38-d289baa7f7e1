package com.wosai.upay.transaction.enums;

import com.wosai.upay.transaction.model.StatementObjectConfig;

import java.util.Set;

public enum TransactionCategory implements BaseEnum<Integer, String> {

    UNKNOWN(0, "未知"),
    UPAY_GATEWAY(1, "收银"),
    STORE_GATEWAY(2, "储值收银"), //储值充值、储值充值退款
    CHARGING_GATEWAY(3, "记账"),
    STORE_IN(4, "会员储值"); // 储值核销、 储值核销退款


    private int code;

    private String desc;

    private TransactionCategory(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public static TransactionCategory getByPayWayAndProductFlag(Integer payway, String productFlag, Set<Integer> chargePayWayCodes) {
        if (productFlag != null && productFlag.contains("aa")) {
            return TransactionCategory.STORE_GATEWAY;
        }
        if (payway < 99 || payway == 101 || payway == 111) {
            return TransactionCategory.UPAY_GATEWAY;
        }
        if (chargePayWayCodes.contains(payway)) {
            return TransactionCategory.CHARGING_GATEWAY;
        }
        if (payway == StatementObjectConfig.STORE) {
            return TransactionCategory.STORE_IN;
        }

        return UNKNOWN;
    }

}
