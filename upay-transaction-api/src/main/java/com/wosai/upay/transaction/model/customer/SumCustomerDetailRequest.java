package com.wosai.upay.transaction.model.customer;

import com.wosai.upay.common.validation.NotEmpty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * CustomerDetailRequest
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@ToString
@Data
public class SumCustomerDetailRequest {

    /**
     * 门店id
     */
    @NotEmpty(message = "门店id不能为空")
    private String storeId;

    /**
     * 支付方式
     */
    @NotNull(message = "支付方式不能为空")
    private Integer payWay;

    /**
     * 顾客id
     */
    @NotEmpty(message = "顾客id不能为空")
    private String buyerUid;

    /**
     * 起始时间戳
     */
    @NotNull(message = "起始时间不能为空")
    private Long startTime;

    /**
     * 截止时间戳
     */
    @NotNull(message = "截止时间不能为空")
    private Long endTime;

}
