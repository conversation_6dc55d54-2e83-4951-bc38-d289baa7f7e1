package com.wosai.upay.transaction.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExportWalletChangeLogVO {

    private String id;

    private Integer type;

    private Long amount;

    private Integer sign;

    private Long balance;

    private String remark;

    private Map<String, Object> detail;

    private String merchantId;

    private String actionId;

    private Long ctime;

    private Long mtime;

    private Long version;

    private String name;

}
