package com.wosai.upay.transaction.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionParam {

    private int upayQueryType;

    private String merchantId;

    private long nextStart;

    private long start;

    private long end;

    private String taskLogId;

    private List<String> storeIds;

    private String terminalId;

    private List<Integer> statementTypes;

    private List<String> edgeRecordIds;

    private int limit;

}
