package com.wosai.upay.transaction.enums;

import com.wosai.upay.transaction.model.StatementTaskLog;

/**
 * 对账单的样式类型类型，通过include区分
 *
 * <AUTHOR>
 */
public enum StatementTaskStyle implements BaseEnum<Integer, String> {

    /**
     * 经典账单（）
     */
    CLASSIC(1, "经典账单"),

    /**
     * 储值类的账单
     */
    STORE_IN(2, "储值类"),

    /**
     * 混合类的账单
     */
    MIX(3, "混合类");


    private int code;

    private String desc;

    private StatementTaskStyle(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }


    public static StatementTaskStyle getStyleByInclude(String includes) {
        if ((StatementTaskLog.INCLUDE_STORE_IN + "").equals(includes)) {
            return StatementTaskStyle.STORE_IN;
        }
        if (!includes.contains(StatementTaskLog.INCLUDE_STORE_IN + "")) {
            return StatementTaskStyle.CLASSIC;
        }
        return StatementTaskStyle.MIX;
    }
}
