package com.wosai.upay.transaction.util;


import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 加解密工具类,只支持DESede/ECB/PKCS5Padding
 *
 */
public class CryptoUtil {

    public static final String ALGORITHM_AES = "AES";
    public static final String PADDING = "/ECB/PKCS5Padding";

    /**
     *  生成秘钥
     * @return hex key string
     */
    public static String generateSecret(){
        // 创建AES密钥生成器
        KeyGenerator keyGenerator = null;
        try {
            keyGenerator = KeyGenerator.getInstance("AES");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        keyGenerator.init(128); // 指定密钥长度为128位
        // 生成密钥
        SecretKey secretKey = keyGenerator.generateKey();
        // 获取密钥的字节数组形式
        byte[] keyBytes = secretKey.getEncoded();
        return DatatypeConverter.printHexBinary(keyBytes);
    }

    public static String encrypt(String data, String hexKeyString) {
        return encrypt(data, getAESSecretKey(hexKeyString));
    }

    public static String decrypt(String encryptedData, String hexKeyString){
        return decrypt(encryptedData, getAESSecretKey(hexKeyString));
    }


    /**
     * Encrypt a string with AES algorithm.
     *
     * @param data is a string
     * @param key
     * @return the encrypted string
     */
    public static String encrypt(String data, Key key){
        try{
            Cipher c = Cipher.getInstance(key.getAlgorithm() + PADDING);
            c.init(Cipher.ENCRYPT_MODE, key);
            byte[] encVal = c.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(encVal);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * Decrypt a string with AES algorithm.
     * @param encryptedData is a string
     * @param key
     * @return the decrypted string
     */
    public static String decrypt(String encryptedData, Key key) {
        try{
            Cipher c = Cipher.getInstance(key.getAlgorithm() + PADDING);
            c.init(Cipher.DECRYPT_MODE, key);
            byte[] decodedValue = Base64.getDecoder().decode(encryptedData);
            byte[] decValue = c.doFinal(decodedValue);
            return new String(decValue);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取aes key
     * @param hexKeyString
     * @return
     */
    public static Key getAESSecretKey(String hexKeyString){
        byte[] byteArray = DatatypeConverter.parseHexBinary(hexKeyString);
        return new SecretKeySpec(byteArray,  ALGORITHM_AES);
    }
}
