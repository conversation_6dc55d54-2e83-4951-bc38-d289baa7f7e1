package com.wosai.upay.transaction.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class QueryResultVO {
    private List<Map<String, Object>> result = new ArrayList<>();
    private long lastTime;
    private String lastId;

    public QueryResultVO() {
    }

    public QueryResultVO(List<Map<String, Object>> result, long lastTime, String lastId) {
        this.result = result;
        this.lastId = lastId;
        this.lastTime = lastTime;
    }

    public List<Map<String, Object>> getResult() {
        return result;
    }

    public void setResult(List<Map<String, Object>> result) {
        this.result = result;
    }

    public long getLastTime() {
        return lastTime;
    }

    public void setLastTime(long lastTime) {
        this.lastTime = lastTime;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }
}

