package com.wosai.upay.transaction.model.param;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 账本排序类型
 *
 * <AUTHOR>
 */
public class AccountBookOrderByType {

    /**
     * 按交易金额降序
     */
    public static final int ORIGIN_AMOUNT_DESC = 1;

    /**
     * 按交易金额升序
     */
    public static final int ORIGIN_AMOUNT_ASC = 2;

    /**
     * 按时间降序
     */
    public static final int CTIME_DESC = 3;

    /**
     * 按时间降序
     */
    public static final int CTIME_ASC = 4;

    /**
     * 类型集合
     */
    public static final Set<Integer> TYPES = new HashSet<>(Arrays.asList(ORIGIN_AMOUNT_DESC, ORIGIN_AMOUNT_ASC, CTIME_DESC, CTIME_ASC));

}
