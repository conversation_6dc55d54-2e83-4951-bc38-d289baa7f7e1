package com.wosai.upay.transaction.model.customer;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * CustomerTradeResponse
 *
 * <AUTHOR>
 */
@ToString
@Accessors(chain = true)
@Data
public class MerchantTradesResponse {

    /**
     * 交易sn
     */
    private String tsn;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 收款终端名
     */
    private String operatorName;
    /**
     * 支付方式
     */
    private Integer payWay;

    /**
     * appId
     */
    private String appId;

    /**
     * 买家/顾客 id
     */
    private String buyerUid;

    /**
     * 买家/顾客登录账号
     */
    private String buyerLogin;

    /**
     * 交易类型（收款/退款/其他）
     */
    private Integer type;

    /**
     * 流水类别
     * {@link com.wosai.upay.transaction.enums.TransactionCategory}
     */
    private Integer category;

    /**
     * 账本列表展示（收款，会员储值，美团外卖 等等等）
     */
    private String tag;

    /**
     * 交易金额
     */
    private long originalAmount;

    /**
     * 商户优惠金额
     */
    private long mchFavorableAmount;

    /**
     * 交易时间戳
     */
    private Long ctime;


}
