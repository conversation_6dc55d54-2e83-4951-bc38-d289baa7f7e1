package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.model.QueryResultVO;

import java.util.List;

@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/cross_merchant_service")
public interface CrossMerchantService {

    QueryResultVO queryResult(String merchantId, String merchantUserId, List<String> storeIds, String terminalId, Long start, Long lastTime, String lastId, Long limit);
}
