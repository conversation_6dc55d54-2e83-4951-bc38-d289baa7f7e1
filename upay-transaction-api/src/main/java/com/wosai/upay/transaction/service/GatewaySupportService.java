package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.annotation.DBSelectService;
import com.wosai.upay.transaction.constant.DataPartitionConst;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 针对网关特定订单/交易查询进行优化
 *
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "rpc/gateway-support")
public interface GatewaySupportService {

    /**
     * 根据订单 sn 获取订单信息
     *
     * @param merchantId 商户id
     * @param orderSn    订单 sn
     * @param clientSn   客户端 sn
     * @param partition  数据分区 取值范围详见 {@link DataPartitionConst}
     * @return 订单详情
     */
    Map<String, Object> getOrderBySn(String merchantId,
                                     String orderSn,
                                     String clientSn,
                                     @NotEmpty(message = "数据分区不能为空") String partition);

    /**
     * 根据订单 sn 获取订单信息
     *
     * @param merchantId 商户id
     * @param orderSn    订单 sn
     * @param clientSn   客户端 sn
     * @param date  交易发生日期
     * @return 订单详情
     */
    Map<String, Object> getOrderBySnAndDate(@NotEmpty(message = "商户id不能为空") String merchantId,
                                     @NotEmpty(message = "订单号不能为空") String orderSn,
                                     @NotEmpty(message = "交易时间不能为空") String date);

    /**
     * 通过商户订单号获取流水信息
     *
     * @param merchantId 商户 id
     * @param orderSn    订单号
     * @param clientTsn  商户订单号
     * @param ctime      订单 ctime
     * @return 交易详情
     */
    Map<String, Object> getTransactionByClientTsn(@NotEmpty(message = "商户id不能为空") String merchantId,
                                                  @NotEmpty(message = "订单号不能为空") String orderSn,
                                                  @NotEmpty(message = "商户订单sn不能为空") String clientTsn,
                                                  @NotNull(message = "交易时间戳不能为空") Long ctime);

    /**
     * 查询最后一笔流水
     *
     * @param merchantId 商户 id
     * @param orderSn    订单 sn
     * @param ctime      订单 ctime
     * @return 订单内最近一笔交易
     */
    Map<String, Object> getLatestTransactionByOrderSn(@NotEmpty(message = "商户id不能为空") String merchantId,
                                                      @NotEmpty(message = "订单sn不能为空") String orderSn,
                                                      @NotNull(message = "交易时间戳不能为空") Long ctime);

    /**
     * 查询支付和预授权完成流水
     *
     * @param merchantId 商户 id
     * @param orderSn    订单 sn
     * @param ctime      订单 ctime
     * @return 支付和预授权完成交易
     */
    Map<String, Object> getPayOrConsumerTransaction(@NotEmpty(message = "商户id不能为空") String merchantId,
                                                    @NotEmpty(message = "订单sn不能为空") String orderSn,
                                                    @NotNull(message = "交易时间戳不能为空") Long ctime);

    /**
     * 获取状态为成功的流水信息
     *
     * @param merchantId 商户id
     * @param orderSn    订单 sn
     * @param ctime      订单 ctime
     * @return 成功交易列表
     */
    List<Map<String, Object>> getSuccessTransactionList(@NotEmpty(message = "商户id不能为空") String merchantId,
                                                        @NotEmpty(message = "订单sn不能为空") String orderSn,
                                                        @NotNull(message = "交易时间戳不能为空") Long ctime);

    /**
     * 获取状态为成功的退款流水信息
     *
     * @param merchantId 商户 id
     * @param orderSn    订单 sn
     * @param ctime      订单 ctime
     * @return 成功的退款交易列表
     */
    List<Map<String, Object>> getRefundSuccessTransactionList(@NotEmpty(message = "商户id不能为空") String merchantId,
                                                              @NotEmpty(message = "订单sn不能为空") String orderSn,
                                                              @NotNull(message = "交易时间戳不能为空") Long ctime);

    /**
     * 获取所有支付宝预授权完成流水
     * 
     * 注意：此接口只返回支付宝预授权完成流水（预授权在2021.11.20之前上送的外部订单号为流水号，履约时需要将所有之前的流水都同步一遍，因为其它接口默认查询15条就返回，故单独提供此接口-未做条数限制）
     *
     * @param merchantId
     * @param orderSn
     * @param ctime
     * @return
     */
    List<Map<String,Object>> getAlipayDepositConsumeTransactionList(@NotEmpty(message = "商户id不能为空") String merchantId, 
                                                                    @NotEmpty(message = "订单sn不能为空") String orderSn, 
                                                                    @NotNull(message = "交易时间戳不能为空") Long ctime);
}

