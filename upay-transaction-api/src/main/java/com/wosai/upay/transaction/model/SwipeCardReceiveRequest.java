package com.wosai.upay.transaction.model;

import com.wosai.upay.common.validation.NotEmpty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 刷卡收款 request
 *
 * <AUTHOR>
 */
@Data
public class SwipeCardReceiveRequest {

    @Deprecated
    private String account_id;

    @NotEmpty(message = "商户id不能为空")
    private String merchant_id;

    @NotNull(message = "时间戳不能为空")
    private Long timestamp;

}
