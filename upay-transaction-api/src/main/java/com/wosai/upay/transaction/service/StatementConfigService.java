package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;

import java.util.List;
import java.util.Map;

@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/statementConfig")
public interface StatementConfigService {
    List<Map<String,Object>> getPaywayRecords();

}
