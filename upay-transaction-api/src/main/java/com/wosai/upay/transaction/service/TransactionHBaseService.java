package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.model.ExportTransactionVO;
import com.wosai.upay.transaction.model.param.HbaseQueryParam;
import com.wosai.upay.transaction.model.param.TransactionParam;

import java.util.List;
import java.util.Map;

@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/transactionHBase")
public interface TransactionHBaseService {

    ExportTransactionVO query(TransactionParam transactionParam);


    List<Map<String, Object>> queryList(HbaseQueryParam hbaseQueryParam);

}
