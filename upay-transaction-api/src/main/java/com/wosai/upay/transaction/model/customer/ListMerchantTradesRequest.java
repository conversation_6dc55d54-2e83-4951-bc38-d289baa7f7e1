package com.wosai.upay.transaction.model.customer;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * ListMerchantTradesRequest
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ToString
@Data
public class ListMerchantTradesRequest extends PageInfo {

    /**
     * 门店 id 列表
     */
    private List<String> storeIds;

    /**
     * buyerUid
     */
    private String buyerUid;

    /**
     * 支付方式
     */
    private Integer payWay;

}
