# HBase/Solr to MySQL Migration Guide

## Overview
This guide provides step-by-step instructions for migrating transaction queries from HBase/Solr to MySQL.

## Architecture Changes

### New Components
1. **MySQL DAO Layer**
   - `TransactionMySQLDao` - Handles transaction queries
   - `OrderMySQLDao` - Handles order queries
   - `WalletChangeLogMySQLDao` - Handles wallet change log queries

2. **Routing Layer**
   - `QueryRoutingService` - Determines MySQL vs HBase/Solr routing
   - `RoutingTransactionService` - Routes transaction queries
   - `RoutingOrderService` - Routes order queries
   - `RoutingWalletChangeLogService` - Routes wallet change log queries

3. **Feature Toggle System**
   - Gradual rollout support via merchant whitelist/blacklist
   - Percentage-based traffic routing
   - Runtime configuration changes

## Migration Steps

### Phase 1: Database Setup
1. **Create MySQL tables** (if not already existing)
   - `transaction` table with proper schema
   - `order` table with proper schema
   - `wallet_change_log` table with proper schema

2. **Apply performance indexes**
   ```sql
   -- Run the mysql_indexes.sql file
   mysql -u username -p database_name < sql/mysql_indexes.sql
   ```

3. **Populate data** (initial data migration)
   - Use your data pipeline to populate historical data
   - Ensure data consistency between HBase/Solr and MySQL

### Phase 2: Configuration
1. **Update application configuration**
   ```yaml
   # Add to application.yml or create application-mysql.yml
   query:
     routing:
       mysql:
         enabled: true
         percentage: 0  # Start with 0% traffic
         merchant:
           whitelist: "test_merchant_1,test_merchant_2"
   ```

2. **Database connection setup**
   ```yaml
   spring:
     datasource:
       url: ********************************************
       username: your_username
       password: your_password
   ```

### Phase 3: Gradual Rollout
1. **Start with test merchants**
   - Add test merchant IDs to whitelist
   - Verify queries work correctly
   - Monitor performance and accuracy

2. **Increase percentage gradually**
   - Increase `percentage` from 0 to 10, 25, 50, 75, 100
   - Monitor for issues at each step
   - Use merchant blacklist for problematic merchants

3. **Full rollout**
   - When confident, set `percentage: 100`
   - Remove HBase/Solr dependencies

### Phase 4: Monitoring and Validation

## Configuration Options

### Query Routing
```yaml
query.routing.mysql.enabled: false # Disable MySQL queries
query.routing.mysql.percentage: 0  # 0-100 percentage of traffic
query.routing.mysql.merchant.whitelist: "merchant1,merchant2" # Always use MySQL
query.routing.mysql.merchant.blacklist: "merchant3,merchant4" # Never use MySQL
```

### Performance Tuning
- Adjust connection pool settings in `application-mysql.yml`
- Monitor slow queries and add additional indexes as needed
- Use database partitioning for large datasets

## Testing Strategy

### Unit Tests
```bash
# Run MySQL DAO tests
mvn test -Dtest=*MySQLDaoImplTest

# Run routing service tests
mvn test -Dtest=*Routing*Test
```

### Integration Tests
1. Test with sample data in MySQL
2. Compare results between HBase/Solr and MySQL
3. Validate performance metrics
4. Test edge cases and error handling

### Load Testing
1. Use existing load testing framework
2. Compare query response times
3. Monitor database connection pool usage
4. Test with production-like data volumes

## Rollback Plan

### Immediate Rollback
1. Set `query.routing.mysql.enabled: false`
2. Restart application
3. All traffic will route back to HBase/Solr

### Gradual Rollback
1. Reduce `query.routing.mysql.percentage`
2. Add problematic merchants to blacklist
3. Monitor and adjust as needed

## Monitoring

### Key Metrics
- Query response time (MySQL vs HBase/Solr)
- Error rates
- Database connection pool usage
- Query accuracy (result comparison)

### Log Analysis
```bash
# Check routing decisions
grep "QueryRoutingService" application.log

# Check MySQL query performance
grep "TransactionMySQLDao" application.log
```

## Troubleshooting

### Common Issues
1. **Slow queries**: Add appropriate indexes
2. **Connection pool exhaustion**: Increase pool size
3. **Data inconsistency**: Verify data migration completeness
4. **Feature toggle not working**: Check configuration syntax

### Debug Mode
Enable debug logging:
```yaml
logging:
  level:
    com.wosai.upay.transaction.service.dao.mysql: DEBUG
    com.wosai.upay.transaction.service.routing: DEBUG
```

## Performance Considerations

### Index Strategy
- Primary indexes on (merchant_id, ctime)
- Composite indexes for common query patterns
- Consider partitioning by merchant_id for large datasets

### Connection Pool
- Monitor and adjust pool size based on load
- Set appropriate timeouts for production
- Use connection validation to prevent stale connections

### Query Optimization
- Use EXPLAIN to analyze query plans
- Consider covering indexes for frequently queried columns
- Monitor slow query log for optimization opportunities

## Security Considerations

### Database Security
- Use connection encryption (SSL/TLS)
- Implement proper database user permissions
- Regular security updates and patches

### Configuration Security
- Use environment variables for sensitive configuration
- Implement configuration validation
- Regular security audits of connection strings

## Support and Contact

For migration support:
- Check application logs for detailed error messages
- Use the debug routing service to verify decisions
- Contact the database team for performance optimization
- Submit issues to the development team with merchant IDs and query details