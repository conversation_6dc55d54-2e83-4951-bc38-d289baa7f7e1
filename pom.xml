<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai</groupId>
        <artifactId>common-parent</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>
    <groupId>com.wosai.upay</groupId>
    <artifactId>upay-transaction-parent</artifactId>
    <version>1.10.35</version>
    <packaging>pom</packaging>

    <properties>
        <nextgen.version>2.0-SNAPSHOT</nextgen.version>
        <spring4-boot.version>1.0-SNAPSHOT</spring4-boot.version>
        <sonar.coverage.exclusions>
            **/bean/**,
            **/common/**,
            **/enums/**,
            **/exception/**,
            **/param/**,
            **/helper/**,
            **/util/**,
            **/job/**,
            **/annotation/**,
            **/redis/**,
            **/remote/**,
            **/com/wosai/upay/transaction/rpc/**,
            **/com/wosai/upay/transaction/export/base/**,
            **/com/wosai/upay/transaction/service/*.java,
            **/com/wosai/upay/transaction/service/api/*.java,
            **/com/wosai/upay/transaction/service/dao/**,
            **/com/wosai/upay/transaction/service/service/client/impl/*.java
        </sonar.coverage.exclusions>
    </properties>

    <modules>
        <module>upay-transaction</module>
        <module>upay-transaction-api</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>spring4-boot-dependencies</artifactId>
                <version>${spring4-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.8</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
