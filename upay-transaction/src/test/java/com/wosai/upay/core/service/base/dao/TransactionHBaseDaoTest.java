package com.wosai.upay.core.service.base.dao;


import com.google.common.collect.Lists;
import com.wosai.upay.core.service.base.BaseTest;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.service.IAccountBookService;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;


public class  TransactionHBaseDaoTest extends BaseTest {

    public final Logger log = LoggerFactory.getLogger(TransactionHBaseDaoTest.class);

    @Autowired
    private TransactionHBaseDao transactionHbaseDao;


    private TransactionHBaseQuery transactionQuery;


    @Autowired
    private IAccountBookService accountBookService;

    @Before
    public void initParam(){
        DBSelectContext.getContext().initDB(UpayQueryType.UPAY.getCode());
        transactionQuery = new TransactionHBaseQuery();
        // transactionQuery.setMerchantIds(Lists.newArrayList("0680861f-e36d-11e5-801b-6c92bf21cf9b"));
        transactionQuery.setMerchantIds(Lists.newArrayList("b1a1e85d-97af-4896-b039-28ada4096658"));
        //transactionQuery.setTerminals(Lists.newArrayList("1cf816ae-2a9e-406b-ace6-7a9aadd86898"));
        transactionQuery.setTransactionSn("****************");
        //transactionQuery.setProviderIsNull(true);
//        Map<CommonStatus, StatusTypeSubPayWayQuery> statusStatusTypeSubPayWayQueryMap = transactionQuery.getStatusTypeSubPayWayQueries();
//        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
//        statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(2000));
//        statusTypeSubPayWayQuery.setSubPayWayList(Lists.newArrayList(1, 2, 3));
//        statusTypeSubPayWayQuery.setTypeList(Lists.newArrayList(10, 11, 13, 30));
//        statusStatusTypeSubPayWayQueryMap.put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
//        StatusTypeSubPayWayQuery statusTypeSubPayWayQuerys = new StatusTypeSubPayWayQuery();
//        statusTypeSubPayWayQuerys.setNotStatusList(Lists.newArrayList(0, 1001, 2000));
//        statusTypeSubPayWayQuerys.setSubPayWayList(Lists.newArrayList(1,2));
//        statusTypeSubPayWayQuerys.setTypeList(Lists.newArrayList(30,13));
//        statusStatusTypeSubPayWayQueryMap.put(CommonStatus.ERROR, statusTypeSubPayWayQuerys);
//        transactionQuery.setFilterColumns(CommonConstant.QUERY_TRANSACTION_COLUMNS_PC_DESKTOP);
        transactionQuery.setStartTime(1579139860783L);
        transactionQuery.setEndTime(1579139860983L);
        //transactionQuery.setPayWays(Lists.newArrayList(3));
//        transactionQuery.setOffset(0);
//        transactionQuery.setLimit(5000);
//        transactionQuery.getOrderBys().add(new OrderBy("ctime", OrderBy.OrderType.DESC));
//        transactionQuery.getOrderBys().add(new OrderBy("original_amount", OrderBy.OrderType.DESC));
        //transactionQuery.setTransactionSn("t789525489272136");
    }


    @Test
    public void queryListTest() {

        accountBookService.getRecordForDetail("****************","b1a1e85d-97af-4896-b039-28ada4096658");
        //logPrintResult(transactionHbaseDao.queryList(transactionQuery));
    }

    @Test
    public void countTxTest(){
        logPrintResult(transactionHbaseDao.count(transactionQuery));
    }


//    @Test
    public void summaryTxTest(){

        while (true){
            logPrintResult(transactionHbaseDao.summaryTx(transactionQuery));
            try {
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }


    @Test
    public void summaryTxGroupByPayWayTest(){
        transactionQuery.setGroupByKey("payway");
        logPrintResult(transactionHbaseDao.summaryTxGroupByKey(transactionQuery));
    }

}
