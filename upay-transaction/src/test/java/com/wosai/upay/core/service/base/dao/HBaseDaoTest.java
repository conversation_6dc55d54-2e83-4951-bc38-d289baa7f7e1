//package com.wosai.upay.core.service.base.dao;
//
//import com.wosai.upay.core.service.base.BaseTest;
//import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
//import com.wosai.upay.transaction.service.dao.base.HBaseDao;
//import com.wosai.upay.transaction.service.model.query.BaseQuery;
//import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
//import org.junit.Assert;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import com.wosai.upay.transaction.util.SolrHBaseUtils.SolrPartition;
//
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//import java.time.*;
//import java.util.List;
//
///**
// * HBaseDaoTest
// *
// * <AUTHOR>
// */
//public class HBaseDaoTest extends BaseTest {
//
//    @Autowired
//    public HBaseDao<TransactionHBaseQuery> transactionHBaseDao;
//
//    @Test
//    public void getCollections() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
//        Method getCollections = HBaseDao.class.getDeclaredMethod("getCollections", BaseQuery.class);
//        Assert.assertNotNull("单元测试失败", getCollections);
//        getCollections.setAccessible(true);
//
//        // 覆盖1.1：指定索引分区，则使用指定的索引分区 hot
//        TransactionHBaseQuery param = new TransactionHBaseQuery();
//        param.setSolrPartition(SolrPartition.hot);
//        List<String> result = (List<String>) getCollections.invoke(new TransactionHBaseDao(), param);
//        Assert.assertTrue("单元测试失败", result.size() == 1 && result.get(0).split(",").length == 4);
//        // 覆盖1.2：指定索引分区，则使用指定的索引分区 recent 6 month
//        param = new TransactionHBaseQuery();
//        param.setSolrPartition(SolrPartition.recent_6m);
//        result = (List<String>) getCollections.invoke(new TransactionHBaseDao(), param);
//        Assert.assertTrue("单元测试失败", result.size() == 1 && result.get(0).split(",").length == 7);
//        // 覆盖1.3：指定索引分区，则使用指定的索引分区 cold
//        param = new TransactionHBaseQuery();
//        param.setSolrPartition(SolrPartition.cold);
//        result = (List<String>) getCollections.invoke(new TransactionHBaseDao(), param);
//        Assert.assertTrue("单元测试失败", result.size() == 1
//                && result.get(0).split(",").length > 12
//                && result.get(0).startsWith("upay_tx_201605")
//                && result.get(0).endsWith("upay_tx_" + YearMonth.now().minusMonths(4).toString().replace("-", "")));
//        // 覆盖1.4：指定索引分区，则使用指定的索引分区 all
//        param = new TransactionHBaseQuery();
//        param.setSolrPartition(SolrPartition.all);
//        result = (List<String>) getCollections.invoke(new TransactionHBaseDao(), param);
//        Assert.assertTrue("单元测试失败", result.size() == 2
//                && result.get(0).split(",").length == 4
//                && result.get(1).split(",").length > 12
//                && result.get(1).startsWith("upay_tx_201605")
//                && result.get(1).endsWith("upay_tx_" + YearMonth.now().minusMonths(4).toString().replace("-", "")));
//
//        // 2.1默认根据根据时间计算 Collection ：startTime 为 null
//        param = new TransactionHBaseQuery();
//        param.setStartTime(null);
//        result = (List<String>) getCollections.invoke(new TransactionHBaseDao(), param);
//        Assert.assertTrue("单元测试失败", result.size() == 2
//                && result.get(0).split(",").length == 4
//                && result.get(1).split(",").length > 12
//                && result.get(1).startsWith("upay_tx_201605")
//                && result.get(1).endsWith("upay_tx_" + YearMonth.now().minusMonths(4).toString().replace("-", "")));
//        // 2.2 默认根据根据时间计算 Collection ：startTime > endTime
//        param = new TransactionHBaseQuery();
//        param.setStartTime(Instant.now().toEpochMilli());
//        param.setEndTime(Instant.now().minusSeconds(36000000).toEpochMilli());
//        result = (List<String>) getCollections.invoke(new TransactionHBaseDao(), param);
//        Assert.assertTrue("单元测试失败", result == null);
//
//        // 2.3 默认根据根据时间计算 Collection ：startTime 很小很小， endTime 很大很大
//        param = new TransactionHBaseQuery();
//        param.setStartTime(LocalDate.of(1999, 1, 1).atStartOfDay(ZoneId.of("+8")).toInstant().toEpochMilli());
//        param.setEndTime(LocalDate.now().plusMonths(3).atStartOfDay(ZoneId.of("+8")).toInstant().toEpochMilli());
//        result = (List<String>) getCollections.invoke(new TransactionHBaseDao(), param);
//        Assert.assertTrue("单元测试失败", result.size() == 1
//                && result.get(0).split(",").length > 12
//                && result.get(0).startsWith("upay_tx_201605")
//                && result.get(0).endsWith("upay_tx_" + YearMonth.now().toString().replace("-", "")));
//    }
//
//}
