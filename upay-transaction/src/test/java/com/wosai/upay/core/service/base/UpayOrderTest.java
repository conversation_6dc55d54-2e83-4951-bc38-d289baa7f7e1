package com.wosai.upay.core.service.base;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.UpayOrderService;

public class UpayOrderTest extends BaseTest {

//    public final Logger log = LoggerFactory.getLogger(getClass());
//
//    @Autowired
//    private UpayOrderService upayOrderService;
//
//    @Test
//    public void getTransactionListTest(){
//        log.info("--------------------------------------------------------------------------");
//        log.info("               start test getTransactionList                               ");
//
//        PageInfo pageInfo = new PageInfo();
//        Map queryFilter = new HashMap<>();
//        String merchantId = "";
//        String storeId = null;
//
//        log.info("step 1：:通过商户id查询");
//        log.info("req {}",  queryFilter);
//        merchantId = "affcf8d1-1338-11e6-9180-ecf4bbdede50";
//        ListResult result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 2：通过门店id查询交易");
//        log.info("req {}",  queryFilter);
//        merchantId = null;
//        storeId = "c62c410b-ea1c-e449-3381-2ec3f6177a31";
//        result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 3：通过多门店id查询交易");
//        queryFilter.clear();
//        merchantId = "affcf8d1-1338-11e6-9180-ecf4bbdede50";
//        storeId = null;
//        queryFilter.put("storeIds", Arrays.asList("3c3321c1-74fd-452c-a879-c220ef0fba72","28bb8ec9-143f-4ba0-b56c-a4d1d51b6839"));
//        log.info("req {}",  queryFilter);
//        result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 5：通过门店名称查询交易");
//        queryFilter.clear();
//        queryFilter.put("storeName", "11号门店");
//        merchantId = "affcf8d1-1338-11e6-9180-ecf4bbdede50";
//        storeId = null;
//        log.info("req {}",  queryFilter);
//        result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 6：通过订单号查询交易");
//        queryFilter.clear();
//        queryFilter.put("orderSn", "7895259211512343");
//        merchantId = "affcf8d1-1338-11e6-9180-ecf4bbdede50";
//        storeId = null;
//        log.info("req {}",  queryFilter);
//        result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 6：通过多个订单号查询交易");
//        queryFilter.clear();
//        queryFilter.put("orderSns", Arrays.asList("7895259211512343"));
//        merchantId = "affcf8d1-1338-11e6-9180-ecf4bbdede50";
//        storeId = null;
//        log.info("req {}",  queryFilter);
//        result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 7：通过流水类型状态查询交易");
//        queryFilter.clear();
//        queryFilter.put("type", Arrays.asList(Transaction.TYPE_PAYMENT, Transaction.TYPE_REFUND));
//        merchantId = "affcf8d1-1338-11e6-9180-ecf4bbdede50";
//        storeId = null;
//        log.info("req {}",  queryFilter);
//        result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 8：通过流水状态查询交易");
//        queryFilter.clear();
//        queryFilter.put("status", Arrays.asList(Transaction.STATUS_SUCCESS));
//        merchantId = "affcf8d1-1338-11e6-9180-ecf4bbdede50";
//        storeId = null;
//        log.info("req {}",  queryFilter);
//        result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("                end test getTransactionList                               ");
//        log.info("--------------------------------------------------------------------------");
//    }
//
//    @Test
//    public void getOrderListForPcDesktopTest(){
//        log.info("--------------------------------------------------------------------------");
//        log.info("               start test getOrderListForPcDesktop                               ");
//
//        PageInfo pageInfo = new PageInfo();
//        Map queryFilter = new HashMap<>();
//        String terminalSn = "";
//
//        log.info("step 1：全量查询");
//        log.info("req {}",  queryFilter);
//        ListResult result = upayOrderService.getOrderListForPcDesktop(terminalSn, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 2：通过订单号查询交易");
//        queryFilter.put("order_sn", "");
//        log.info("req {}",  queryFilter);
//        result = upayOrderService.getOrderListForPcDesktop(terminalSn, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("                end test getOrderListForPcDesktop                               ");
//        log.info("--------------------------------------------------------------------------");
//    }
//
//    @Test
//    public void getTransactionListForPcDesktopTest(){
//        log.info("--------------------------------------------------------------------------");
//        log.info("               start test getTransactionListForPcDesktop                  ");
//
//        PageInfo pageInfo = new PageInfo();
//        Map queryFilter = new HashMap<>();
//        String terminalSn = "";
//
//        log.info("step 1：全量查询");
//        log.info("req {}",  queryFilter);
//        ListResult result = upayOrderService.getTransactionListForPcDesktop(terminalSn, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("step 2：通过订单号查询交易");
//        queryFilter.put("order_sn", "");
//        log.info("req {}",  queryFilter);
//        result = upayOrderService.getTransactionListForPcDesktop(terminalSn, pageInfo, queryFilter);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("                end test getTransactionListForPcDesktop                    ");
//        log.info("--------------------------------------------------------------------------");
//    }
//
//    @Test
//    public void getOrderDetailByOrderSnTest(){
//        log.info("--------------------------------------------------------------------------");
//        log.info("               start test getOrderDetailByOrderSn                             ");
//
//        String orderSn = "7895259946468686";
//        log.info("step 1：全量查询");
//        log.info("req {}",  orderSn);
//        Map result = upayOrderService.getOrderDetailByOrderSn(orderSn);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("                end test getOrderDetailByOrderSn                            ");
//        log.info("--------------------------------------------------------------------------");
//    }
//
//    @Test
//    public void getOrderBySnTest(){
//        log.info("--------------------------------------------------------------------------");
//        log.info("               start test getOrderBySn                             ");
//
//        String merchantId = "fecee8b3-864c-4894-8d0a-35b5ab6a6aed";
//        String orderSn = "7895259946468686";
//        log.info("step 1：全量查询");
//        log.info("req {}",  orderSn);
//        Map result = upayOrderService.getOrderBySn(merchantId, orderSn, null);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("                end test getOrderBySn                            ");
//        log.info("--------------------------------------------------------------------------");
//    }
//
//    @Test
//    public void getTerminalChangeShiftsStatisticsInfoTest(){
//        log.info("--------------------------------------------------------------------------");
//        log.info("               start test getTerminalChangeShiftsStatisticsInfo                      ");
//
//        String terminalSn = "2100217560002282845";
//        String batchNo = "201909160002";
//        log.info("step 1：全量查询");
//        log.info("req terminalSn = {}, batchNo = {}",  terminalSn, batchNo);
//        Map result = upayOrderService.getTerminalChangeShiftsStatisticsInfo(terminalSn, batchNo);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("                end test getTerminalChangeShiftsStatisticsInfo            ");
//        log.info("--------------------------------------------------------------------------");
//    }
//
//    @Test
//    public void getTerminalChangeShiftsStatisticsListTest(){
//        log.info("--------------------------------------------------------------------------");
//        log.info("               start test getTerminalChangeShiftsStatisticsList                      ");
//
//        String terminalSn = "2100217560002282845";
//        String batchNo = "201909160002";
//        log.info("step 1：全量查询");
//        log.info("req terminalSn = {}, batchNo = {}",  terminalSn, batchNo);
//        ListResult result = upayOrderService.getTerminalChangeShiftsStatisticsList(terminalSn, null, null, null);
//        log.info("resp {}", WosaiJsonUtils.toJSONString(result));
//
//        log.info("                end test getTerminalChangeShiftsStatisticsList            ");
//        log.info("--------------------------------------------------------------------------");
//    }
}
