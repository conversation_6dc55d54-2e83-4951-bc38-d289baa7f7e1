package com.wosai.upay.core.service.base.dao;

import com.wosai.upay.core.service.base.BaseTest;
import com.wosai.upay.transaction.service.dao.hbase.WalletChangeLogHBaseDao;
import com.wosai.upay.transaction.service.model.po.WalletChangeLogPo;
import com.wosai.upay.transaction.service.model.query.WalletChangeLogQuery;
import junit.framework.TestCase;
import org.joda.time.LocalDate;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;

/**
 * WalletChangeLogHBaseDaoTest
 *
 * <AUTHOR>
 */
public class WalletChangeLogHBaseDaoTest extends BaseTest {

    @Autowired
    private WalletChangeLogHBaseDao walletChangeLogHBaseDao;

    @Test
    public void count() {
        WalletChangeLogQuery query = new WalletChangeLogQuery()
                .setMerchantId("8a1b24e2f25b-ddc9-ee94-33ed-dd220e31")
                .setStartTime(1593585326694L)
                .setEndTime(1593585771186L)
                .setOrderBys(WalletChangeLogQuery.OrderBy.CTIME_ASC)
                .setLimit(1000);
        List<WalletChangeLogPo> walletChangeLogPoList = walletChangeLogHBaseDao.queryForList(query);

        long count = walletChangeLogHBaseDao.count(query);

        TestCase.assertEquals("单元测试失败", walletChangeLogPoList.size(), count);
    }

    @Test
    public void queryForList() {
        WalletChangeLogQuery query = new WalletChangeLogQuery()
                .setMerchantId("8a1b24e2f25b-ddc9-ee94-33ed-dd220e31")

                .setStartTime(LocalDate.parse("2020-02-25").toDate().getTime())
                .setEndTime(LocalDate.parse("2020-10-05").toDate().getTime())
                .setOrderBys(WalletChangeLogQuery.OrderBy.CTIME_ASC)
                .setLimit(10);
        List<WalletChangeLogPo> walletChangeLogPoList = walletChangeLogHBaseDao.queryForList(query);
        Assert.notNull(walletChangeLogPoList);
    }

}
