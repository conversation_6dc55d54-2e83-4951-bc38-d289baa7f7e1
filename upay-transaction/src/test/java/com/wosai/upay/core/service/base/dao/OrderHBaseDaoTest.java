package com.wosai.upay.core.service.base.dao;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.core.service.base.BaseTest;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.service.dao.hbase.OrderHBaseDao;
import com.wosai.upay.transaction.service.model.query.OrderHBaseQuery;
import com.wosai.upay.transaction.util.SolrHBaseUtils;
import com.wosai.upay.transaction.util.SolrUtils;
import com.wosai.upay.transaction.util.Three;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;


public class OrderHBaseDaoTest  extends BaseTest {

    public final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private OrderHBaseDao orderHbaseDao;

    private OrderHBaseQuery orderHbaseQuery;

    @Before
    public void initParam(){
        DBSelectContext.getContext().initDB(UpayQueryType.UPAY.getCode());
        orderHbaseQuery = new OrderHBaseQuery();
        orderHbaseQuery.setMerchantIds(Lists.newArrayList("42d84f95-fa63-11e6-9007-ecf4bbdeffbc"));
//        orderHbaseQuery.setOrderSn("7895255729391006");
//        orderHbaseQuery.setClientSn("147450482549851510571");
        orderHbaseQuery.setOffset(0);
        orderHbaseQuery.setLimit(2);
        //orderHbaseQuery.setStartTime(1555316732669L);
        //orderHbaseQuery.setStartTime(1561363723000L);
        //orderHbaseQuery.setEndTime(1561363723100L);
        orderHbaseQuery.getOrderBys().add(new OrderBy("ctime", OrderBy.OrderType.DESC));
        orderHbaseQuery.getOrderBys().add(new OrderBy("original_total", OrderBy.OrderType.DESC));
//        orderHbaseQuery.sethBaseTimeout(20000);
//        orderHbaseQuery.setSolrTimeout(20000);
//        orderHbaseQuery.setStartTime(1464714200745L);
//        orderHbaseQuery.setEndTime(1492247745825L);

    }


    @Test
    public void putRowTest(){
        List<Three<String, Object, Class<?>>> columnValueTypeList = Lists.newArrayList();
        columnValueTypeList.add(Three.of("subject","亿生康大药房广州瑶华街店", String.class));
        columnValueTypeList.add(Three.of("original_total",800 , Long.class));
        columnValueTypeList.add(Three.of("buyer_login","xst***@163.com" , String.class));
        columnValueTypeList.add(Three.of("merchant_id","dca241e4-fd70-11e5-9180-ecf4bbdede50", String.class));
        columnValueTypeList.add(Three.of("body","亿生康大药房广州瑶华街店", String.class));
        columnValueTypeList.add(Three.of("mtime",1474504839697L, Long.class));
        columnValueTypeList.add(Three.of("client_sn","147450482549851510571", String.class));
        columnValueTypeList.add(Three.of("operator","QRCODE:16032100045060129845", String.class));
        columnValueTypeList.add(Three.of("provider",1003, Integer.class));
        columnValueTypeList.add(Three.of("ctime",1474504825612L, Long.class));
        columnValueTypeList.add(Three.of("id", "95810db9-401c-4c21-bcb6-39e43896ac2c", String.class));
        columnValueTypeList.add(Three.of("net_original", 800L, Long.class));
        columnValueTypeList.add(Three.of("store_id", "a15c0a11-b9a3-a80f-0bb4-7145c59345f8", String.class));
        columnValueTypeList.add(Three.of("payway", 2, Integer.class));
        columnValueTypeList.add(Three.of("sub_payway", 3, Integer.class));
        columnValueTypeList.add(Three.of("trade_no", "100520017600201609221082606894", String.class));
        columnValueTypeList.add(Three.of("effective_total", 800L, Long.class));
        columnValueTypeList.add(Three.of("net_effective", 800L, Long.class));
        columnValueTypeList.add(Three.of("buyer_uid", "2088202845130560", String.class));
        columnValueTypeList.add(Three.of("status", 1200, Integer.class));
        columnValueTypeList.add(Three.of("sn", "7895259205322459", String.class));
        orderHbaseDao.putRow("dca241e4-fd70-11e5-9180-ecf4bbdede50" ,1474504825612L,"95810db9-401c-4c21-bcb6-39e43896ac2c", columnValueTypeList);
    }


    @Test
    public void queryListTest() {

            logPrintResult(orderHbaseDao.queryList(orderHbaseQuery));

    }

    @Test
    public void countTest(){

        logPrintResult(orderHbaseDao.count(orderHbaseQuery));

    }


    @Test
    public void test() throws Exception{
        List<String> q = Lists.newArrayList();
        SolrQuery solrQuery = new SolrQuery("*:*");
        solrQuery.setFacet(true);
        solrQuery.addNumericRangeFacet("ctime",1569168000000L,1569254399000L,60000);
        solrQuery.setRows(0);
        q.add("ctime:[1569168000000 TO 1569254399000]");
        solrQuery.setQuery(Joiner.on(SolrUtils.SEPARATOR_AND).join(q));
        log.info("queryString ->{}", solrQuery.toQueryString());

        SolrHBaseUtils.getCloudSolrClient(500000).query("test_tx", solrQuery, SolrRequest.METHOD.POST);

    }


//    @Test
//    public void test(){
//        DateTime begin = new DateTime("2016-01-01");
//        while (begin.toString("yyyy-MM-dd").compareTo("2018-01-02") < 0){
//            orderHbaseQuery.setStartTime(begin.toDate().getTime());
//            orderHbaseQuery.setEndTime(begin.plusDays(1).withTimeAtStartOfDay().toDate().getTime());
//            logPrintResult(begin.toString("yyyy-MM-dd") +  " ~ " + orderHbaseDao.count(orderHbaseQuery));
//            begin = begin.plusDays(1);
//        }
//    }



}
