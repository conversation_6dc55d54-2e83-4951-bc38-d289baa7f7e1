package com.wosai.upay.core.service.base;

import com.wosai.common.utils.WosaiJsonUtils;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(locations = {"classpath:spring/business-config.xml", "classpath:spring/tools-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public abstract class BaseTest {

    public final Logger log = LoggerFactory.getLogger(getClass());

    public void logPrintResult(Object object){
        log.info("\n************************************************************\n"
                + WosaiJsonUtils.toJSONString(object)
                + "\n************************************************************");
    }

}
