//package com.wosai.upay;
//
//import com.wosai.mpay.api.paynet.DuitNowQRParser;
//import com.wosai.upay.transaction.util.SmtpMailSender;
//
//import java.time.LocalDate;
//
///**
// * Created by w<PERSON><PERSON><PERSON><PERSON> on 2024/8/11.
// */
//public class MainTest {
//    public static void main(String[] args) {
//        String s = "00020201021126560014A0000006150001010689023602241002240820800011619067625204000053034585802MY5911TIM TESTING6013Petaling Jaya623703169580000000631509071390000306524906304D333";
//        System.out.println(DuitNowQRParser.getFieldContentByFieldPath(s, "26.02"));;
//        System.out.println(DuitNowQRParser.parseQRCodeContent(s));;
//        System.out.println(DuitNowQRParser.parseQRCodeContent("0014A000000615000101068902360224100224082080001161906762"));;
//        System.out.println(DuitNowQRParser.parseQRCodeContent("0316958000000063150907139000030652490"));;
//
//        System.out.println(LocalDate.now().plusMonths(-1));
//
//    }
//}
