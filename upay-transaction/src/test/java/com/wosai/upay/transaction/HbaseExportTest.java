package com.wosai.upay.transaction;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.ResultScanner;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.filter.RegexStringComparator;
import org.apache.hadoop.hbase.filter.RowFilter;
import org.apache.hadoop.hbase.util.Bytes;
import org.junit.Test;
import org.powermock.reflect.Whitebox;
import org.springframework.util.CollectionUtils;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.IHbaseConstant;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.dao.hbase.OrderHBaseDao;
import com.wosai.upay.transaction.util.DateTimeUtil;
import com.wosai.upay.transaction.util.SolrHBaseUtils;
import com.wosai.upay.transaction.util.SolrUtils;
import com.wosai.upay.transaction.util.TablePool;

public class HbaseExportTest {
//    static byte[] FAMILY = Bytes.toBytes("f1");
//    static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
//
//    @Test
//    public void testHbaseExport() throws IOException {
//        Map<String, Object> properties = Whitebox.getInternalState(PropertyUtil.class, "propertiesData");
//        properties.put(CommonConstant.HBASE_ZKHOST, "hb-8vbbm6rvs2k1olp39-master1-001.hbase.zhangbei.rds.aliyuncs.com:2181,hb-8vbbm6rvs2k1olp39-master2-001.hbase.zhangbei.rds.aliyuncs.com:2181,hb-8vbbm6rvs2k1olp39-master3-001.hbase.zhangbei.rds.aliyuncs.com:2181");
//        properties.put(CommonConstant.LINDORM_USER_NAME, "root");
//        properties.put(CommonConstant.LINDORM_PASSWORD, "root");
//        int hbaseTimeout = 30000;
//        int pageSize = 100;
//        long start = DateTimeUtil.getOneDayStart(System.currentTimeMillis()) - 24 * 60 * 60 * 1000L;
//        long end = DateTimeUtil.getOneDayStart(System.currentTimeMillis()) - 1;
//        String merchantId = "1e12d53ac54c-8389-46f4-feac-ad8480af";
//        Connection connection = SolrHBaseUtils.getConnection(hbaseTimeout);
//        TablePool pool = SolrHBaseUtils.getTablePool(connection, TableName.valueOf("upay", "tx_202211"), hbaseTimeout);
//        Table table = pool.borrowObject();
//        Scan scan = new Scan();
//        // 按照商户筛选
//        scan.setStartRow(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(end)));
//        scan.setStopRow(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(start)));
////        scan.setRowPrefixFilter(Bytes.toBytes(merchantId));
//        // 数据的timestamp=ctime+version，当日23点59分数据可能存储在第二日
////        scan.setTimeRange(start, end + 100);
//        scan.setReversed(true);
//        ResultScanner resultScanner = table.getScanner(scan);
//        Result[] batchGet = null;
//        long total = 0L;
//        File localTransactionFile = File.createTempFile(UUID.randomUUID().toString(), ".data");
//        System.out.println(localTransactionFile.getPath());
//        do {
//            batchGet = resultScanner.next(pageSize);
//            for (Result rs : batchGet) {
//                Map<String, Object> transaction = convertResultToMap(rs);
//                long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
//                if (ctime > end) {
//                    continue;
//                }
//                System.out.println(format.format(new Date(ctime)) + ": " + rs);
//            }
//            total += batchGet.length;
//        } while (batchGet != null && batchGet.length == pageSize);
//        System.out.println("total: " + total);
//    }
//
//
//    @Test
//    public void testQueryOrderBySnAndDate() throws IOException {
//        Map<String, Object> properties = Whitebox.getInternalState(PropertyUtil.class, "propertiesData");
//        properties.put(CommonConstant.HBASE_ZKHOST, "hb-8vbbm6rvs2k1olp39-master1-001.hbase.zhangbei.rds.aliyuncs.com:2181,hb-8vbbm6rvs2k1olp39-master2-001.hbase.zhangbei.rds.aliyuncs.com:2181,hb-8vbbm6rvs2k1olp39-master3-001.hbase.zhangbei.rds.aliyuncs.com:2181");
//        properties.put(CommonConstant.LINDORM_USER_NAME, "root");
//        properties.put(CommonConstant.LINDORM_PASSWORD, "root");
//        int hbaseTimeout = 30000;
//        String merchantId = "1e12d53ac54c-8389-46f4-feac-ad8480af";
//        String date = "20200117";
//        Date dateTime = DateTimeUtil.parse(DateTimeUtil.DAY_FORMAT, date);
//        String queryTableName = OrderHBaseDao.TABLE_PREFIX + date.substring(0, 6);
//        long start = dateTime.getTime();
//        long end = start + DateTimeUtil.ONE_DAY_MILLIS -1;
//
//        Connection connection = SolrHBaseUtils.getConnection(hbaseTimeout);
//        TablePool pool = SolrHBaseUtils.getTablePool(connection, TableName.valueOf(queryTableName), hbaseTimeout);
//        Table table = pool.borrowObject();
//        Scan scan = new Scan();
//        // 按照商户筛选
//        scan.withStartRow(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(start)));
//        scan.withStopRow(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(end)));
//        scan.setFilter(new RowFilter(CompareOperator.EQUAL, new RegexStringComparator(".*o2000259247344538$")));
//
//
//        ResultScanner resultScanner = table.getScanner(scan);
//        Result get = resultScanner.next();
//        if (get != null) {
//            System.out.println(convertResultToMap(get));
//        }
//    }
//
//    private Map<String, Object> convertResultToMap(Result r) {
//        Map<String, Object> row = new HashMap<>();
//        if (!CollectionUtils.isEmpty(row)) {
//            row.replace(DaoConstants.ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ID)));
//            row.replace(Transaction.TSN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TSN)));
//            row.replace(Transaction.STORE_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.STORE_ID)));
//            row.replace(Transaction.ORDER_SN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ORDER_SN)));
//            row.replace(Transaction.TRADE_NO, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TRADE_NO)));
//            row.replace(Transaction.MERCHANT_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.MERCHANT_ID)));
//            row.replace(Transaction.TERMINAL_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TERMINAL_ID)));
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PROVIDER)).ifPresent(m ->
//                    row.replace(Transaction.PROVIDER, null, Bytes.toInt(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.EFFECTIVE_AMOUNT)).ifPresent(m ->
//                    row.replace(Transaction.EFFECTIVE_AMOUNT, null, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.ORIGINAL_AMOUNT)).ifPresent(m ->
//                    row.replace(Transaction.ORIGINAL_AMOUNT, null, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAID_AMOUNT)).ifPresent(m ->
//                    row.replace(Transaction.PAID_AMOUNT, null, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.RECEIVED_AMOUNT)).ifPresent(m ->
//                    row.replace(Transaction.RECEIVED_AMOUNT, null, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.STATUS)).ifPresent(m ->
//                    row.replace(Transaction.STATUS, null, Bytes.toInt(m))
//            );
//            row.replace(Transaction.BUYER_UID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_UID)));
//            row.replace(Transaction.CLIENT_TSN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.CLIENT_TSN)));
//            row.replace(Transaction.SUBJECT, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.SUBJECT)));
//            row.replace(Transaction.BODY, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BODY)));
//            row.replace(Transaction.ORDER_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ORDER_ID)));
//            row.replace(Transaction.PRODUCT_FLAG, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.PRODUCT_FLAG)));
//            row.replace(Transaction.BUYER_LOGIN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_LOGIN)));
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.TYPE)).ifPresent(m ->
//                    row.replace(Transaction.TYPE, null, Bytes.toInt(m))
//            );
//
//            row.replace(Transaction.OPERATOR, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.OPERATOR)));
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAY_WAY)).ifPresent(m ->
//                    row.replace(Transaction.PAYWAY, null, Bytes.toInt(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.SUB_PAYWAY)).ifPresent(m ->
//                    row.replace(Transaction.SUB_PAYWAY, null, Bytes.toInt(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.FINISH_TIME)).ifPresent(m ->
//                    row.replace(Transaction.FINISH_TIME, null, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CHANNEL_FINISH_TIME)).ifPresent(m ->
//                    row.replace(Transaction.CHANNEL_FINISH_TIME, null, Bytes.toLong(m))
//            );
//
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CTIME)).ifPresent(m ->
//                    row.replace(DaoConstants.CTIME, null, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.MTIME)).ifPresent(m ->
//                    row.replace(DaoConstants.MTIME, null, Bytes.toLong(m))
//            );
//            row.replace(Transaction.PROVIDER_ERROR_INFO, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.PROVIDER_ERROR_INFO)));
//            row.replace(Transaction.ITEMS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.ITEMS)));
//            row.replace(Transaction.EXTRA_PARAMS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTRA_PARAMS)));
//            row.replace(Transaction.EXTRA_OUT_FIELDS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTRA_OUT_FIELDS)));
//            row.replace(Transaction.EXTENDED_PARAMS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTENDED_PARAMS)));
//            row.replace(Transaction.CONFIG_SNAPSHOT, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.CONFIG_SNAPSHOT)));
//            row.replace(Transaction.REFLECT, null, SolrHBaseUtils.parseReflectField(r.getValue(FAMILY, IHbaseConstant.REFLECT)));
//            row.replace(Transaction.BIZ_ERROR_CODE, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.BIZ_ERROR_CODE)));
//        } else {
//            row.put(DaoConstants.ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ID)));
//            row.put(Transaction.TSN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TSN)));
//            row.put(Transaction.STORE_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.STORE_ID)));
//            row.put(Transaction.ORDER_SN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ORDER_SN)));
//            row.put(Transaction.TRADE_NO, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TRADE_NO)));
//            row.put(Transaction.MERCHANT_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.MERCHANT_ID)));
//            row.put(Transaction.TERMINAL_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TERMINAL_ID)));
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PROVIDER)).ifPresent(m ->
//                    row.put(Transaction.PROVIDER, Bytes.toInt(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.EFFECTIVE_AMOUNT)).ifPresent(m ->
//                    row.put(Transaction.EFFECTIVE_AMOUNT, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.ORIGINAL_AMOUNT)).ifPresent(m ->
//                    row.put(Transaction.ORIGINAL_AMOUNT, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAID_AMOUNT)).ifPresent(m ->
//                    row.put(Transaction.PAID_AMOUNT, Bytes.toLong(m))
//            );
//            row.put(Transaction.RECEIVED_AMOUNT, null);
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.RECEIVED_AMOUNT)).ifPresent(m ->
//                    row.put(Transaction.RECEIVED_AMOUNT, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.STATUS)).ifPresent(m ->
//                    row.put(Transaction.STATUS, Bytes.toInt(m))
//            );
//            row.put(Transaction.BUYER_UID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_UID)));
//            row.put(Transaction.CLIENT_TSN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.CLIENT_TSN)));
//            row.put(Transaction.SUBJECT, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.SUBJECT)));
//            row.put(Transaction.BODY, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BODY)));
//            row.put(Transaction.ORDER_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ORDER_ID)));
//            row.put(Transaction.PRODUCT_FLAG, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.PRODUCT_FLAG)));
//            row.put(Transaction.BUYER_LOGIN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_LOGIN)));
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.TYPE)).ifPresent(m ->
//                    row.put(Transaction.TYPE, Bytes.toInt(m))
//            );
//            row.put(Transaction.NFC_CARD, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.NFC_CARD)));
//            row.put(Transaction.OPERATOR, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.OPERATOR)));
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAY_WAY)).ifPresent(m ->
//                    row.put(Transaction.PAYWAY, Bytes.toInt(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.SUB_PAYWAY)).ifPresent(m ->
//                    row.put(Transaction.SUB_PAYWAY, Bytes.toInt(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.FINISH_TIME)).ifPresent(m ->
//                    row.put(Transaction.FINISH_TIME, Bytes.toLong(m))
//            );
//            row.put(Transaction.CHANNEL_FINISH_TIME, null);
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CHANNEL_FINISH_TIME)).ifPresent(m ->
//                    row.put(Transaction.CHANNEL_FINISH_TIME, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.VERSION)).ifPresent(m ->
//                    row.put(DaoConstants.VERSION, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CTIME)).ifPresent(m ->
//                    row.put(DaoConstants.CTIME, Bytes.toLong(m))
//            );
//            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.MTIME)).ifPresent(m ->
//                    row.put(DaoConstants.MTIME, Bytes.toLong(m))
//            );
//            if (Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.DELETED)).isPresent()) {
//                row.put(DaoConstants.DELETED, Bytes.toInt(r.getValue(FAMILY, IHbaseConstant.DELETED)) == 1);
//            } else {
//                row.put(DaoConstants.DELETED, false);
//            }
//            row.put(Transaction.PROVIDER_ERROR_INFO, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.PROVIDER_ERROR_INFO)));
//            row.put(Transaction.ITEMS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.ITEMS)));
//            row.put(Transaction.EXTRA_PARAMS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTRA_PARAMS)));
//            row.put(Transaction.EXTRA_OUT_FIELDS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTRA_OUT_FIELDS)));
//            row.put(Transaction.EXTENDED_PARAMS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTENDED_PARAMS)));
//            row.put(Transaction.CONFIG_SNAPSHOT, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.CONFIG_SNAPSHOT)));
//            row.put(Transaction.REFLECT, SolrHBaseUtils.parseReflectField(r.getValue(FAMILY, IHbaseConstant.REFLECT)));
//            row.put(Transaction.BIZ_ERROR_CODE, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.BIZ_ERROR_CODE)));
//        }
//        return row;
//    }
}
