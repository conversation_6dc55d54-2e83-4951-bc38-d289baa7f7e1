package com.wosai.upay.transaction.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.service.dao.hbase.OrderHBaseDao;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.dao.redis.RedisMapCache;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@PowerMockIgnore({"javax.management.*"})
@RunWith(PowerMockRunner.class)
public class GatewaySupportServiceTest {

    private static final String merchantId;

    private static final String orderSn;

    private static final String clientSn;

    private static final String clientTsn;

    private static final String cacheRowKeyMap;

    private static final long ctime;

    private static final String orderList;

    private static final String transactionList;

    private static final String partition;

    static {
        clientTsn = "15848155676997293-4189";
        orderSn = "2004259247731034";
        clientSn = "158493414593888031727";
        merchantId = "1e12d53ac54c-8389-46f4-feac-ad8480af";
        cacheRowKeyMap = "{\"id\":\"6239666331326662323963362d373839392d356531312d663333632d646334376438316400000170523161987432303030323539323437333135323030\",\"ctime\":1581926932888}";
        ctime = 1581926932888L;
        orderList = "[{\"subject\":\"CS费翔CS\",\"fee\":0,\"systraceno\":null,\"store_client_sn\":null,\"merchant_currency\":\"CNY\",\"merchant_sn\":\"mch-*************\",\"body\":\"CS费翔CS\",\"mtime\":*************,\"operator\":\"QRCODE:000099190228**********2421\",\"liquidation_next_day\":true,\"channel_favorable_amount\":0,\"reflect\":\"\",\"ctime\":*************,\"terminal_device_fingerprint\":\"000099190228**********2421\",\"id\":\"o****************\",\"terminal_id\":\"3e551fd78675-e0d9-c394-676f-7e5eade2\",\"terminal_client_sn\":\"**********\",\"store_sn\":\"st-****************\",\"tcp_modified\":false,\"bank_type\":null,\"net_discount\":null,\"merchant_name\":\"CS费翔CS\",\"channel_mch_top_up_favorable_amount\":0,\"version\":2,\"channel_agent_favorable_amount\":0,\"fee_rate\":\"0.38\",\"ratioTotal\":0,\"sub_payway\":3,\"wosai_favorable_amount\":0,\"terminal_name\":\"CS费翔CS的收钱音箱\",\"net_items\":null,\"trade_no\":\"**************\",\"channel_finish_time\":*************,\"items\":{\"channel_payments\":[{\"amount_total\":1,\"net_amount\":1,\"type\":\"BANKCARD_DEBIT\"}]},\"net_effective\":1,\"status\":1200,\"lakala_merc_id\":null,\"terminal_type\":\"40\",\"total_discount\":null,\"original_total\":1,\"payments\":[{\"amount\":1,\"origin_type\":\"01\",\"type\":\"BANKCARD_DEBIT\"}],\"channel_mch_favorable_amount\":0,\"buyer_login\":\"*翔\",\"actual_receive_amount\":1,\"mch_favorable_amount\":0,\"merchant_id\":\"1e12d53ac54c-8389-46f4-feac-ad8480af\",\"client_sn\":\"158493414593888031727\",\"product_flag\":\"a2\",\"provider\":1017,\"sharing_amount\":0,\"store_name\":\"CS费翔CS\",\"currency\":\"CNY\",\"pay_type\":\"未知卡类型\",\"sn\":\"****************\",\"net_original\":1,\"sharingFlag\":0,\"terminal_sn\":\"tsn-*************\",\"store_id\":\"f1d2b274054e-1f68-96c4-2d90-bcf7dbd0\",\"payway\":17,\"clearing_amount\":1,\"actual_pay_currency\":null,\"nfc_card\":null,\"operator_name\":null,\"lakala_term_id\":null,\"deleted\":false,\"batchbillno\":null,\"effective_total\":1,\"buyer_uid\":\"****************\"}]";
        transactionList = "[{\"payWay\":21,\"storeSn\":\"st-****************\",\"status\":1,\"type\":30,\"ctime\":*************,\"storeName\":\"CS七杯茶师大瑶湖方舟店CS\",\"operatorName\":\"CS七杯茶师大瑶湖方舟店CS1\",\"terminalType\":\"门店码\",\"terminalId\":\"03a3be35f75a-58eb-4f34-0fdd-e7bb8336\",\"totalFee\":2200,\"storeId\":\"00fd139567f6-4f19-f864-bb60-ccdd2ab1\",\"hongBaoWosaiMchTotal\":0,\"discountWosaiMchTotal\":0,\"wosaiFavorableAmount\":0,\"channelMchFavorableAmount\":0,\"channelAgentFavorableAmount\":0,\"channelMchTopUpFavorableAmount\":0,\"payId\":\"****************\",\"actualReceiveAmount\":2200,\"orderSn\":\"****************\",\"bankTradeNo\":\"VWa8SEnrA1c2sYXJdmRi7Z3cYsF4wuyp\",\"payWayTradeNo\":\"VWa8SEnrA1c2sYXJdmRi7Z3cYsF4wuyp\",\"transactionSn\":\"****************\",\"clearingAmount\":2200,\"tradeFeeRate\":0,\"refundList\":[],\"blendingStatus\":1,\"tradeFee\":0,\"merchantId\":\"0007ae72ce34-fea8-f894-15c1-9a0fe2f2\",\"merchantName\":\"CS七杯茶CS\",\"buyerAccount\":\"****************\",\"direct\":false,\"appAuthShopId\":\"\",\"instalmentMerchantCharge\":0,\"paidAmount\":2200,\"effectiveAmount\":2200,\"terminalSn\":\"tsn-*************\",\"sysTraceNo\":\"OqhGpYguOGHKcSwJ6dlvEnWhdiKFm8tU\",\"batchBillNo\":\"fn3Re9JdhlU0rm17EufXj38UzWlL008F\",\"channelFinishTime\":*************,\"allRefund\":false,\"sharingFlag\":0,\"outerDiscount\":0,\"woSaiMchFavorableAmount\":0,\"instalment\":false}]";
        partition = DataPartitionConst.HOT;
    }

    @InjectMocks
    private GatewaySupportServiceImpl gatewaySupportService;

    @Mock
    private RedisMapCache redisMapCache;
    @Mock
    private OrderHBaseDao orderHbaseDao;
    @Mock
    private TransactionHBaseDao transactionHBaseDao;

    @Before
    public void setUp() throws Exception {
        PowerMockito.when(redisMapCache.getAllAsyncAndRefreshTtl(Mockito.anyString(), Mockito.anySet(), Mockito.anyInt(), Mockito.any()))
                .thenReturn(JSON.parseObject(cacheRowKeyMap, new TypeReference<Map<String, Object>>() {{
                }}));

        GatewaySupportServiceImpl spy = PowerMockito.spy(gatewaySupportService);
        PowerMockito.when(spy, "getOrderOrTxFromIndexCache",
                "cacheKey", orderHbaseDao, null)
                .thenReturn(JSON.parseObject(orderList, new TypeReference<List<Map<String, Object>>>() {
        }));
        PowerMockito.when(spy, "getOrderOrTxFromIndexCache",
                "cacheKey", transactionHBaseDao, null)
                .thenReturn(JSON.parseObject(orderList, new TypeReference<List<Map<String, Object>>>() {
                }));

        PowerMockito.when(orderHbaseDao.queryList(Mockito.any()))
                .thenReturn(JSON.parseObject(orderList, new TypeReference<List<Map<String, Object>>>() {
                }));

        PowerMockito.when(orderHbaseDao.rowFilterByIds(Mockito.anyList(), Mockito.anySet(), Mockito.anyInt()))
                .thenReturn(JSON.parseObject(orderList, new TypeReference<List<Map<String, Object>>>() {
                }));

        OrderHBaseDao orderHbaseDaoMock = PowerMockito.mock(OrderHBaseDao.class);
        PowerMockito.doReturn(JSON.parseObject(orderList, new TypeReference<List<Map<String, Object>>>() {
        })).when(orderHbaseDaoMock).rowFilterByIds(Mockito.anyList(), Mockito.anySet(), Mockito.anyInt());

        PowerMockito.when(transactionHBaseDao.queryList(Mockito.any()))
                .thenReturn(JSON.parseObject(transactionList, new TypeReference<List<Map<String, Object>>>() {
                }));
        PowerMockito.when(transactionHBaseDao.rowFilterByIds(Mockito.anyList(), Mockito.anySet(), Mockito.anyInt()))
                .thenReturn(JSON.parseObject(transactionList, new TypeReference<List<Map<String, Object>>>() {
                }));
    }

    @Test
    public void getOrderBySn() {
        Map<String, Object> result = this.gatewaySupportService.getOrderBySn(merchantId, orderSn, clientSn, partition);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void getTransactionByClientTsn() {
        Map<String, Object> result = this.gatewaySupportService.getTransactionByClientTsn(merchantId, orderSn, clientTsn, ctime);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void getLatestTransactionByOrderSn() {
        Map<String, Object> result = this.gatewaySupportService.getLatestTransactionByOrderSn(merchantId, orderSn, ctime);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void getPayOrConsumerTransaction() {
        Map<String, Object> result = this.gatewaySupportService.getPayOrConsumerTransaction(merchantId, orderSn, ctime);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void getSuccessTransactionList() {
        List<Map<String, Object>> result = this.gatewaySupportService.getSuccessTransactionList(merchantId, orderSn, ctime);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void getRefundSuccessTransactionList() {
        List<Map<String, Object>> result = this.gatewaySupportService.getRefundSuccessTransactionList(merchantId, orderSn, ctime);
        Assert.assertNotNull("单元测试失败", result);
    }

}
