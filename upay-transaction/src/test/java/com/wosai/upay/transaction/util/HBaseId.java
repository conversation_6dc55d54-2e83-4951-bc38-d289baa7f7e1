package com.wosai.upay.transaction.util;

import org.apache.commons.codec.binary.Hex;
import org.apache.hadoop.hbase.util.Bytes;

import java.util.Date;

public class HBaseId {
    public static void main(String[] args) throws Exception {


        Date date = new Date(1579000945000L);

       System.out.println(date.getYear());


        System.out.println( date.getMonth());

        // get 'test_order','1e12d53ac54c-8389-46f4-feac-ad8480af\x00\x00\x01m\xDD\x9C\xDC\xFAo2002259247320976'
        //System.out.println(Bytes.toStringBinary(Hex.decodeHex("3165313264353361633534632d383338392d343666342d666561632d61643834383061660000016ddd9cdcfa6f32303032323539323437333230393736".toCharArray())));
    }
}
