package com.wosai.upay.transaction.service.service;

import com.google.common.collect.Lists;
import com.wosai.upay.transaction.model.SwipeCardReceiveRequest;
import com.wosai.upay.transaction.model.SwipeCardReceiveResponse;
import com.wosai.upay.transaction.service.model.vo.TransactionDaySumV;
import com.wosai.upay.user.api.model.MerchantUserStoreAuth;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AopServiceTest
 *
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class AopServiceTest {

    private static final String date;

    private static final List<Map> STORE_ID_MAP;

    private static final Map<String, TransactionDaySumV> STRING_TRANSACTION_DAY_SUM_V_MAP;

    static {
        date = "2020-03-02";

        STORE_ID_MAP = Lists.newArrayList(new HashMap<String, Object>() {{
            put(MerchantUserStoreAuth.STORE_ID, "1ce384ece391-7d6b-4f34-52a8-0f20bb8f");
        }});

        STRING_TRANSACTION_DAY_SUM_V_MAP = new HashMap<String, TransactionDaySumV>() {{
            put("2020-03-02", new TransactionDaySumV(date, 199, 7980989L,0L,0L));
        }};
    }

//    @Mock
//    private UserService userService;

    @Mock
    private IAccountBookBaseService accountBookBaseService;

    @InjectMocks
    private AopService aopService;

    @Before
    public void init() {
//        PowerMockito.when(userService.fetchMerchantUserStoreAuths(Mockito.anyMap()))
//                .thenReturn(STORE_ID_MAP);
        PowerMockito.when(accountBookBaseService.summaryByDay(Mockito.any(), Mockito.anySet()))
                .thenReturn(STRING_TRANSACTION_DAY_SUM_V_MAP);
    }

    @Test
    public void getSwipeCardTradeAmount() {
        SwipeCardReceiveRequest request = new SwipeCardReceiveRequest();
        request.setTimestamp(1583139139772L);
        request.setAccount_id("********-1851-4155-ab80-2f7e10f10e46");
        request.setMerchant_id("9a0c5dd3-ac86-44ad-8877-846e4c236471");
        SwipeCardReceiveResponse result = this.aopService.getSwipeCardOriginalAmount(request);

        Assert.assertNotNull("单元测试失败", result);
    }


}
