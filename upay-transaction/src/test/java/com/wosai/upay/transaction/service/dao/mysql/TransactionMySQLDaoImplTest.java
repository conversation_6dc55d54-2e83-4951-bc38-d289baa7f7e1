package com.wosai.upay.transaction.service.dao.mysql;

import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TransactionMySQLDaoImplTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private TransactionMySQLDaoImpl transactionMySQLDao;

    private TransactionHBaseQuery query;

    @Before
    public void setUp() {
        query = new TransactionHBaseQuery();
        query.setMerchantIds(Arrays.asList("merchant1", "merchant2"));
        query.setStoreIds(Arrays.asList("store1", "store2"));
        query.setStartTime(1000000L);
        query.setEndTime(2000000L);
        query.setOffset(0);
        query.setLimit(10);
    }

    @Test
    public void testQueryList_Success() {
        List<Map<String, Object>> expectedResult = new ArrayList<>();
        Map<String, Object> row = new HashMap<>();
        row.put("id", "txn123");
        row.put("tsn", "txn123");
        row.put("merchant_id", "merchant1");
        row.put("original_amount", 1000L);
        expectedResult.add(row);

        when(jdbcTemplate.query(anyString(), any(Object[].class), any(RowMapper.class)))
                .thenReturn(expectedResult);

        List<Map<String, Object>> result = transactionMySQLDao.queryList(query);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("txn123", result.get(0).get("tsn"));
        verify(jdbcTemplate, times(1)).query(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test
    public void testCount_Success() {
        when(jdbcTemplate.queryForObject(anyString(), any(Object[].class), eq(Long.class)))
                .thenReturn(100L);

        Long result = transactionMySQLDao.count(query);

        assertEquals(Long.valueOf(100L), result);
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), any(Object[].class), eq(Long.class));
    }

    @Test
    public void testSummaryTx_Success() {
        TransactionSummaryPo expected = new TransactionSummaryPo();
        expected.setPaid_amount(5000L);
        expected.setPaid_count(5L);
        expected.setRefunded_amount(1000L);
        expected.setRefunded_count(1L);
        expected.setCanceled_amount(500L);
        expected.setCanceled_count(1L);

        when(jdbcTemplate.queryForObject(anyString(), any(Object[].class), any(RowMapper.class)))
                .thenReturn(expected);

        TransactionSummaryPo result = transactionMySQLDao.summaryTx(query);

        assertNotNull(result);
//        assertEquals(5000L, result.getPaidAmount());
//        assertEquals(5L, result.getPaidCount());
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test
    public void testSummaryTxGroupByKey_Success() {
        query.setGroupByKey("store_id");

        List<TransactionSummaryPo> expected = new ArrayList<>();
        TransactionSummaryPo po1 = new TransactionSummaryPo();
        po1.setKey("store1");
        po1.setPaid_amount(3000L);
        po1.setPaid_count(3L);
        expected.add(po1);

        TransactionSummaryPo po2 = new TransactionSummaryPo();
        po2.setKey("store2");
        po2.setPaid_amount(2000L);
        po2.setPaid_count(2L);
        expected.add(po2);

        when(jdbcTemplate.query(anyString(), any(Object[].class), any(RowMapper.class)))
                .thenReturn(expected);

        List<TransactionSummaryPo> result = transactionMySQLDao.summaryTxGroupByKey(query);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("store1", result.get(0).getKey());
        assertEquals("store2", result.get(1).getKey());
        verify(jdbcTemplate, times(1)).query(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSummaryTxGroupByKey_MissingGroupByKey() {
        query.setGroupByKey(null);
        transactionMySQLDao.summaryTxGroupByKey(query);
    }

    @Test
    public void testQueryListByMemo_Success() {
        query.setTradeMemos(Arrays.asList("memo1", "memo2"));

        List<String> expected = Arrays.asList("txn1", "txn2");
        when(jdbcTemplate.queryForList(anyString(), any(Object[].class), eq(String.class)))
                .thenReturn(expected);

        List<String> result = transactionMySQLDao.queryListByMemo(query);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("txn1", result.get(0));
        assertEquals("txn2", result.get(1));
        verify(jdbcTemplate, times(1)).queryForList(anyString(), any(Object[].class), eq(String.class));
    }

    @Test
    public void testQueryBySn_Success() {
        Map<String, Object> expected = new HashMap<>();
        expected.put("tsn", "txn123");
        expected.put("merchant_id", "merchant1");

        when(jdbcTemplate.queryForObject(anyString(), any(Object[].class), any(RowMapper.class)))
                .thenReturn(expected);

        Map<String, Object> result = transactionMySQLDao.queryBySn("merchant1", "txn123", 1000000L, 2000000L);

        assertNotNull(result);
        assertEquals("txn123", result.get("tsn"));
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test
    public void testQueryBySn_NotFound() {
        when(jdbcTemplate.queryForObject(anyString(), any(Object[].class), any(RowMapper.class)))
                .thenThrow(new RuntimeException("Not found"));

        Map<String, Object> result = transactionMySQLDao.queryBySn("merchant1", "txn123", 1000000L, 2000000L);

        assertNull(result);
    }
}