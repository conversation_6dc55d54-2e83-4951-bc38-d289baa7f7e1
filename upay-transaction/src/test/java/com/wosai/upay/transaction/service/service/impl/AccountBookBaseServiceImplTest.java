package com.wosai.upay.transaction.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Sets;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionDaySumV;
import com.wosai.upay.transaction.service.model.vo.TransactionSumV;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;
import com.wosai.upay.transaction.service.service.client.IAccountStoreService;
import com.wosai.upay.transaction.service.service.common.OperatorService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> Date: 2019-09-10 Time: 11:03
 */
@RunWith(PowerMockRunner.class)
public class AccountBookBaseServiceImplTest {

    @InjectMocks
    private AccountBookBaseServiceImpl accountBookBaseService;
    @Mock
    private OperatorService operatorService;
    @Mock
    private IAccountStoreService iAccountStoreService;
    @Mock
    private TransactionHBaseDao transactionHbaseDao;

    @Before
    public void setUp() throws Exception {
        PowerMockito.when(iAccountStoreService.findStoreStaffByMerchant(Mockito.anyString()))
                .thenReturn(new HashMap<String, String>() {{}});
        PowerMockito.when(transactionHbaseDao.queryList(Mockito.any()))
                .thenReturn(JSON.parseObject(transactionListStr, new TypeReference<List<Map<String, Object>>>(){}));
        PowerMockito.doNothing().when(operatorService).setOperator(Mockito.any(), Mockito.anyMap());
//        PowerMockito.mockStatic(SpringUtil.class);
//        PowerMockito.when(SpringUtil.getBean(DataRepository.class))
//                .thenReturn(new DataRepository());
        PowerMockito.when(transactionHbaseDao.summaryTx(Mockito.any()))
                .thenReturn(JSON.parseObject(summaryTxStr, new TypeReference<TransactionSummaryPo>(){}));
        PowerMockito.when(transactionHbaseDao.summaryTxGroupByKey(Mockito.any()))
                .thenReturn(JSON.parseObject(summaryGroupByStr, new TypeReference<List<TransactionSummaryPo>>(){}));
    }

    @Test
    public void queryList() {
        List<TransactionVo> transactionVos = accountBookBaseService.queryList(buildTransactionQuery());
        Assert.assertNotNull("单测失败", transactionVos);
        Assert.assertEquals("单测失败", 4, transactionVos.size());

    }

    @Test
    public void summaryByDay() {
        Map<String, TransactionDaySumV> sumVMap = accountBookBaseService.summaryByDay(buildTransactionQuery()
                , Sets.newHashSet(new SimpleDateFormat("yyyy-MM-dd").format( new Date())));
        Assert.assertNotNull("单测失败", sumVMap);
    }

    @Test
    public void summary() {
        List<TransactionSumV> transactionSumVS = accountBookBaseService.summary(buildTransactionQuery());
        Assert.assertNotNull("单测失败", transactionSumVS);
    }

    private TransactionQuery buildTransactionQuery() {
        TransactionQuery transactionQuery = new TransactionQuery();
        transactionQuery.setMerchantId("cc06fb77-b300-11e5-9987-6c92bf21cf9b");
        return transactionQuery;
    }

    String summaryGroupByStr = "[{\"paid_count\":4,\"deposit_amount\":0,\"refunded_count\":0,\"deposit_canceled_amount\":0,\"deposit_count\":0,\"canceled_amount\":0,\"paid_amount\":4700,\"canceled_count\":0,\"key\":3,\"refunded_amount\":0}]";

    String summaryTxStr = "{\"paid_count\":4,\"deposit_amount\":0,\"refunded_count\":0,\"deposit_canceled_amount\":0,\"deposit_count\":0,\"canceled_amount\":0,\"paid_amount\":4700,\"canceled_count\":0,\"refunded_amount\":0}";

    String transactionListStr = "[{\"subject\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"received_amount\":600,\"merchant_id\":\"cc06fb77-b300-11e5-9987-6c92bf21cf9b\",\"body\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"type\":30,\"mtime\":1464713846279,\"tsn\":\"7895259245429937\",\"operator\":\"QR_WAP_PAY_WECHAT\",\"original_amount\":600,\"ctime\":1464713837702,\"id\":\"7be9f227-46b8-40d8-8ef9-68956639fbfa\",\"store_id\":\"da3db17f-aeb0-72d2-1b7e-ad7a7d8a3258\",\"client_tsn\":\"146471383760020586237\",\"provider_error_info\":{\"query\":{\"return_code\":\"SUCCESS\",\"return_msg\":\"OK\",\"result_code\":\"SUCCESS\"}},\"extra_params\":{\"payer_uid\":\"okSzXt7N8RsQ9dPq8q3fi8LEBXVY\"},\"payway\":3,\"finish_time\":1464713846274,\"sub_payway\":3,\"config_snapshot\":{\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"merchant_id\":\"cc06fb77-b300-11e5-9987-6c92bf21cf9b\",\"merchant_name\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"store_id\":\"da3db17f-aeb0-72d2-1b7e-ad7a7d8a3258\",\"store_sn\":\"180880021001200200076816\",\"store_name\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"store_city\":\"深圳市\",\"merchant_daily_max_sum_of_trans\":\"5000\",\"weixin_wap_trade_params\":{\"fee_rate\":\"0\",\"liquidation_next_day\":true,\"weixin_appid\":\"wx42f6886cbbb3fdbc\",\"weixin_sub_appid\":\"wx0fd179d3b11b7b34\",\"weixin_appkey\":\"\",\"weixin_mch_id\":\"**********\",\"weixin_sub_mch_id\":\"**********\",\"weixin_cert_config_key\":\"f71eb994-aecb-4d42-be9a-5c5605b06ba0\",\"weixin_cert_password\":\"\",\"goods_tag\":\"shenzhen\",\"active\":true},\"solicitor_id\":\"c97fbb99-b300-11e5-9987-6c92bf21cf9b\",\"merchant_sn\":\"1680000155735\",\"solicitor_sn\":\"5180000080\",\"solicitor_name\":\"收钱吧销售管理公司\",\"vendor_sn\":\"1\",\"vendor_name\":\"shouqianba\"},\"effective_amount\":600,\"paid_amount\":600L,\"trade_no\":\"4005882001201606016583968623\",\"channel_finish_time\":1464713845000,\"order_id\":\"2c2abc2f-f16c-494a-a648-0fc1f32ccb79\",\"order_sn\":\"7895259245429937\",\"buyer_uid\":\"okSzXt7N8RsQ9dPq8q3fi8LEBXVY\",\"status\":2000},{\"subject\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"received_amount\":2400,\"merchant_id\":\"cc06fb77-b300-11e5-9987-6c92bf21cf9b\",\"body\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"type\":30,\"mtime\":1464711988482,\"tsn\":\"7895259245424339\",\"operator\":\"QR_WAP_PAY_WECHAT\",\"original_amount\":2400,\"ctime\":1464711981569,\"id\":\"533df453-c31a-41a2-8eca-391a762221a6\",\"store_id\":\"da3db17f-aeb0-72d2-1b7e-ad7a7d8a3258\",\"client_tsn\":\"146471196429929269961\",\"provider_error_info\":{\"query\":{\"return_code\":\"SUCCESS\",\"return_msg\":\"OK\",\"result_code\":\"SUCCESS\"}},\"extra_params\":{\"payer_uid\":\"okSzXt7STvpG83ltVGAk3n7PJIms\"},\"payway\":3,\"finish_time\":1464711988478,\"sub_payway\":3,\"config_snapshot\":{\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"merchant_id\":\"cc06fb77-b300-11e5-9987-6c92bf21cf9b\",\"merchant_name\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"store_id\":\"da3db17f-aeb0-72d2-1b7e-ad7a7d8a3258\",\"store_sn\":\"180880021001200200076816\",\"store_name\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"store_city\":\"深圳市\",\"merchant_daily_max_sum_of_trans\":\"5000\",\"weixin_wap_trade_params\":{\"fee_rate\":\"0\",\"liquidation_next_day\":true,\"weixin_appid\":\"wx42f6886cbbb3fdbc\",\"weixin_sub_appid\":\"wx0fd179d3b11b7b34\",\"weixin_appkey\":\"\",\"weixin_mch_id\":\"**********\",\"weixin_sub_mch_id\":\"**********\",\"weixin_cert_config_key\":\"f71eb994-aecb-4d42-be9a-5c5605b06ba0\",\"weixin_cert_password\":\"\",\"goods_tag\":\"shenzhen\",\"active\":true},\"solicitor_id\":\"c97fbb99-b300-11e5-9987-6c92bf21cf9b\",\"merchant_sn\":\"1680000155735\",\"solicitor_sn\":\"5180000080\",\"solicitor_name\":\"收钱吧销售管理公司\",\"vendor_sn\":\"1\",\"vendor_name\":\"shouqianba\"},\"effective_amount\":2400,\"paid_amount\":2400L,\"trade_no\":\"4005762001201606016583641825\",\"channel_finish_time\":1464711987000,\"order_id\":\"698d4e1f-a99f-4e67-9227-c51798da7e3f\",\"order_sn\":\"7895259245424339\",\"buyer_uid\":\"okSzXt7STvpG83ltVGAk3n7PJIms\",\"status\":2000},{\"subject\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"received_amount\":200,\"merchant_id\":\"cc06fb77-b300-11e5-9987-6c92bf21cf9b\",\"body\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"type\":30,\"mtime\":1464711082344,\"tsn\":\"7895259245420486\",\"operator\":\"QR_WAP_PAY_WECHAT\",\"original_amount\":200,\"ctime\":1464711077452,\"id\":\"ad5b579d-79fc-446a-9855-9671537d599f\",\"store_id\":\"da3db17f-aeb0-72d2-1b7e-ad7a7d8a3258\",\"client_tsn\":\"146471107732048023421\",\"provider_error_info\":{\"query\":{\"return_code\":\"SUCCESS\",\"return_msg\":\"OK\",\"result_code\":\"SUCCESS\"}},\"extra_params\":{\"payer_uid\":\"okSzXt071crvc5YoCe_bYpmbbkSk\"},\"payway\":3,\"finish_time\":1464711082339,\"sub_payway\":3,\"config_snapshot\":{\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"merchant_id\":\"cc06fb77-b300-11e5-9987-6c92bf21cf9b\",\"merchant_name\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"store_id\":\"da3db17f-aeb0-72d2-1b7e-ad7a7d8a3258\",\"store_sn\":\"180880021001200200076816\",\"store_name\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"store_city\":\"深圳市\",\"merchant_daily_max_sum_of_trans\":\"5000\",\"weixin_wap_trade_params\":{\"fee_rate\":\"0\",\"liquidation_next_day\":true,\"weixin_appid\":\"wx42f6886cbbb3fdbc\",\"weixin_sub_appid\":\"wx0fd179d3b11b7b34\",\"weixin_appkey\":\"\",\"weixin_mch_id\":\"**********\",\"weixin_sub_mch_id\":\"**********\",\"weixin_cert_config_key\":\"f71eb994-aecb-4d42-be9a-5c5605b06ba0\",\"weixin_cert_password\":\"\",\"goods_tag\":\"shenzhen\",\"active\":true},\"solicitor_id\":\"c97fbb99-b300-11e5-9987-6c92bf21cf9b\",\"merchant_sn\":\"1680000155735\",\"solicitor_sn\":\"5180000080\",\"solicitor_name\":\"收钱吧销售管理公司\",\"vendor_sn\":\"1\",\"vendor_name\":\"shouqianba\"},\"effective_amount\":200,\"paid_amount\":200L,\"trade_no\":\"4006802001201606016583439531\",\"channel_finish_time\":1464711081000,\"order_id\":\"fc174cc8-58c6-4cdc-9607-9563ac3cceb0\",\"order_sn\":\"7895259245420486\",\"buyer_uid\":\"okSzXt071crvc5YoCe_bYpmbbkSk\",\"status\":2000},{\"subject\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"received_amount\":1500,\"merchant_id\":\"cc06fb77-b300-11e5-9987-6c92bf21cf9b\",\"body\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"type\":30,\"mtime\":1464711053590,\"tsn\":\"7895259245420799\",\"operator\":\"QR_WAP_PAY_WECHAT\",\"original_amount\":1500,\"ctime\":1464711047873,\"id\":\"109ea5b9-be3c-4c48-937b-c3c76ab5773d\",\"store_id\":\"da3db17f-aeb0-72d2-1b7e-ad7a7d8a3258\",\"client_tsn\":\"146471104775605752020\",\"provider_error_info\":{\"query\":{\"return_code\":\"SUCCESS\",\"return_msg\":\"OK\",\"result_code\":\"SUCCESS\"}},\"extra_params\":{\"payer_uid\":\"okSzXt071crvc5YoCe_bYpmbbkSk\"},\"payway\":3,\"finish_time\":1464711053585,\"sub_payway\":3,\"config_snapshot\":{\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"merchant_id\":\"cc06fb77-b300-11e5-9987-6c92bf21cf9b\",\"merchant_name\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"store_id\":\"da3db17f-aeb0-72d2-1b7e-ad7a7d8a3258\",\"store_sn\":\"180880021001200200076816\",\"store_name\":\"芙蓉兴盛鼎和便利店深圳布吉新村店\",\"store_city\":\"深圳市\",\"merchant_daily_max_sum_of_trans\":\"5000\",\"weixin_wap_trade_params\":{\"fee_rate\":\"0\",\"liquidation_next_day\":true,\"weixin_appid\":\"wx42f6886cbbb3fdbc\",\"weixin_sub_appid\":\"wx0fd179d3b11b7b34\",\"weixin_appkey\":\"\",\"weixin_mch_id\":\"**********\",\"weixin_sub_mch_id\":\"**********\",\"weixin_cert_config_key\":\"f71eb994-aecb-4d42-be9a-5c5605b06ba0\",\"weixin_cert_password\":\"\",\"goods_tag\":\"shenzhen\",\"active\":true},\"solicitor_id\":\"c97fbb99-b300-11e5-9987-6c92bf21cf9b\",\"merchant_sn\":\"1680000155735\",\"solicitor_sn\":\"5180000080\",\"solicitor_name\":\"收钱吧销售管理公司\",\"vendor_sn\":\"1\",\"vendor_name\":\"shouqianba\"},\"effective_amount\":1500,\"paid_amount\":1500L,\"trade_no\":\"4006802001201606016584404513\",\"channel_finish_time\":1464711053000,\"order_id\":\"c64a9721-3d6f-4675-8b78-507faf0b003e\",\"order_sn\":\"7895259245420799\",\"buyer_uid\":\"okSzXt071crvc5YoCe_bYpmbbkSk\",\"status\":2000}]";
}