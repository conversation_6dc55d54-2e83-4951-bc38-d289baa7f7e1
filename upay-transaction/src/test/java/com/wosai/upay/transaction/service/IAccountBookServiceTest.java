package com.wosai.upay.transaction.service;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.transaction.model.TAccountRecordDetail;
import com.wosai.upay.transaction.service.api.AccountBookServiceImpl;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;
import com.wosai.upay.transaction.service.service.IAccountBookBaseService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * IAccountBookServiceTest
 *
 * <AUTHOR>
 */
@PowerMockIgnore({"javax.management.*"})
@RunWith(PowerMockRunner.class)
public class IAccountBookServiceTest {

    private String merchantId = "0040a3b45204-5ceb-fdb4-c8d5-02c14674";
    private Long ctime = *************L;
    private String transactionSn = "****************";
    private String transactionId = "t****************";

    private String transactionVoText = "{\"actualReceiveAmount\":0,\"appAuthShopId\":\"\",\"blendingStatus\":0,\"channelAgentFavorableAmount\":0,\"channelMchFavorableAmount\":0,\"channelMchTopUpFavorableAmount\":0,\"clearingAmount\":0,\"ctime\":*************,\"discountWosaiMchTotal\":0,\"effectiveAmount\":15,\"extraParams\":{\"profit_sharing\":{\"sharing_flag\":\"1\",\"receivers\":[{\"id\":\"r1\",\"sharing_amount\":\"2\"},{\"id\":\"r2\",\"sharing_amount\":\"3\"}],\"sharing_pay_clearing_amount\":30}},\"failText\":\"\",\"feeRate\":0.6,\"finishTime\":0,\"hongBaoWosaiMchTotal\":0,\"instalment\":false,\"instalmentMerchantCharge\":0,\"liquidationNextDay\":false,\"mchFavorableAmount\":0,\"merchantId\":\"0040a3b45204-5ceb-fdb4-c8d5-02c14674\",\"merchantName\":\"testcase测试商户\",\"operator\":\"麻辣E族\",\"orderSn\":\"****************\",\"originalAmount\":15,\"outerDiscount\":0,\"payAccount\":\"oyBevt50TTTMcvXEQhmOrLFMLBnA\",\"payId\":\"oUlSyjgyqFH2DnGtDudzmcdboYtn8\",\"payWay\":3,\"productFlag\":\"a1,a5\",\"receivedAmount\":0,\"sharingAmount\":0,\"sharingFlag\":1,\"sqbDiscount\":0,\"status\":2109,\"storeId\":\"1ce384ece391-7d6b-4f34-52a8-0f20bb8f\",\"storeName\":\"just a test002\",\"storeSn\":\"st-****************\",\"subPayWay\":1,\"terminalId\":\"620585fcd3a9-6eeb-f274-cb95-39fad5e5\",\"terminalName\":\"麻辣E族\",\"terminalSn\":\"tsn-*************\",\"terminalType\":\"门店码\",\"tradeFee\":0,\"tsn\":\"****************\",\"type\":11,\"wosaiFavorableAmount\":0}\n";

    @InjectMocks
    private AccountBookServiceImpl accountBookService;

    @Mock
    private IAccountBookBaseService accountBookBaseService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.when(accountBookBaseService.queryObj(Mockito.any(TransactionQuery.class)))
                .thenReturn(JSON.parseObject(transactionVoText, TransactionVo.class));
    }

    @Test
    public void getRecordForDetail() {
        TAccountRecordDetail result = this.accountBookService.getRecordForDetail(transactionSn, merchantId);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void getTransactionDetail() {
        TAccountRecordDetail result = this.accountBookService.getTransactionDetail(merchantId, ctime, transactionSn);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void getTransactionDetail2() {
        TAccountRecordDetail result = this.accountBookService.getTransactionDetail(merchantId, ctime, transactionSn, true);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void getTransactionDetailById() {
        TAccountRecordDetail result = this.accountBookService.getTransactionDetail(merchantId, ctime, transactionId);
        Assert.assertNotNull("单元测试失败", result);
    }

}
