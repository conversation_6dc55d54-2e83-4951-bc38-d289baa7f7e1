package com.wosai.upay.transaction.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.transaction.service.api.OrderServiceV2Impl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * OrderServiceV2Test
 *
 * <AUTHOR>
 */
@PowerMockIgnore({"javax.management.*"})
@RunWith(PowerMockRunner.class)
public class OrderServiceV2Test {

    private static final String ORDER_INFO = "{\"subject\":\"电饱饱共享充电宝\",\"fee\":0,\"store_client_sn\":null,\"merchant_currency\":\"CNY\",\"merchant_sn\":\"21690002909106\",\"body\":null,\"mtime\":1589512242896,\"operator\":null,\"channel_favorable_amount\":0,\"reflect\":\"这是是备注\",\"ctime\":1589512213425,\"terminal_device_fingerprint\":\"unionpay alipay deposit\",\"id\":\"o7894259249556407\",\"terminal_id\":\"f6a2648b-616f-462b-8975-6aaa23e69ed3\",\"terminal_client_sn\":null,\"store_sn\":\"21590000000603250\",\"tcp_modified\":false,\"net_discount\":0,\"merchant_name\":\"银联支付宝间连预授权自动化测试专用\",\"channel_mch_top_up_favorable_amount\":39,\"version\":4,\"channel_agent_favorable_amount\":0,\"sub_payway\":4,\"wosai_favorable_amount\":0,\"terminal_name\":\"银联支付宝间连预授权自动化测试专用\",\"net_items\":null,\"trade_no\":\"2020051510002001110519774382\",\"items\":null,\"net_effective\":0,\"status\":5201,\"terminal_type\":\"30\",\"total_discount\":null,\"original_total\":9900,\"channel_mch_favorable_amount\":66,\"buyer_login\":\"lik***@163.com\",\"actual_receive_amount\":0,\"mch_favorable_amount\":56,\"merchant_id\":\"67555feb-7b5e-49d4-aa13-e541b6bb775a\",\"client_sn\":\"115895122133003142140100\",\"provider\":1016,\"sharing_amount\":0,\"store_name\":\"银联支付宝间连预授权自动化测试专用门店\",\"sn\":\"7894259249556407\",\"net_original\":100,\"terminal_sn\":\"2101000690002274166\",\"store_id\":\"e1c41063-55d9-4d4c-bd8c-70f7aa72f0cf\",\"payway\":2,\"clearing_amount\":0,\"nfc_card\":null,\"operator_name\":null,\"deleted\":false,\"effective_total\":9900,\"buyer_uid\":\"2088002463095113\"}";
    private static final String MERCHANT_INFO = "{\"name\":\"商户名称\"}";

    @InjectMocks
    private OrderServiceV2Impl orderServiceV2;

    @Mock
    private OrderService orderService;
    @Mock
    private MerchantService merchantService;

    @Before
    public void init() {
        PowerMockito.when(orderService.getOrderDetailsByMerchantIdAndOrderSn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.parseObject(ORDER_INFO, new TypeReference<Map<String, Object>>() {
                }));

        PowerMockito.when(merchantService.getMerchant(Mockito.anyString()))
                .thenReturn(JSON.parseObject(MERCHANT_INFO, new TypeReference<Map<String, Object>>() {
                }));
    }

    @Test
    public void getOrder() {
        Map<String, Object> params = new TreeMap<>();
        params.put("merchant_id", "67555feb-7b5e-49d4-aa13-e541b6bb775a");
        params.put("orderSn", "7894259249518045");
        Map<String, Object> result = this.orderServiceV2.getOrder(params);
        Assert.assertNotNull("单元测试失败", result);
    }

}
