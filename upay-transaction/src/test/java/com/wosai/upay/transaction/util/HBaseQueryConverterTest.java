package com.wosai.upay.transaction.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.enums.LoadType;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import org.junit.Assert;
import org.junit.Test;


/**
 * <AUTHOR> Date: 2019-09-10 Time: 10:36
 */
public class HBaseQueryConverterTest {

    @Test
    public void convert() {
        TransactionQuery transactionQuery = new TransactionQuery();
        transactionQuery.setStartTime(System.currentTimeMillis());
        transactionQuery.setEndTime(System.currentTimeMillis());
        transactionQuery.setBackUpTime(System.currentTimeMillis());
        transactionQuery.setOffset(0);
        transactionQuery.setGroupByKeys(Sets.newHashSet("payway"));
        transactionQuery.setStoreIdList(Lists.newArrayList("123"));
        transactionQuery.setOperatorIds(Lists.newArrayList("123"));
        transactionQuery.setMerchantId("123");
        transactionQuery.setTerminalList(Lists.newArrayList("123"));
        transactionQuery.setNotContainTerminalList(Lists.newArrayList());
        transactionQuery.setPayways(Lists.newArrayList(3));
        transactionQuery.setOrderSns(Lists.newArrayList(""));
        transactionQuery.setTransactionSn("");
        transactionQuery.setProductFlags(Lists.newArrayList(""));
        transactionQuery.setLoadTypes(Sets.newHashSet(LoadType.EXTRA_OUT_FIELDS));
        transactionQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, new StatusTypeSubPayWayQuery());

        TransactionHBaseQuery transactionHbaseQuery = HbaseQueryConverter.convert(transactionQuery);
        Assert.assertNotNull("单测失败", transactionHbaseQuery);
        Assert.assertEquals("单侧失败",new Integer(15), transactionHbaseQuery.getLimit());

        transactionQuery.setLimit(10);
        transactionQuery.setGroupByKeys(null);
        transactionHbaseQuery = HbaseQueryConverter.convert(transactionQuery);
        Assert.assertEquals("单侧失败",new Integer(10), transactionHbaseQuery.getLimit());
        Assert.assertNotNull("单测失败", transactionHbaseQuery);
        Assert.assertNull("单侧失败", transactionHbaseQuery.getGroupByKey());
    }
}