package com.wosai.upay.transaction.rpc;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.transaction.model.SwipeCardReceiveRequest;
import com.wosai.upay.transaction.model.SwipeCardReceiveResponse;
import com.wosai.upay.transaction.util.OdpsUtil;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AopRpcTest
 *
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
public class AopRpcTest {

    @Autowired
    public AopRpc aopRpc;

    @Autowired
    public OdpsUtil odpsUtil;

    @Test
    public void getSwipeCardOriginalAmount() {
        SwipeCardReceiveRequest request = new SwipeCardReceiveRequest();
        request.setAccount_id("9f2d9864-c16e-4034-9d0a-77a5b425a0e0");
        request.setMerchant_id("0007ae72ce34-fea8-f894-15c1-9a0fe2f2");
        request.setTimestamp(System.currentTimeMillis());
        SwipeCardReceiveResponse response = this.aopRpc.getSwipeCardOriginalAmount(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testOdps() {
        String sql = getSqlByName("odps/transaction.sql");
        String replace = sql.replace("{pt}", "********");
        String sqlByName = replace.replace("{trade_no}", "4200057896********3803105851");
        List<Map<String, Object>> result = odpsUtil.getResult(sqlByName);
        System.out.println(JSON.toJSONString(result));
    }

    @SneakyThrows
    public String getSqlByName(String name){
        InputStream inputStream = new ClassPathResource(name).getInputStream();
        return IOUtils.readLines(inputStream, "utf8").stream().filter(line -> !line.trim().startsWith("--")).collect(Collectors.joining(" "));
    }

}
