package com.wosai.upay.transaction.rpc;

import javax.mail.*;
import javax.mail.internet.*;
import java.util.Properties;

public class SendEmail {
    public static void main(String[] args) {
//        main0(args);
        main1(args);
    }
    public static void main0(String[] args) {
        // SMTP server information
        String host = "smtp.office365.com";
        final String username = "<EMAIL>"; // 替换为您的邮箱
        final String password = "N.576564520287ah"; // 替换为您的密码

        // Set properties
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true"); // 启用 STARTTLS
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.port", "587"); // 使用端口 587

        // Get the Session object
        Session session = Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });

        try {
            // Create a default MimeMessage object
            Message message = new MimeMessage(session);

            // Set From: header field
            message.setFrom(new InternetAddress(username));

            // Set To: header field
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse("<EMAIL>")); // 替换为收件人邮箱

            // Set Subject: header field
            message.setSubject("Test Email");

            // Now set the actual message
            message.setText("This is a test email from Java application.");

            // Send message
            Transport.send(message);
            System.out.println("Email sent successfully!");

        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }

    public static void main1(String[] args) {
        String accessToken = "N.576564520287ah"; // 从上一步获得的访问令牌

        // 设置邮件服务器属性
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.host", "smtp.office365.com");
        props.put("mail.smtp.port", "587");
        props.put("mail.smtp.starttls.enable", "true");

        Session session = Session.getInstance(props);

        try {
            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress("<EMAIL>")); // 发件人邮箱
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse("<EMAIL>")); // 收件人邮箱
            message.setSubject("Test Email with OAuth 2.0");
            message.setText("This is a test email sent using OAuth 2.0");

            // 使用访问令牌进行身份验证
            Transport transport = session.getTransport("smtp");
            transport.connect("smtp.office365.com", "<EMAIL>", accessToken);
            transport.sendMessage(message, message.getAllRecipients());
            transport.close();

            System.out.println("Email sent successfully!");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
