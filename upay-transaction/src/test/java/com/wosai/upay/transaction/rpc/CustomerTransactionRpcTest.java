package com.wosai.upay.transaction.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.wosai.upay.transaction.model.customer.ListMerchantTradesRequest;
import com.wosai.upay.transaction.model.customer.MerchantTradesResponse;
import com.wosai.upay.transaction.model.customer.SumCustomerDetailRequest;
import com.wosai.upay.transaction.model.customer.SumCustomerDetailResponse;
import com.wosai.upay.transaction.rpc.impl.CustomerTransactionRpcImpl;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.Map;

/**
 * CustomerTransactionRpcTest
 *
 * <AUTHOR>
 */
@PowerMockIgnore({"javax.management.*"})
@RunWith(PowerMockRunner.class)
public class CustomerTransactionRpcTest {

    private static SumCustomerDetailRequest request;
    private static TransactionSummaryPo summaryTxResult;

    static {
        request = JSON.parseObject("{\"buyerUid\":\"2088102122524333\",\"endTime\":1604160578953,\"startTime\":1604160578653,\"storeId\":\"f1d2b274054e-1f68-96c4-2d90-bcf7dbd0\"}", SumCustomerDetailRequest.class);
        summaryTxResult = JSON.parseObject("{\"paid_count\":1,\"deposit_amount\":0,\"refunded_count\":1,\"deposit_canceled_amount\":0,\"deposit_count\":0,\"canceled_amount\":0,\"paid_amount\":1000,\"deposit_canceled_count\":0,\"canceled_count\":0,\"key\":\"15988885620\",\"refunded_amount\":1000}", new TypeReference<TransactionSummaryPo>() {{
        }});
    }

    @InjectMocks
    private CustomerTransactionRpcImpl customerTransactionRpc;
    @Mock
    public TransactionHBaseDao transactionHBaseDao;

    @Before
    public void setUp() {
        PowerMockito.when(transactionHBaseDao.summaryTx(Mockito.any(TransactionHBaseQuery.class)))
                .thenReturn(summaryTxResult);
        PowerMockito.when(transactionHBaseDao.queryList(Mockito.any(TransactionHBaseQuery.class)))
                .thenReturn(JSON.parseObject("[{\"subject\":\"mock-union-alipay-b30ad421\",\"received_amount\":790,\"buyer_login\":\"15988885620\",\"merchant_id\":\"1e12d53ac54c-8389-46f4-feac-ad8480af\",\"type\":11,\"mtime\":1604160578809,\"tsn\":\"2001259247438552\",\"product_flag\":\"a1\",\"extra_out_fields\":{\"payments\":[{\"type\":\"DISCOUNT_CHANNEL_MCH\",\"origin_type\":\"MDISCOUNT\",\"amount\":210},{\"type\":\"DISCOUNT_CHANNEL\",\"origin_type\":\"DISCOUNT\",\"amount\":99},{\"type\":\"ALIPAY_HUABEI\",\"origin_type\":\"PCREDIT\",\"amount\":691}],\"order_info\":{\"original_total\":1000,\"effective_total\":1000,\"ctime\":1604160578653,\"trade_no\":\"13112011001004330000121536\"}},\"provider\":1016,\"original_amount\":1000,\"ctime\":1604160578797,\"id\":\"t2001259247438552\",\"terminal_id\":\"489cbfe5ce9b-ab19-3354-c28f-5d51fac0\",\"store_id\":\"f1d2b274054e-1f68-96c4-2d90-bcf7dbd0\",\"client_tsn\":\"16041605784167126-2819\",\"provider_error_info\":{\"refund\":{\"code\":\"10000\",\"msg\":\"处理成功\"}},\"extra_params\":{},\"payway\":2,\"version\":3,\"finish_time\":1604160578808,\"sub_payway\":1,\"config_snapshot\":{\"vendor_id\":\"b52600e36100-3ce9-5e11-99fa-f5f9d958\",\"merchant_id\":\"1e12d53ac54c-8389-46f4-feac-ad8480af\",\"merchant_sn\":\"mch-1680001449180\",\"merchant_name\":\"CS费翔CS\",\"merchant_country\":\"CHN\",\"currency\":\"CNY\",\"store_id\":\"f1d2b274054e-1f68-96c4-2d90-bcf7dbd0\",\"store_sn\":\"st-1580000000921013\",\"store_client_sn\":\"\",\"store_name\":\"CS费翔CS\",\"store_city\":\"市辖区\",\"terminal_id\":\"489cbfe5ce9b-ab19-3354-c28f-5d51fac0\",\"terminal_sn\":\"tsn-100000330007352422\",\"terminal_name\":\"支付组自动化回归测试银联直连终端\",\"clearance_provider\":2,\"pay_status\":1,\"across_store_refund_switch\":1,\"gen_order_sn_switch\":1,\"common_switch\":\"02222222222222222222222222222222\",\"language_case\":0,\"sharing_switch\":1,\"up_direct_trade_params\":{\"fee_rate\":\"0.41\",\"liquidation_next_day\":true,\"up_private_key\":\"743e5ea7-6536-41f7-aa08-c2421e151f39\",\"public_key\":\"44076dc4-43ac-11e9-9807-7cd30ae435b2\",\"app_id\":\"1266000048220001\",\"sys_pid\":\"2088621838709497\",\"alipay_sub_mch_id\":\"2088821866753527\",\"provider_mch_id \":\"\",\"rece_org_no\":\"36002013293\",\"cert_id\":\"**********\",\"active\":true,\"fee\":3},\"trade_app\":\"1\"},\"deleted\":false,\"effective_amount\":1000,\"paid_amount\":691,\"trade_no\":\"2013112011001004330000121536\",\"channel_finish_time\":1604160578000,\"order_id\":\"o2003259247435090\",\"order_sn\":\"2003259247435090\",\"buyer_uid\":\"2088102122524333\",\"status\":2000}]\n",
                        new TypeReference<List<Map<String, Object>>>() {{
                        }}));
    }

    @Test
    public void sumCustomerDetail() {
        String merchantId = "1e12d53ac54c-8389-46f4-feac-ad8480af";
        SumCustomerDetailResponse response = customerTransactionRpc.sumCustomerDetail(merchantId, request);
        TestCase.assertNotNull("单元测试失败", response);
        TestCase.assertEquals("单元测试失败", 2, response.getTradCount());
        TestCase.assertEquals("单元测试失败", 0L, response.getTradeAmount());
    }

    @Test
    public void listMerchantTrades() {
        String merchantId = "1e12d53ac54c-8389-46f4-feac-ad8480af";
        ListMerchantTradesRequest listMerchantTradesRequest = JSON.parseObject("{\"buyerUid\":\"2088102122524333\",\"endTime\":1604160578953,\"pageSize\":1,\"startTime\":1604160578653,\"storeId\":\"f1d2b274054e-1f68-96c4-2d90-bcf7dbd0\"}\n", ListMerchantTradesRequest.class);
        List<MerchantTradesResponse> result = customerTransactionRpc.listMerchantTrades(merchantId, listMerchantTradesRequest);
        TestCase.assertEquals("单元测试失败", "[{\"buyerLogin\":\"15988885620\",\"buyerUid\":\"2088102122524333\",\"ctime\":1604160578797,\"merchantId\":\"1e12d53ac54c-8389-46f4-feac-ad8480af\",\"originalAmount\":1000,\"payWay\":2,\"storeId\":\"f1d2b274054e-1f68-96c4-2d90-bcf7dbd0\",\"tsn\":\"2001259247438552\",\"type\":11}]", JSON.toJSONString(result));
    }

}
