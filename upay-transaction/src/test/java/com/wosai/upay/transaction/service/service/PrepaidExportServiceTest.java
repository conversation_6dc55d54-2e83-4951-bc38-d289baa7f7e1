package com.wosai.upay.transaction.service.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2023/8/14、11:59
 **/


@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class PrepaidExportServiceTest {



    @Test
    public void exportTest() throws IOException {
        //上海储值对账单
//        prepaidExportService.doExport("b6e01f54-25b1-4883-8885-0f783d45d86a");
    }


}
