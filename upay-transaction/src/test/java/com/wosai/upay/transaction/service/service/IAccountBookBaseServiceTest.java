package com.wosai.upay.transaction.service.service;

import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import static com.wosai.upay.transaction.enums.LoadType.CONFIG_SNAPSHOT;

/**
 * IAccountBookBaseServiceTest
 *
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
public class IAccountBookBaseServiceTest {

    private static TransactionQuery query;
    private static TransactionVo result;

    @Autowired
    private IAccountBookBaseService accountBookBaseService;

    @Test
    public void queryObj() {
        // by id
        query = new TransactionQuery();
        query.setMerchantId("0040a3b45204-5ceb-fdb4-c8d5-02c14674");
        query.setStartTime(1594627264903L);
        query.setTransactionId("t****************");
        result = this.accountBookBaseService.queryObj(query);
        Assert.assertNotNull("单元测试失败", result);

        // by tsn
        query = new TransactionQuery();
        query.setMerchantId("0040a3b45204-5ceb-fdb4-c8d5-02c14674");
        query.setStartTime(1594627264903L);
        query.setTransactionSn("****************");
        result = this.accountBookBaseService.queryObj(query);
        Assert.assertNotNull("单元测试失败", result);

        // 普通查询
        query = new TransactionQuery();
        query.setMerchantId("0040a3b45204-5ceb-fdb4-c8d5-02c14674");
        query.setTransactionSn("****************");
        result = this.accountBookBaseService.queryObj(query);
        Assert.assertNotNull("单元测试失败", result);
    }

    @Test
    public void queryList() {
//        // by id
//        query = new TransactionQuery();
//        query.setMerchantId("0040a3b45204-5ceb-fdb4-c8d5-02c14674");
//        query.setStartTime(1594627264903L);
//        query.setTransactionId("t****************");
//        result = this.accountBookBaseService.queryObj(query);
//        Assert.assertNotNull("单元测试失败", result);

        // by tsn
        query = new TransactionQuery();
        query.setMerchantId("75f2bb2a73c2-046b-ba04-0291-33048a4a");
        query.setTransactionSn("****************");
        query.setLoadTypes(new HashSet<>(Arrays.asList(CONFIG_SNAPSHOT)));
        List<TransactionVo> transactionVos = this.accountBookBaseService.queryList(query);
        System.out.println(JacksonUtil.toJsonString(transactionVos));

    }
}
