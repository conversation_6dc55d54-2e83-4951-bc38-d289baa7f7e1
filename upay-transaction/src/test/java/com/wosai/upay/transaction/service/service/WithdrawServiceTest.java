package com.wosai.upay.transaction.service.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.shouqianba.withdrawservice.service.WithdrawService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.transaction.util.DateTimeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class WithdrawServiceTest {



    @Test
    public void getTodayWithdrawDetailTest(){

    }


}
