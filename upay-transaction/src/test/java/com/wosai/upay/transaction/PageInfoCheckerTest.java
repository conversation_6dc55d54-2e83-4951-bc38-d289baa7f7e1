package com.wosai.upay.transaction;

import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.util.PageInfoChecker;
import org.junit.Assert;
import org.junit.Test;

/**
 * PageInfoCheckerTest
 *
 * <AUTHOR>
 */
public class PageInfoCheckerTest {

    @Test
    public void check() {
        Object result = null;
        try {
            PageInfoChecker.check(new PageInfo(1, 5001));
        } catch (Exception e) {
            result = e;
        }

        Assert.assertEquals("单元测试失败", BizException.class, result.getClass());

        PageInfoChecker.check(new PageInfo(1, 5000));
    }

}
