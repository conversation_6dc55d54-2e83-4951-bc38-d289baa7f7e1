package com.wosai.service;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.service.common.ListResult;
import com.wosai.upay.transaction.service.CacheService;
import com.wosai.upay.transaction.service.remote.RemoteSharingBookService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/11/19.
 */
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class SharingBookExportServiceTest {
    public static final Logger logger = LoggerFactory.getLogger(SharingBookExportServiceTest.class);
    @Autowired
    private RemoteSharingBookService sharingBookService;
    @Autowired
    private CacheService cacheService;

    @Test
    public void testQuerySharingBooks(){

            cacheService.deleteCache("T::", "a0a4a7a0-5d21-492d-936a-d5a2037d43a4");
            cacheService.deleteCache("T::", "d5d24b37-6c5c-43b3-aa69-a03fa4fb057f");
//        ListResult listResult = sharingBookService.querySharingBooks(CollectionUtil.hashMap(
//                "only_query_count", false,
//                "merchant_id", "0f69420f-3705-427f-9475-21a40cb5e810"
//        ), CollectionUtil.hashMap(
//                "start_timestamp", 0,
//                "end_timestamp", System.currentTimeMillis(),
//                "page_size", 200
//        ));
//        logger.debug("size: {}", listResult.getTotal());
    }
}
