package com.wosai.service;


import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.dao.base.HBaseDao;
import org.apache.avro.generic.GenericRecord;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ConnectionCallback;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;


@ContextConfiguration(locations = {"classpath:spring/business-config.xml", "classpath:spring/tools-config.xml", "classpath:spring/application-security.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class ServiceTest {
    @Autowired
    JdbcTemplate upayJdbcTemplate;
    @Autowired
    DataRepository dataRepository;
    
    @BeforeClass
    public static void setup() {

    }

    @AfterClass
    public static void tearDown() {

    }

    @Test
    public void testTsnGenerator() {

    }

    @Autowired
    private GatewaySupportService gatewaySupportService;

    @Autowired
    private StoreService storeService;

    @Test
    public void test(){
//        upayJdbcTemplate.setQueryTimeout(5);
//
//        upayJdbcTemplate.queryForList("select sleep(20)");
//        Map<String, Object> transaction = gatewaySupportService.getLatestTransactionByOrderSn("1e12d53ac54c-8389-46f4-feac-ad8480af", "2002259240529480", 1626856440278l);
//        Map<String, Object> stringObjectMap = caculateFavorableAmount(transaction);
//        System.out.println(stringObjectMap);

        storeService.getStoreByStoreId("xxxx");
    }

    /**
     * 计算优惠金额
     *
     * @param transaction Avro反序列化对象
     */
    public static Map<String, Object> caculateFavorableAmount(Map<String,Object> transaction){

        try {

            Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
            Map<String, Object> configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
            Map<String, Object> items = MapUtil.getMap(transaction, Transaction.ITEMS);



            Map enhanceConfigSnapshot = TransactionUtils.enhanceConfigSnapshot(configSnapshot);

            //不知道传参是啥
            Map<String, Object> enhanceItems = TransactionUtils.enhanceItems(items != null ? items : configSnapshot );
            Map<String, Object> enhanceExtraOutFields = TransactionUtils.enhanceExtraOutFields(extraOutFields);

            Map<String, Object> caculateMap = TransactionUtils.caculateExtendFields(
                    BeanUtil.getPropInt(transaction, Transaction.STATUS),
                    BeanUtil.getPropInt(transaction, Transaction.TYPE),
                    BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT),
                    BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT),
                    enhanceConfigSnapshot, enhanceItems, enhanceExtraOutFields
            );
            return caculateMap;
        }catch (Exception ex){
            return null;
        }
    }
}
