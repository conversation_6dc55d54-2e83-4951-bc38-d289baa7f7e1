package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.transaction.model.QueryResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@AutoJsonRpcServiceImpl
@Service
public class CrossMerchantServiceImpl implements CrossMerchantService {
    public static final Logger logger = LoggerFactory.getLogger(CrossMerchantServiceImpl.class);
    @Autowired
    CrossMchRefundService crossMchRefundService;

    public QueryResultVO queryResult(String merchantId, String merchantUserId, List<String> storeIds, String terminalId, Long start, Long lastTime, String lastId, Long limit) {
        return crossMchRefundService.doQueryResult(merchantId, merchantUserId, storeIds, terminalId, start, lastTime, lastId, limit);
    }
}
