package com.wosai.upay.transaction.service.model.vo;


import lombok.Data;

import java.util.Objects;

@Data
public class BaseSummary {

    //查询id
     Long statementId;

    //储值
     Long recharge = 0L;
     Long rechargeTotal = 0L;
     Long rechargeGift = 0L;

    //核销
     Long verificationTotal = 0L;
     Long verification = 0L;
     Long verificationGift = 0L;



    public void add(Long recharge, Long rechargeGift, Long rechargeTotal, Long verification, Long verificationGift, Long verificationTotal) {

        //储值
        if (!Objects.isNull(recharge)) {
            this.recharge = this.recharge + recharge;
        }
        if (!Objects.isNull(rechargeGift)) {
            this.rechargeGift = this.getRechargeGift() + rechargeGift;
        }
        if (!Objects.isNull(rechargeTotal)) {
            this.rechargeTotal = this.rechargeTotal + rechargeTotal;
        }

        //核销
        if (!Objects.isNull(verification)) {
            this.verification = this.verification + verification;
        }
        if (!Objects.isNull(verificationGift)) {
            this.verificationGift = this.verificationGift + verificationGift;
        }
        if (!Objects.isNull(verificationTotal)) {
            this.verificationTotal = this.verificationTotal + verificationTotal;
        }
    }




}
