package com.wosai.upay.transaction.service.dao.hbase;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.cashdesk.api.enums.CommonStatus;
import com.wosai.upay.cashdesk.api.request.CashDeskTradeQueryListRequest;
import com.wosai.upay.cashdesk.api.result.SummaryTradeResponse;
import com.wosai.upay.cashdesk.api.result.TotalTradeResponse;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.IHbaseConstant;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.dao.base.HBaseDao;
import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.util.*;

import lombok.SneakyThrows;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.client.solrj.util.ClientUtils;
import org.apache.solr.common.SolrInputDocument;
import org.apache.solr.common.params.ShardParams;
import org.apache.solr.common.util.NamedList;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import static java.util.stream.Collectors.groupingBy;


@Service
public class TransactionHBaseDao extends HBaseDao<TransactionHBaseQuery> {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String TABLE_PREFIX = "upay:tx_";

    private static final String SOLR_COLLECTION_PREFIX = "upay_tx_";

    @Value("${solr.timeout:3000}")
    private Integer solrTimeout;

    @SuppressWarnings("unchecked")
    public TransactionHBaseDao(@Value("${hbase.tx.tableName:}") String hbaseTXTableName,
                               @Value("${hbase.swipe.tx.tableName}") String hbaseSwipeTxTableName,
                               @Value("${solr.swipe.tx.collection}") String solrSwipeTxCollection ) {
        // 扫码交易配置
        if (!StringUtils.isEmpty(hbaseTXTableName)) {
            tableName = TableName.valueOf(hbaseTXTableName);
        }

        // 刷卡交易配置
        swipeTableName = TableName.valueOf(hbaseSwipeTxTableName);
        swipeCollections = Arrays.asList(solrSwipeTxCollection);
    }


    private static final String MOBILE_ = "mobile_";

    private static final String QRCODE_ = "qrcode_";

    private static final String POS_ = "pos_";

    private static final String SOCODE_ = "socode_";

    private static final String MAYA_ = "maya_";

    private static final String NPOS2_ = "npos2_";

    private static final String GROUPMEAL_ = "groupmeal_";

    private static final String POSPLUS_ = "posplus_";

    private static final String PCPLUGIN_ = "pcplugin_";

    private static final String FACESCAN_ = "faceScan_";

    private static final String TERMINAL_ID_COLON = "terminal_id:";

    private static final String OPERATOR_COLON = "operator:";

    private static final String QRCODE_COLON = "QRCODE:";


    private static final String SUMMARY_TX_GROUPBY_KEY = "summaryTxGroupByKey";

    private static final String SUMMARY_TX = "summaryTx";

    private static final String JSON_FACET = "json.facet";

    private static final String FACETS = "facets";

    private static final String AGG = "agg";

    private static final String BUCKETS = "buckets";

    private static final String SUMMARY_JSON_FACET = "{" +
            "paid_amount:\"sum(if(eq(type, " + Transaction.TYPE_PAYMENT + "),original_amount,0))\"" +
            ",paid_count:\"sum(if(eq(type, " + Transaction.TYPE_PAYMENT + "),1,0))\"" +
            ",deposit_amount:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CONSUME + "),original_amount,0))\"" +
            ",deposit_count:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CONSUME + "),1,0))\"" +
            ",refunded_amount:\"sum(if(eq(type, " + Transaction.TYPE_REFUND + "),original_amount,0))\"" +
            ",refunded_count:\"sum(if(eq(type, " + Transaction.TYPE_REFUND + "),1,0))\"" +
            ",deposit_canceled_amount:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CONSUME_CANCEL + "),original_amount,0))\"" +
            ",deposit_canceled_count:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CONSUME_CANCEL + "),1,0))\"" +
            ",canceled_amount:\"sum(if(eq(type, " + Transaction.TYPE_CANCEL + "),original_amount,0))\"" +
            ",canceled_count:\"sum(if(eq(type, " + Transaction.TYPE_CANCEL + "),1,0))\"" +
            ",charge_amount:\"sum(if(eq(type, " + Transaction.TYPE_CHARGE + "),original_amount,0))\"" +
            ",charge_count:\"sum(if(eq(type, " + Transaction.TYPE_CHARGE + "),1,0))\"" +
            ",charge_refund_amount:\"sum(if(eq(type, " + Transaction.TYPE_CHARGE_REFUND + "),original_amount,0))\"" +
            ",charge_refund_count:\"sum(if(eq(type, " + Transaction.TYPE_CHARGE_REFUND + "),1,0))\"" +
            ",order_take_amount:\"sum(if(eq(type, " + Transaction.TYPE_ORDER_TAKE + "),original_amount,0))\"" +
            ",order_take_count:\"sum(if(eq(type, " + Transaction.TYPE_ORDER_TAKE + "),1,0))\"" +
            ",order_take_refund_amount:\"sum(if(eq(type, " + Transaction.TYPE_ORDER_TAKE_REFUND + "),original_amount,0))\"" +
            ",order_take_refund_count:\"sum(if(eq(type, " + Transaction.TYPE_ORDER_TAKE_REFUND + "),1,0))\"" +
            ",store_pay_amount:\"sum(if(eq(type, " + Transaction.TYPE_STORE_PAY + "),original_amount,0))\"" +
            ",store_pay_count:\"sum(if(eq(type, " + Transaction.TYPE_STORE_PAY + "),1,0))\"" +
            ",store_refund_amount:\"sum(if(eq(type, " + Transaction.TYPE_STORE_REFUND + "),original_amount,0))\"" +
            ",store_refund_count:\"sum(if(eq(type, " + Transaction.TYPE_STORE_REFUND + "),1,0))\"" +
            ",store_in_amount:\"sum(if(eq(type, " + Transaction.TYPE_STORE_IN + "),original_amount,0))\"" +
            ",store_in_count:\"sum(if(eq(type, " + Transaction.TYPE_STORE_IN + "),1,0))\"" +
            ",store_in_refund_amount:\"sum(if(eq(type, " + Transaction.TYPE_STORE_IN_REFUND + "),original_amount,0))\"" +
            ",store_in_refund_count:\"sum(if(eq(type, " + Transaction.TYPE_STORE_IN_REFUND + "),1,0))\"" +
            ",deposit_freeze_amount:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_FREEZE + "),original_amount,0))\"" +
            ",deposit_freeze_count:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_FREEZE + "),1,0))\"" +
            ",deposit_freeze_canceled_amount:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CANCEL + "),original_amount,0))\"" +
            ",deposit_freeze_canceled_count:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CANCEL + "),1,0))\"" +
            "}";


    private static final String SUMMARY_GROUP_BY_JSON_FACET = "{agg:{" +
            "type:terms," +
            "field:%s, " +
            "facet:{" +
            "paid_amount:\"sum(if(eq(type, " + Transaction.TYPE_PAYMENT + "),original_amount,0))\"" +
            ",paid_count:\"sum(if(eq(type, " + Transaction.TYPE_PAYMENT + "),1,0))\"" +
            ",deposit_amount:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CONSUME + "),original_amount,0))\"" +
            ",deposit_count:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CONSUME + "),1,0))\"" +
            ",refunded_amount:\"sum(if(eq(type, " + Transaction.TYPE_REFUND + "),original_amount,0))\"" +
            ",refunded_count:\"sum(if(eq(type, " + Transaction.TYPE_REFUND + "),1,0))\"" +
            ",deposit_canceled_amount:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CONSUME_CANCEL + "),original_amount,0))\"" +
            ",deposit_canceled_count:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CONSUME_CANCEL + "),1,0))\"" +
            ",canceled_amount:\"sum(if(eq(type, " + Transaction.TYPE_CANCEL + "),original_amount,0))\"" +
            ",canceled_count:\"sum(if(eq(type, " + Transaction.TYPE_CANCEL + "),1,0))\"" +
            ",charge_amount:\"sum(if(eq(type, " + Transaction.TYPE_CHARGE + "),original_amount,0))\"" +
            ",charge_count:\"sum(if(eq(type, " + Transaction.TYPE_CHARGE + "),1,0))\"" +
            ",charge_refund_amount:\"sum(if(eq(type, " + Transaction.TYPE_CHARGE_REFUND + "),original_amount,0))\"" +
            ",charge_refund_count:\"sum(if(eq(type, " + Transaction.TYPE_CHARGE_REFUND + "),1,0))\"" +
            ",order_take_amount:\"sum(if(eq(type, " + Transaction.TYPE_ORDER_TAKE + "),original_amount,0))\"" +
            ",order_take_count:\"sum(if(eq(type, " + Transaction.TYPE_ORDER_TAKE + "),1,0))\"" +
            ",order_take_refund_amount:\"sum(if(eq(type, " + Transaction.TYPE_ORDER_TAKE_REFUND + "),original_amount,0))\"" +
            ",order_take_refund_count:\"sum(if(eq(type, " + Transaction.TYPE_ORDER_TAKE_REFUND + "),1,0))\"" +
            ",store_pay_amount:\"sum(if(eq(type, " + Transaction.TYPE_STORE_PAY + "),original_amount,0))\"" +
            ",store_pay_count:\"sum(if(eq(type, " + Transaction.TYPE_STORE_PAY + "),1,0))\"" +
            ",store_refund_amount:\"sum(if(eq(type, " + Transaction.TYPE_STORE_REFUND + "),original_amount,0))\"" +
            ",store_refund_count:\"sum(if(eq(type, " + Transaction.TYPE_STORE_REFUND + "),1,0))\"" +
            ",store_in_amount:\"sum(if(eq(type, " + Transaction.TYPE_STORE_IN + "),original_amount,0))\"" +
            ",store_in_count:\"sum(if(eq(type, " + Transaction.TYPE_STORE_IN + "),1,0))\"" +
            ",store_in_refund_amount:\"sum(if(eq(type, " + Transaction.TYPE_STORE_IN_REFUND + "),original_amount,0))\"" +
            ",store_in_refund_count:\"sum(if(eq(type, " + Transaction.TYPE_STORE_IN_REFUND + "),1,0))\"" +
            ",deposit_freeze_amount:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_FREEZE + "),original_amount,0))\"" +
            ",deposit_freeze_count:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_FREEZE + "),1,0))\"" +
            ",deposit_freeze_canceled_amount:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CANCEL + "),original_amount,0))\"" +
            ",deposit_freeze_canceled_count:\"sum(if(eq(type, " + Transaction.TYPE_DEPOSIT_CANCEL + "),1,0))\"" +
            "}" +
            "}}";


    public TransactionSummaryPo summaryTx(TransactionHBaseQuery query) {
        boolean isQueryEs = query.isQueryEs();
        if (query.isQueryCashDesk()) {
            try {
                return summaryTxByCashDesk(query);
            } catch (Exception e) {
                logger.error("summaryTxByCashDesk error", e);
                throw ErrorMessageEnum.GET_CASH_DESK_ERROR.getBizException();
            }
        } else if (isQueryEs) {
            try {
                return summaryTxByEs(query);
            } catch (Exception e) {
                logger.error("summaryTxByEs error", e);
                query.setQueryEs(false);
            }
        }
        return summaryTxBySolr(query);
    }

    private TransactionSummaryPo summaryTxByCashDesk(TransactionHBaseQuery query) throws Exception {
        CashDeskTradeQueryListRequest request = new CashDeskTradeQueryListRequest();
        buildCashDeskRequest(request, query, businessService);
        List<TotalTradeResponse> tradeResponses = cashDeskTradeService.sumCashDeskTradeList(request);
        return TransactionSummaryPo.buildFromCashDeskTradeListSummary(tradeResponses);

    }

    private TransactionSummaryPo summaryTxByEs(TransactionHBaseQuery query) throws Exception {
        BoolQueryBuilder queryParam = QueryBuilders.boolQuery();
        esWhere(queryParam, query);
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(queryParam);
        builder.size(0);
        TermsAggregationBuilder typeAgg = AggregationBuilders.terms(Transaction.TYPE).field(Transaction.TYPE).size(20);
        AggregationBuilder typeCount = AggregationBuilders.count(Transaction.TYPE).field(Transaction.TYPE);
        AggregationBuilder typeSum = AggregationBuilders.sum(Transaction.ORIGINAL_AMOUNT).field(Transaction.ORIGINAL_AMOUNT);
        builder.aggregation(typeAgg.subAggregation(typeCount).subAggregation(typeSum));
        SearchResponse response = elasticsearchService.count(getTableNamePrefix(), builder, query.getMerchantIds());
        if (null != response
                && null != response.getAggregations()
                && null != response.getAggregations().get(Transaction.TYPE)) {
            ParsedLongTerms agg = response.getAggregations().get(Transaction.TYPE);
            return TransactionSummaryPo.buildFromEsTypeBuckets(agg.getBuckets());
        }
        return TransactionSummaryPo.NULL_VALUE;

    }

    private TransactionSummaryPo summaryTxBySolr(TransactionHBaseQuery query) {
        SolrQuery solrQuery = new SolrQuery("*:*");
        where(solrQuery, query);
        solrQuery.set(JSON_FACET, SUMMARY_JSON_FACET);
        solrQuery.setRows(0);
        List<String> collections = getCollections(query);
        if (CollectionUtils.isEmpty(collections)) {
            return TransactionSummaryPo.NULL_VALUE;
        }
        List<QueryResponse> responses = SolrHBaseUtils.query(collections, solrQuery, SUMMARY_TX, getSolrTimeout(query.getSolrTimeout()));
        List<TransactionSummaryPo> result = responses.stream()
                .map(response -> TransactionSummaryPo.buildFromSolrResult((NamedList<?>) response.getResponse().get(FACETS)))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(result) ? TransactionSummaryPo.NULL_VALUE : result.get(0);
    }


    public List<TransactionSummaryPo> summaryTxGroupByKey(TransactionHBaseQuery query) {
        boolean isQueryEs = query.isQueryEs();
        if (query.isQueryCashDesk()) {
            try {
                return summaryTxGroupByKeyByCashDesk(query);
            } catch (Exception e) {
                logger.error("error in summaryTxGroupByKeyByCashDesk", e);
                throw ErrorMessageEnum.GET_CASH_DESK_ERROR.getBizException();
            }
        } else if (isQueryEs) {
            try {
                return summaryTxGroupByKeyByEs(query);
            } catch (Exception e) {
                logger.error("error in summaryTxGroupByKeyByEs", e);
                query.setQueryEs(false);
            }
        }
        return summaryTxGroupByKeyBySolr(query);
    }

    private List<TransactionSummaryPo> summaryTxGroupByKeyByCashDesk(TransactionHBaseQuery query) throws Exception {
        CashDeskTradeQueryListRequest request = new CashDeskTradeQueryListRequest();
        buildCashDeskRequest(request, query, businessService);
        List<SummaryTradeResponse> strs = cashDeskTradeService.sumCashDeskTradeGroup(request);
        List<TransactionSummaryPo> sumPos = new ArrayList<>();
        for (SummaryTradeResponse str : strs) {
            sumPos.add(TransactionSummaryPo.buildFromCashDeskSummary(str));
        }
        Map<String, List<TransactionSummaryPo>> collect = sumPos.stream().collect(groupingBy((po -> Objects.toString(po.getKey()))));

        List<TransactionSummaryPo> sumVs = new ArrayList<>();
        sumVs = collect.values().stream()
                .map(r -> {
                    TransactionSummaryPo po = new TransactionSummaryPo();
                    po.setKey(r.get(0).getKey());
                    return r.stream().reduce(po, TransactionSummaryPo::summarySelf, TransactionSummaryPo::summarySelf);
                })
                .collect(Collectors.toList());
        return sumVs;
    }

    private List<TransactionSummaryPo> summaryTxGroupByKeyByEs(TransactionHBaseQuery query) throws Exception {
        BoolQueryBuilder queryParam = QueryBuilders.boolQuery();
        esWhere(queryParam, query);
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(queryParam);
        builder.size(0);
        TermsAggregationBuilder groupAgg = AggregationBuilders.terms(query.getGroupByKey()).field(query.getGroupByKey());
        TermsAggregationBuilder typeAgg = AggregationBuilders.terms(Transaction.TYPE).field(Transaction.TYPE).size(20);
        AggregationBuilder typeCount = AggregationBuilders.count(Transaction.TYPE).field(Transaction.TYPE);
        AggregationBuilder typeSum = AggregationBuilders.sum(Transaction.ORIGINAL_AMOUNT).field(Transaction.ORIGINAL_AMOUNT);
        builder.aggregation(groupAgg.subAggregation(typeAgg.subAggregation(typeCount).subAggregation(typeSum)));
        SearchResponse response = elasticsearchService.count(getTableNamePrefix(), builder, query.getMerchantIds());
        List<TransactionSummaryPo> result = Lists.newArrayList();
        if (null != response
                && null != response.getAggregations()
                && null != response.getAggregations().get(query.getGroupByKey())) {
            ParsedLongTerms groupTerms = response.getAggregations().get(query.getGroupByKey());
            groupTerms.getBuckets().forEach(group -> {
                ParsedLongTerms typeTerms = group.getAggregations().get(Transaction.TYPE);
                TransactionSummaryPo po = TransactionSummaryPo.buildFromEsTypeBuckets(typeTerms.getBuckets());
                po.setKey(group.getKeyAsNumber().intValue());
                result.add(po);
            });
            // Hbase返回的数据是按照key升序排的，ES没有排序，需要做下排序
            result.sort((r1, r2) -> (int)r1.getKey() < (int) r2.getKey() ? -1 : 1);
        }
        return result;
    }

    private List<TransactionSummaryPo> summaryTxGroupByKeyBySolr(TransactionHBaseQuery query) {
        SolrQuery solrQuery = new SolrQuery("*:*");
        where(solrQuery, query);
        solrQuery.set(JSON_FACET, String.format(SUMMARY_GROUP_BY_JSON_FACET, SolrUtils.escapeQueryChars(query.getGroupByKey())));
        solrQuery.setRows(0);
        List<String> collections = getCollections(query);
        if (CollectionUtils.isEmpty(collections)) {
            return Lists.newArrayList();
        }
        List<QueryResponse> responses = SolrHBaseUtils.query(collections, solrQuery, SUMMARY_TX_GROUPBY_KEY, getSolrTimeout(query.getSolrTimeout()));
        if (CollectionUtils.isEmpty(responses)) {
            return Lists.newArrayList();
        }
        ArrayList<NamedList> bucketsNamedList = new ArrayList<>();
        responses.forEach(response -> {
            NamedList namedList = (NamedList) response.getResponse().get(FACETS);
            if (!(Objects.isNull(namedList.get(AGG)) || Objects.isNull(((NamedList) namedList.get(AGG)).get(BUCKETS)))) {
                bucketsNamedList.addAll((ArrayList<NamedList>) ((NamedList) namedList.get(AGG)).get(BUCKETS));
            }
        });
        return mergeResponseByKey(bucketsNamedList
                .stream()
                .map(TransactionSummaryPo::buildFromSolrResult)
                .collect(Collectors.toList()));
    }

    private List<TransactionSummaryPo> mergeResponseByKey(List<TransactionSummaryPo> result) {
        Map<String, List<TransactionSummaryPo>> collect = result.stream().collect(groupingBy((po -> Objects.toString(po.getKey()))));
        return collect.values().stream()
                .map(r -> {
                    TransactionSummaryPo po = new TransactionSummaryPo();
                    po.setKey(r.get(0).getKey());
                    return r.stream().reduce(po, TransactionSummaryPo::summarySelf, TransactionSummaryPo::summarySelf);
                }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> buildEntryMap(Result r, Map<String, Object> initRow) {
        Map<String, Object> row = new HashMap<>(initRow);
        if (!CollectionUtils.isEmpty(row)) {
            row.replace(DaoConstants.ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ID)));
            row.replace(Transaction.TSN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TSN)));
            row.replace(Transaction.STORE_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.STORE_ID)));
            row.replace(Transaction.ORDER_SN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ORDER_SN)));
            row.replace(Transaction.TRADE_NO, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TRADE_NO)));
            row.replace(Transaction.MERCHANT_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.MERCHANT_ID)));
            row.replace(Transaction.TERMINAL_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TERMINAL_ID)));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PROVIDER)).ifPresent(m ->
                    row.replace(Transaction.PROVIDER, null, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.EFFECTIVE_AMOUNT)).ifPresent(m ->
                    row.replace(Transaction.EFFECTIVE_AMOUNT, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.ORIGINAL_AMOUNT)).ifPresent(m ->
                    row.replace(Transaction.ORIGINAL_AMOUNT, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAID_AMOUNT)).ifPresent(m ->
                    row.replace(Transaction.PAID_AMOUNT, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.RECEIVED_AMOUNT)).ifPresent(m ->
                    row.replace(Transaction.RECEIVED_AMOUNT, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.STATUS)).ifPresent(m ->
                    row.replace(Transaction.STATUS, null, Bytes.toInt(m))
            );
            row.replace(Transaction.BUYER_UID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_UID)));

            row.replace(Transaction.CLIENT_TSN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.CLIENT_TSN)));

            row.replace(Transaction.SUBJECT, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.SUBJECT)));

            row.replace(Transaction.BODY, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BODY)));

            row.replace(Transaction.ORDER_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ORDER_ID)));

            row.replace(Transaction.PRODUCT_FLAG, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.PRODUCT_FLAG)));

            row.replace(Transaction.BUYER_LOGIN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_LOGIN)));

            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.TYPE)).ifPresent(m ->
                    row.replace(Transaction.TYPE, null, Bytes.toInt(m))
            );

            row.replace(Transaction.OPERATOR, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.OPERATOR)));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAY_WAY)).ifPresent(m ->
                    row.replace(Transaction.PAYWAY, null, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.SUB_PAYWAY)).ifPresent(m ->
                    row.replace(Transaction.SUB_PAYWAY, null, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.FINISH_TIME)).ifPresent(m ->
                    row.replace(Transaction.FINISH_TIME, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CHANNEL_FINISH_TIME)).ifPresent(m ->
                    row.replace(Transaction.CHANNEL_FINISH_TIME, null, Bytes.toLong(m))
            );

            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CTIME)).ifPresent(m ->
                    row.replace(DaoConstants.CTIME, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.MTIME)).ifPresent(m ->
                    row.replace(DaoConstants.MTIME, null, Bytes.toLong(m))
            );
            row.replace(Transaction.PROVIDER_ERROR_INFO, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.PROVIDER_ERROR_INFO)));
            row.replace(Transaction.ITEMS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.ITEMS)));
            row.replace(Transaction.EXTRA_PARAMS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTRA_PARAMS)));
            row.replace(Transaction.EXTRA_OUT_FIELDS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTRA_OUT_FIELDS)));
            row.replace(Transaction.EXTENDED_PARAMS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTENDED_PARAMS)));
            row.replace(Transaction.CONFIG_SNAPSHOT, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.CONFIG_SNAPSHOT)));
            row.replace(Transaction.REFLECT, null, reflectService.convertReflect(SolrHBaseUtils.parseReflectField(r.getValue(FAMILY, IHbaseConstant.REFLECT))));
            row.replace(Transaction.BIZ_ERROR_CODE, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.BIZ_ERROR_CODE)));
        } else {
            row.put(DaoConstants.ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ID)));
            row.put(Transaction.TSN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TSN)));
            row.put(Transaction.STORE_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.STORE_ID)));
            row.put(Transaction.ORDER_SN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ORDER_SN)));
            row.put(Transaction.TRADE_NO, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TRADE_NO)));
            row.put(Transaction.MERCHANT_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.MERCHANT_ID)));
            row.put(Transaction.TERMINAL_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TERMINAL_ID)));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PROVIDER)).ifPresent(m ->
                    row.put(Transaction.PROVIDER, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.EFFECTIVE_AMOUNT)).ifPresent(m ->
                    row.put(Transaction.EFFECTIVE_AMOUNT, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.ORIGINAL_AMOUNT)).ifPresent(m ->
                    row.put(Transaction.ORIGINAL_AMOUNT, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAID_AMOUNT)).ifPresent(m ->
                    row.put(Transaction.PAID_AMOUNT, Bytes.toLong(m))
            );
            row.put(Transaction.RECEIVED_AMOUNT, null);
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.RECEIVED_AMOUNT)).ifPresent(m ->
                    row.put(Transaction.RECEIVED_AMOUNT, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.STATUS)).ifPresent(m ->
                    row.put(Transaction.STATUS, Bytes.toInt(m))
            );
            row.put(Transaction.BUYER_UID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_UID)));

            row.put(Transaction.CLIENT_TSN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.CLIENT_TSN)));

            row.put(Transaction.SUBJECT, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.SUBJECT)));

            row.put(Transaction.BODY, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BODY)));

            row.put(Transaction.ORDER_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ORDER_ID)));

            row.put(Transaction.PRODUCT_FLAG, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.PRODUCT_FLAG)));

            row.put(Transaction.BUYER_LOGIN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_LOGIN)));

            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.TYPE)).ifPresent(m ->
                    row.put(Transaction.TYPE, Bytes.toInt(m))
            );
            row.put(Transaction.NFC_CARD, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.NFC_CARD)));
            row.put(Transaction.OPERATOR, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.OPERATOR)));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAY_WAY)).ifPresent(m ->
                    row.put(Transaction.PAYWAY, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.SUB_PAYWAY)).ifPresent(m ->
                    row.put(Transaction.SUB_PAYWAY, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.FINISH_TIME)).ifPresent(m ->
                    row.put(Transaction.FINISH_TIME, Bytes.toLong(m))
            );
            row.put(Transaction.CHANNEL_FINISH_TIME, null);
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CHANNEL_FINISH_TIME)).ifPresent(m ->
                    row.put(Transaction.CHANNEL_FINISH_TIME, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.VERSION)).ifPresent(m ->
                    row.put(DaoConstants.VERSION, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CTIME)).ifPresent(m ->
                    row.put(DaoConstants.CTIME, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.MTIME)).ifPresent(m ->
                    row.put(DaoConstants.MTIME, Bytes.toLong(m))
            );
            if (Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.DELETED)).isPresent()) {
                row.put(DaoConstants.DELETED, Bytes.toInt(r.getValue(FAMILY, IHbaseConstant.DELETED)) == 1);
            } else {
                row.put(DaoConstants.DELETED, false);
            }
            row.put(Transaction.PROVIDER_ERROR_INFO, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.PROVIDER_ERROR_INFO)));
            row.put(Transaction.ITEMS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.ITEMS)));
            row.put(Transaction.EXTRA_PARAMS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTRA_PARAMS)));
            row.put(Transaction.EXTRA_OUT_FIELDS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTRA_OUT_FIELDS)));
            row.put(Transaction.EXTENDED_PARAMS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.EXTENDED_PARAMS)));
            row.put(Transaction.CONFIG_SNAPSHOT, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.CONFIG_SNAPSHOT)));
            row.put(Transaction.REFLECT, reflectService.convertReflect(SolrHBaseUtils.parseReflectField(r.getValue(FAMILY, IHbaseConstant.REFLECT))));
            row.put(Transaction.BIZ_ERROR_CODE, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.BIZ_ERROR_CODE)));
        }

        return row;
    }

    @Override
    protected int getSolrTimeout(Integer solrTimeout) {
        return Optional.ofNullable(solrTimeout).orElse(this.solrTimeout);
    }

    @Override
    protected void where(SolrQuery solrQuery, TransactionHBaseQuery query) {
        List<String> fq = Lists.newArrayList();
        List<String> q = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(query.getTransactionSns())) {
            q.add(SolrUtils.formatInFq(Transaction.TSN, SolrUtils.escapeQueryChars(query.getTransactionSns())));
        } else if (org.springframework.util.StringUtils.hasLength(query.getTransactionSn())) {
            q.add(SolrUtils.formatEqualFq(Transaction.TSN, SolrUtils.escapeQueryChars(query.getTransactionSn())));
        }

        if (!CollectionUtils.isEmpty(query.getOrderSns())) {
            q.add(SolrUtils.formatInFq(Transaction.ORDER_SN, SolrUtils.escapeQueryChars(query.getOrderSns())));
        }

        if (StringUtils.hasLength(query.getTradeNo())) {
            q.add(SolrUtils.formatInFq(Transaction.TRADE_NO, Lists.newArrayList(SolrUtils.escapeQueryChars(query.getTradeNo()))));
        }

        if (!CollectionUtils.isEmpty(query.getMerchantIds())) {
            q.add(SolrUtils.formatInFq(Transaction.MERCHANT_ID, SolrUtils.escapeQueryChars(query.getMerchantIds())));
            if (ApolloUtil.useSolrRoute(query.getMerchantIds().get(0), query.getStartTime()) 
                    && DBSelectContext.getContext().getSelectDb() != UpayQueryType.UPAY_SWIPE) {
                solrQuery.setParam(ShardParams._ROUTE_, query.getMerchantIds().stream().collect(Collectors.joining(",")));
            }
        }

        Long startTime = null;

        if (!Objects.isNull(query.getStartTime()) || !Objects.isNull(query.getEndTime())) {
            q.add(SolrUtils.formatRangeFq(DaoConstants.CTIME, query.getStartTime(), query.getEndTime(), false));
            startTime = query.getStartTime();
        }

        if (!Objects.isNull(query.getChannelFinishStartTime()) || !Objects.isNull(query.getChannelFinishEndTime())) {
            q.add(SolrUtils.formatRangeFq(Transaction.CHANNEL_FINISH_TIME, query.getChannelFinishStartTime(), query.getChannelFinishEndTime(), false));
            startTime = query.getChannelFinishStartTime();
        }

        // 门店id
        if (!CollectionUtils.isEmpty(query.getStoreIds())) {
            q.add(SolrUtils.formatInFq(Transaction.STORE_ID, SolrUtils.escapeQueryChars(query.getStoreIds())));
        }

        // payWay
        if (!CollectionUtils.isEmpty(query.getPayWays())) {
            q.add(SolrUtils.formatInFq(Transaction.PAYWAY, query.getPayWays()));
        }


        if (!CollectionUtils.isEmpty(query.getClientTsns())) {
            q.add(SolrUtils.formatInFq(Transaction.CLIENT_TSN, SolrUtils.escapeQueryChars(query.getClientTsns())));
        }


        if (!CollectionUtils.isEmpty(query.getProviders())) {
            q.add(SolrUtils.formatInFq(Transaction.PROVIDER, query.getProviders()));
        }


        if (!Objects.isNull(query.getMinOriginalAmount()) || !Objects.isNull(query.getMaxOriginalAmount())) {
            q.add(SolrUtils.formatRangeFq(Transaction.ORIGINAL_AMOUNT, query.getMinOriginalAmount(), query.getMaxOriginalAmount(), true));
        }

        if (!CollectionUtils.isEmpty(query.getBuyerUids())) {
            q.add(SolrUtils.formatInFq(Transaction.BUYER_UID, SolrUtils.escapeQueryChars(query.getBuyerUids())));
        }

        if (CollectionUtils.isEmpty(query.getBuyerUids()) && Objects.equals(query.getBuyerUidNotEmpty(), true)) {
            fq.add(SolrUtils.formatNotEmpty(Transaction.BUYER_UID));
        }

        if (!CollectionUtils.isEmpty(query.getProductFlags())) {
            q.add(SolrUtils.formatLikeInFq(Transaction.PRODUCT_FLAG, SolrUtils.escapeQueryChars(query.getProductFlags())));
        }
        if (!CollectionUtils.isEmpty(query.getBuyerLogins())) {
            q.add(SolrUtils.formatInFq(Transaction.BUYER_LOGIN, SolrUtils.escapeQueryChars(query.getBuyerLogins())));
        }

        if (query.getProviderIsNull()) {
            q.add(SolrUtils.formatNullFq(Transaction.PROVIDER));
        }


        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(query.getOpenId())) {
            q.add(SolrUtils.bracketsWrapper(SolrUtils.formatInFq(Transaction.BUYER_LOGIN, SolrUtils.escapeQueryChars(query.getOpenId()))
                    + SolrUtils.SEPARATOR_OR + SolrUtils.formatInFq(Transaction.BUYER_UID, SolrUtils.escapeQueryChars(query.getOpenId()))));
        }

        //终端 包含
        if (!CollectionUtils.isEmpty(query.getTerminals())) {
            List<String> terminalWhere = new ArrayList<>(query.getTerminals().size());
            for (String terminal : SolrUtils.escapeQueryChars(query.getTerminals())) {
                if (terminal.startsWith(MOBILE_)) {
                    terminalWhere.add(OPERATOR_COLON + terminal.substring(MOBILE_.length()));
                } else if (terminal.startsWith(QRCODE_)) {
                    terminalWhere.add(OPERATOR_COLON + terminal.replace(QRCODE_, ClientUtils.escapeQueryChars(QRCODE_COLON)));
                } else if (terminal.startsWith(POS_)) {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal.substring(POS_.length()));
                } else if (terminal.startsWith(MAYA_)) {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal.substring(MAYA_.length()));
                } else if (terminal.startsWith(POSPLUS_)) {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal.substring(POSPLUS_.length()));
                } else if (terminal.startsWith(PCPLUGIN_)) {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal.substring(PCPLUGIN_.length()));
                } else if (terminal.startsWith(SOCODE_)) {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal.substring(SOCODE_.length()));
                } else if (terminal.startsWith(NPOS2_)) {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal.substring(NPOS2_.length()));
                } else if (terminal.startsWith(GROUPMEAL_)) {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal.substring(GROUPMEAL_.length()));
                } else if (terminal.startsWith(FACESCAN_)) {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal.substring(FACESCAN_.length()));
                } else {
                    terminalWhere.add(TERMINAL_ID_COLON + terminal);
                }
            }
            q.add(SolrUtils.bracketsWrapper(Joiner.on(SolrUtils.SEPARATOR_OR).join(terminalWhere)));
        }

        //终端不包含

        if (!CollectionUtils.isEmpty(query.getNotTerminals())) {
            List<String> operators = Lists.newArrayList();
            List<String> terminals = Lists.newArrayList();
            SolrUtils.escapeQueryChars(query.getNotTerminals()).forEach(terminal -> {
                if (terminal.startsWith(MOBILE_)) {
                    operators.add(terminal.substring(MOBILE_.length()));
                } else if (terminal.startsWith(QRCODE_)) {
                    operators.add(terminal.replace(QRCODE_, ClientUtils.escapeQueryChars(QRCODE_COLON)));
                } else if (terminal.startsWith(SOCODE_)) {
                    terminals.add(terminal.substring(SOCODE_.length()));
                } else if (terminal.startsWith(POS_)) {
                    terminals.add(terminal.substring(POS_.length()));
                } else if (terminal.startsWith(MAYA_)) {
                    terminals.add(terminal.substring(MAYA_.length()));
                } else if (terminal.startsWith(POSPLUS_)) {
                    terminals.add(terminal.substring(POSPLUS_.length()));
                } else if (terminal.startsWith(PCPLUGIN_)) {
                    terminals.add(terminal.substring(PCPLUGIN_.length()));
                } else if (terminal.startsWith(NPOS2_)) {
                    terminals.add(terminal.substring(NPOS2_.length()));
                } else if (terminal.startsWith(GROUPMEAL_)) {
                    terminals.add(terminal.substring(GROUPMEAL_.length()));
                } else if (terminal.startsWith(FACESCAN_)) {
                    terminals.add(terminal.substring(FACESCAN_.length()));
                } else {
                    terminals.add(terminal);
                }
            });

            if (!CollectionUtils.isEmpty(terminals)) {
                q.add(SolrUtils.formatNotInFq(Transaction.TERMINAL_ID, terminals));
            }

            if (!CollectionUtils.isEmpty(operators)) {
                q.add(SolrUtils.formatNotInFq(Transaction.OPERATOR, operators));
            }
        }

        List<String> subFq = new ArrayList<>(query.getStatusTypeSubPayWayQueries().values().size());
        for (StatusTypeSubPayWayQuery statusTypeSubPayWayQuery : query.getStatusTypeSubPayWayQueries().values()) {
            List<String> fqs = Lists.newArrayList();
            if (!statusTypeSubPayWayQuery.isValid()) {
                continue;
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getStatusList())) {
                fqs.add(SolrUtils.formatInFq(Transaction.STATUS, statusTypeSubPayWayQuery.getStatusList()));
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getNotStatusList())) {
                fqs.add(SolrUtils.formatNotInFq(Transaction.STATUS, statusTypeSubPayWayQuery.getNotStatusList()));
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getTypeList())) {
                fqs.add(SolrUtils.formatInFq(Transaction.TYPE, statusTypeSubPayWayQuery.getTypeList()));
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getNotTypeList())) {
                fqs.add(SolrUtils.formatNotInFq(Transaction.TYPE, statusTypeSubPayWayQuery.getNotTypeList()));
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getSubPayWayList())) {
                fqs.add(SolrUtils.formatInFq(Transaction.SUB_PAYWAY, statusTypeSubPayWayQuery.getSubPayWayList()));
            }
            if (!CollectionUtils.isEmpty(fqs)) {
                subFq.add(SolrUtils.bracketsWrapper(Joiner.on(SolrUtils.SEPARATOR_AND).join(fqs)));
            }
        }

        if (!CollectionUtils.isEmpty(subFq)) {
            q.add(SolrUtils.bracketsWrapper(Joiner.on(SolrUtils.SEPARATOR_OR).join(subFq)));
        }
        queryStrategy(solrQuery, fq, q, startTime);


    }

    @Override
    protected String getTableNamePrefix() {
        return TABLE_PREFIX;
    }

    @Override
    protected String getSolrCollectionPrefix() {
        return SOLR_COLLECTION_PREFIX;
    }


    @Override
    protected void esWhere(BoolQueryBuilder esQuery, TransactionHBaseQuery query) {
        if (!CollectionUtils.isEmpty(query.getTransactionSns())) {
            esQuery.must(buildESInQuery(Transaction.TSN, query.getTransactionSns()));
        } else if (StringUtils.hasLength(query.getTransactionSn())) {
            esQuery.must(QueryBuilders.matchQuery(Transaction.TSN, query.getTransactionSn()));
        }

        if (!CollectionUtils.isEmpty(query.getOrderSns())) {
            esQuery.must(buildESInQuery(Transaction.ORDER_SN, query.getOrderSns()));
        }

        if (StringUtils.hasLength(query.getTradeNo())) {
            esQuery.must(QueryBuilders.matchQuery(Transaction.TRADE_NO, query.getTradeNo()));
        }

        if (!CollectionUtils.isEmpty(query.getMerchantIds())) {
            esQuery.must(buildESInQuery(Transaction.MERCHANT_ID, query.getMerchantIds()));
        }

        Long startTime = query.getStartTime();
        if (CollectionUtils.isEmpty(query.getTradeMemos()) && (startTime == null || startTime < NeedESQueryUtil.ES_DATA_START)) {
            startTime = NeedESQueryUtil.ES_DATA_START;
        }
        esQuery.must(buildESRangeQuery(DaoConstants.CTIME, startTime, query.getEndTime(), false));

        if (!Objects.isNull(query.getChannelFinishStartTime()) || !Objects.isNull(query.getChannelFinishEndTime())) {
            esQuery.must(buildESRangeQuery(Transaction.CHANNEL_FINISH_TIME, query.getChannelFinishStartTime(), query.getChannelFinishEndTime(), false));
        }

        // 门店id
        if (!CollectionUtils.isEmpty(query.getStoreIds())) {
            esQuery.must(buildESInQuery(Transaction.STORE_ID, query.getStoreIds()));
        }

        // payWay
        if (!CollectionUtils.isEmpty(query.getPayWays())) {
            esQuery.must(buildESInQuery(Transaction.PAYWAY, query.getPayWays()));
        }

        if (!CollectionUtils.isEmpty(query.getClientTsns())) {
            esQuery.must(buildESInQuery(Transaction.CLIENT_TSN, query.getClientTsns()));
        }

        if (!CollectionUtils.isEmpty(query.getProviders())) {
            esQuery.must(buildESInQuery(Transaction.PROVIDER, query.getProviders()));
        }


        if (!Objects.isNull(query.getMinOriginalAmount()) || !Objects.isNull(query.getMaxOriginalAmount())) {
            esQuery.must(buildESRangeQuery(Transaction.ORIGINAL_AMOUNT, query.getMinOriginalAmount(), query.getMaxOriginalAmount(), true));
        }


        if (!CollectionUtils.isEmpty(query.getBuyerUids())) {
            esQuery.must(buildESInQuery(Transaction.BUYER_UID, query.getBuyerUids()));
        }

        if (CollectionUtils.isEmpty(query.getBuyerUids()) && Objects.equals(query.getBuyerUidNotEmpty(), true)) {
            esQuery.must(buildExists(Transaction.BUYER_UID));
        }

        if (!CollectionUtils.isEmpty(query.getProductFlags())) {
            esQuery.must(buildFullWildcardQuery(Transaction.PRODUCT_FLAG, query.getProductFlags()));
        }
        if (!CollectionUtils.isEmpty(query.getBuyerLogins())) {
            esQuery.must(buildESInQuery(Transaction.BUYER_LOGIN, query.getBuyerLogins()));
        }

        if (query.getProviderIsNull()) {
            esQuery.mustNot(QueryBuilders.existsQuery(Transaction.PROVIDER));
        }


        //终端 包含
        if (!CollectionUtils.isEmpty(query.getTerminals())) {
            BoolQueryBuilder termialQuery = QueryBuilders.boolQuery();
            for (String terminal : query.getTerminals()) {
                if (terminal.startsWith(MOBILE_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.OPERATOR, terminal.substring(MOBILE_.length())));
                } else if (terminal.startsWith(QRCODE_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.OPERATOR, terminal.replace(QRCODE_, QRCODE_COLON)));
                } else if (terminal.startsWith(POS_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(POS_.length())));
                } else if (terminal.startsWith(MAYA_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(MAYA_.length())));
                } else if (terminal.startsWith(POSPLUS_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(POSPLUS_.length())));
                } else if (terminal.startsWith(PCPLUGIN_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(PCPLUGIN_.length())));
                } else if (terminal.startsWith(SOCODE_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(SOCODE_.length())));
                } else if (terminal.startsWith(NPOS2_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(NPOS2_.length())));
                } else if (terminal.startsWith(GROUPMEAL_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(GROUPMEAL_.length())));
                } else if (terminal.startsWith(FACESCAN_)) {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(FACESCAN_.length())));
                } else {
                    termialQuery.should().add(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal));
                }
            }
            esQuery.must(termialQuery);
        }

        //终端不包含
        if (!CollectionUtils.isEmpty(query.getNotTerminals())) {
            query.getNotTerminals().forEach(terminal -> {
                if (terminal.startsWith(MOBILE_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.OPERATOR, terminal.substring(MOBILE_.length())));
                } else if (terminal.startsWith(QRCODE_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.OPERATOR, terminal.replace(QRCODE_, QRCODE_COLON)));
                } else if (terminal.startsWith(SOCODE_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(SOCODE_.length())));
                } else if (terminal.startsWith(POS_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(POS_.length())));
                } else if (terminal.startsWith(MAYA_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(MAYA_.length())));
                } else if (terminal.startsWith(POSPLUS_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(POSPLUS_.length())));
                } else if (terminal.startsWith(PCPLUGIN_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(PCPLUGIN_.length())));
                } else if (terminal.startsWith(NPOS2_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(NPOS2_.length())));
                } else if (terminal.startsWith(GROUPMEAL_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(GROUPMEAL_.length())));
                } else if (terminal.startsWith(FACESCAN_)) {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal.substring(FACESCAN_.length())));
                } else {
                    esQuery.mustNot(QueryBuilders.matchQuery(Transaction.TERMINAL_ID, terminal));
                }
            });
        }


        BoolQueryBuilder statusQuery = QueryBuilders.boolQuery();
        for (StatusTypeSubPayWayQuery statusTypeSubPayWayQuery : query.getStatusTypeSubPayWayQueries().values()) {
            if (!statusTypeSubPayWayQuery.isValid()) {
                continue;
            }
            BoolQueryBuilder tmpQuery = QueryBuilders.boolQuery();
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getStatusList())) {
                tmpQuery.must(buildESInQuery(Transaction.STATUS, statusTypeSubPayWayQuery.getStatusList()));
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getNotStatusList())) {
                statusTypeSubPayWayQuery.getNotStatusList().forEach(status -> {
                    tmpQuery.mustNot(QueryBuilders.matchQuery(Transaction.STATUS, status));
                });
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getTypeList())) {
                tmpQuery.must(buildESInQuery(Transaction.TYPE, statusTypeSubPayWayQuery.getTypeList()));
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getNotTypeList())) {
                statusTypeSubPayWayQuery.getNotTypeList().forEach(type -> {
                    tmpQuery.mustNot(QueryBuilders.matchQuery(Transaction.TYPE, type));
                });
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuery.getSubPayWayList())) {
                tmpQuery.must(buildESInQuery(Transaction.SUB_PAYWAY, statusTypeSubPayWayQuery.getSubPayWayList()));
            }
            statusQuery.should(tmpQuery);
        }

        if (statusQuery.should().size() > 0) {
            esQuery.must(statusQuery);
        }

        if (!CollectionUtils.isEmpty(query.getTradeMemos())) {
            esQuery.must(buildFullWildcardQuery(Transaction.REFLECT, query.getTradeMemos()));
        }

    }

    @Override
    protected void buildCashDeskRequest(CashDeskTradeQueryListRequest request, TransactionHBaseQuery query, BusinessService businessService) {
        if (!CollectionUtils.isEmpty(query.getTransactionSns())) {
            request.setTsns(query.getTransactionSns());
        } else if (StringUtils.hasLength(query.getTransactionSn())) {
            request.setTsns(Lists.newArrayList(query.getTransactionSn()));
        }

        if (!CollectionUtils.isEmpty(query.getMerchantIds())) {
            request.setMerchantIds(query.getMerchantIds());
        }

        request.setStartTime(query.getStartTime());
        request.setEndTime(query.getEndTime());

        request.setMinOriginalAmount(query.getMinOriginalAmount());
        request.setMaxOriginalAmount(query.getMaxOriginalAmount());

        // 门店id
        if (!CollectionUtils.isEmpty(query.getStoreIds())) {
            request.setStoreIds(query.getStoreIds());
        }

        // payWay
        if (!CollectionUtils.isEmpty(query.getPayWays())) {
            request.setPayways(query.getPayWays());
        }

        // status type subPayWay
        if (MapUtils.isNotEmpty(query.getStatusTypeSubPayWayQueries())) {
            Map<CommonStatus, com.wosai.upay.cashdesk.api.param.StatusTypeSubPayWayQuery> queries = new HashMap<>();
            query.getStatusTypeSubPayWayQueries().forEach((k,v) -> {
                com.wosai.upay.cashdesk.api.param.StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new com.wosai.upay.cashdesk.api.param.StatusTypeSubPayWayQuery();
                if (!v.isValid()) {
                    return;
                }
                if (!CollectionUtils.isEmpty(v.getTypeList())) {
                    statusTypeSubPayWayQuery.setTypeList(v.getTypeList());
                }
                if (!CollectionUtils.isEmpty(v.getNotTypeList())) {
                    statusTypeSubPayWayQuery.setNotStatusList(v.getNotTypeList());
                }
                if (!CollectionUtils.isEmpty(v.getStatusList())) {
                    statusTypeSubPayWayQuery.setStatusList(v.getStatusList());
                }
                if (!CollectionUtils.isEmpty(v.getNotStatusList())) {
                    statusTypeSubPayWayQuery.setNotStatusList(v.getNotStatusList());
                }
                if (!CollectionUtils.isEmpty(v.getSubPayWayList())) {
                    statusTypeSubPayWayQuery.setSubPayWayList(v.getSubPayWayList());
                }
                queries.put(CommonStatus.valueOf(k.name()), statusTypeSubPayWayQuery);
            });
            request.setStatusTypeSubPayWayQueries(queries);
        }

        if (!CollectionUtils.isEmpty(query.getProductFlags())) {
            request.setProductFlags(query.getProductFlags());
        }

        if (!CollectionUtils.isEmpty(query.getCashDeskIds())) {
            request.setCashDeskIds(query.getCashDeskIds());
        }

        //终端 包含
        List<String> deviceIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(query.getTerminals())) {
            for (String terminal : query.getTerminals()) {
                if (terminal.startsWith(MOBILE_)) {
                    deviceIds.add(terminal.replace(MOBILE_, ""));
                } else if (terminal.startsWith(QRCODE_)) {
                    String qrcode = terminal.replace(QRCODE_, "");
                    deviceIds.add(MapUtil.getString(businessService.getTerminalInfoByDeviceFingerprint(qrcode), DaoConstants.ID));
                } else if (terminal.startsWith(POS_)) {
                    deviceIds.add(terminal.replace(POS_, ""));
                } else if (terminal.startsWith(MAYA_)) {
                    deviceIds.add(terminal.replace(MAYA_, ""));
                } else if (terminal.startsWith(POSPLUS_)) {
                    deviceIds.add(terminal.replace(POSPLUS_, ""));
                } else if (terminal.startsWith(PCPLUGIN_)) {
                    deviceIds.add(terminal.replace(PCPLUGIN_, ""));
                } else if (terminal.startsWith(SOCODE_)) {
                    deviceIds.add(terminal.replace(SOCODE_, ""));
                } else if (terminal.startsWith(NPOS2_)) {
                    deviceIds.add(terminal.replace(NPOS2_, ""));
                } else if (terminal.startsWith(GROUPMEAL_)) {
                    deviceIds.add(terminal.replace(GROUPMEAL_, ""));
                } else if (terminal.startsWith(FACESCAN_)) {
                    deviceIds.add(terminal.replace(FACESCAN_, ""));
                } else {
                    deviceIds.add(terminal);
                }
            }
            request.setDeviceIds(deviceIds);
        }

    }

    @SneakyThrows
    public void putRow(@NotNull String merchantId, @NotNull Long cTime, @NotNull String id, List<Three<String, Object, Class<?>>> columnValueTypeList) {
        if (!CollectionUtils.isEmpty(columnValueTypeList)) {
            Put put = new Put(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(cTime), Bytes.toBytes(id)));
            Map<String, Object> map = new HashedMap(columnValueTypeList.size());
            columnValueTypeList.stream().filter(value -> (value.fst != null && value.snd != null && value.third != null)).forEach(columnValueType -> {
                        map.put(columnValueType.fst, columnValueType.snd);
                        put.addColumn(FAMILY, columnValueType.fst.getBytes(), SolrUtils.convert2ByteArray(columnValueType.snd, columnValueType.third));
                    }
            );
            put.setTimestamp(MapUtils.getLongValue(map, DaoConstants.MTIME, System.currentTimeMillis()));
            SolrHBaseUtils.put(getTableName(new DateTime(cTime).toString(CommonConstant.DAY_SDF_PATTERN_YYYYMM)), put);

            SolrInputDocument solrInputFields = buildSolrInputDocument(merchantId, cTime, id, map);
            SolrHBaseUtils.putSolr(SOLR_COLLECTION_PREFIX + new DateTime(cTime).toString(CommonConstant.DAY_SDF_PATTERN_YYYYMM), solrInputFields);
        }
    }

    private SolrInputDocument buildSolrInputDocument(@NotNull String merchantId, @NotNull Long cTime, @NotNull String id, Map<String, Object> map) {
        SolrInputDocument solrInputFields = new SolrInputDocument();
        solrInputFields.addField(DaoConstants.ID, String.valueOf(Hex.encodeHex(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(cTime), Bytes.toBytes(id)))));
        solrInputFields.addField(Transaction.TSN, map.get(Transaction.TSN));
        solrInputFields.addField(Transaction.MERCHANT_ID, map.get(Transaction.MERCHANT_ID));
        solrInputFields.addField(Transaction.STORE_ID, map.get(Transaction.STORE_ID));
        solrInputFields.addField(Transaction.TERMINAL_ID, map.get(Transaction.TERMINAL_ID));
        solrInputFields.addField(Transaction.CLIENT_TSN, map.get(Transaction.CLIENT_TSN));
        solrInputFields.addField(Transaction.TYPE, map.get(Transaction.TYPE));
        solrInputFields.addField(Transaction.ORIGINAL_AMOUNT, map.get(Transaction.ORIGINAL_AMOUNT));
        solrInputFields.addField(Transaction.ORDER_SN, map.get(Transaction.ORDER_SN));
        solrInputFields.addField(Transaction.TRADE_NO, map.get(Transaction.TRADE_NO));
        solrInputFields.addField(Transaction.PAYWAY, map.get(Transaction.PAYWAY));
        solrInputFields.addField(Transaction.SUB_PAYWAY, map.get(Transaction.SUB_PAYWAY));
        solrInputFields.addField(Transaction.PROVIDER, map.get(Transaction.PROVIDER));
        solrInputFields.addField(DaoConstants.CTIME, map.get(DaoConstants.CTIME));
        solrInputFields.addField(DaoConstants.MTIME, map.get(DaoConstants.MTIME));
        solrInputFields.addField(Transaction.STATUS, map.get(Transaction.STATUS));
        solrInputFields.addField(Transaction.OPERATOR, map.get(Transaction.OPERATOR));
        solrInputFields.addField(Transaction.PRODUCT_FLAG, map.get(Transaction.PRODUCT_FLAG));
        solrInputFields.addField(Transaction.BUYER_UID, map.get(Transaction.BUYER_UID));
        solrInputFields.addField(Transaction.BUYER_LOGIN, map.get(Transaction.BUYER_LOGIN));
        solrInputFields.addField(Transaction.CHANNEL_FINISH_TIME, map.get(Transaction.CHANNEL_FINISH_TIME));
        solrInputFields.addField(CommonConstant.UPDATE_VERSION_L, MapUtils.getLongValue(map, DaoConstants.MTIME, System.currentTimeMillis()) + 1);
        return solrInputFields;
    }
}
