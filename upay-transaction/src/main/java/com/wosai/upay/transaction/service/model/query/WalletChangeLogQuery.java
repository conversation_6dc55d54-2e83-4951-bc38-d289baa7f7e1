package com.wosai.upay.transaction.service.model.query;

import com.wosai.upay.transaction.service.model.metadata.WalletChangeLogSolrFields;
import com.wosai.upay.transaction.util.SolrUtils;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.util.ClientUtils;
import org.apache.solr.common.params.CommonParams;

import java.util.List;

/**
 * WalletChangeLogQuery
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WalletChangeLogQuery {

    /**
     * 商户 id
     */
    private String merchantId;

    /**
     * 类型列表
     * <p/>
     * <a href="https://confluence.wosai-inc.com/pages/viewpage.action?pageId=240812269">总资产通用账本<a/>
     */
    private List<Integer> types;

    /**
     * 起始时间戳（毫秒）（包含）
     */
    private Long startTime;

    /**
     * 截止时间戳（毫秒）（不含）
     */
    private Long endTime;

    /**
     * 排序列表，依次按序
     */
    private OrderBy[] orderBys;

    /**
     * 兼容 {@link Accessors#chain true}
     */
    public WalletChangeLogQuery setMerchantId(String merchantId) {
        this.merchantId = escapeQueryChars(merchantId);
        return this;
    }

    /**
     * 排序列表，依次排序，setOrderBys 空数据 表示不排序，不调用该方法默认按 ctime 降序
     * <br/>兼容 {@link Accessors#chain true}
     */
    public WalletChangeLogQuery setOrderBys(OrderBy... orderBys) {
        this.orderBys = orderBys;
        return this;
    }

    /**
     * 排序方式
     */
    @Getter
    public enum OrderBy {
        /**
         * 按 ctime 升序
         */
        CTIME_ASC("ctime", SolrQuery.ORDER.asc),
        /**
         * 按 ctime 降序
         */
        CTIME_DESC("ctime", SolrQuery.ORDER.desc);

        /**
         * solr 字段
         */
        private String solrFiled;

        /**
         * 排序
         */
        private SolrQuery.ORDER solrQueryOrder;

        OrderBy(String solrFiled, SolrQuery.ORDER solrQueryOrder) {
            this.solrFiled = solrFiled;
            this.solrQueryOrder = solrQueryOrder;
        }
    }

    /**
     * 查询偏移量
     */
    private Integer offset;

    /**
     * 限制最大条目数（rows）
     */
    private Integer limit;

    /**
     * 构建 {@link SolrQuery}
     */
    public SolrQuery buildSolrQuery() {
        SolrQuery solrQuery = new SolrQuery("*:*");
        // 导余额对账单，禁用缓存
        solrQuery.set(CommonParams.CACHE, false);
        // filter list
        solrQuery.setParam("fl", "id,merchant_id,ctime");

        // filter query
        if (StringUtils.isNotEmpty(merchantId)) {
            solrQuery.addFilterQuery(SolrUtils.formatEqualFq(WalletChangeLogSolrFields.MERCHANT_ID, merchantId));
        }
        if (CollectionUtils.isNotEmpty(types)) {
            solrQuery.addFilterQuery(SolrUtils.formatInFq(WalletChangeLogSolrFields.TYPE, types));
        }
        String ctimeRange = SolrUtils.formatRangeFq(WalletChangeLogSolrFields.CTIME, startTime, endTime, false);
        if (StringUtils.isNotEmpty(ctimeRange)) {
            solrQuery.addFilterQuery(ctimeRange);
        }

        // sort（order by）
        if (orderBys != null) {
            for (OrderBy orderBy : orderBys) {
                if (orderBy != null) {
                    solrQuery.addSort(orderBy.getSolrFiled(), orderBy.getSolrQueryOrder());
                }
            }
        } else {
            // 默认按 ctime 降序
            solrQuery.addSort(OrderBy.CTIME_DESC.getSolrFiled(), OrderBy.CTIME_DESC.getSolrQueryOrder());
        }

        // offset & limit
        if (offset != null && offset > 0) {
            solrQuery.setStart(offset);
        }
        if (limit != null && limit > 0) {
            solrQuery.setRows(limit);
        }

        return solrQuery;
    }

    /**
     * 处理下查询注入问题
     */
    private String escapeQueryChars(String value) {
        if (value == null || value.length() == 0) {
            return value;
        }

        return ClientUtils.escapeQueryChars(value);
    }

}
