package com.wosai.upay.transaction.util;

import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * see 支付源列表以及通道: https://confluence.wosai-inc.com/pages/viewpage.action?pageId=1182225
 *
 * <AUTHOR>
 */
public final class ProductFlagUtils {

    public static Set<String> getProductFlagDesc(String... productFlags) {
        if (productFlags == null) {
            return Sets.newTreeSet();
        }
        HashMap<String, String> productFlagMapping = ConfigServiceUtils.getProductFlagMapping();
       return Arrays.stream(productFlags)
                .map(productFlagMapping::get)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
    }

}
