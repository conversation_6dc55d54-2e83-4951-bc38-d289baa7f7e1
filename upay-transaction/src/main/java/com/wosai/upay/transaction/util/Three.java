package com.wosai.upay.transaction.util;

/**
 * <AUTHOR>
 * @param <A>
 * @param <B>
 * @param <C>
 */
public class Three<A, B, C> {

    public final A fst;

    public final B snd;

    public final C third;


    public Three(A fst, B snd, C third) {
        this.fst = fst;
        this.snd = snd;
        this.third = third;
    }

    public A getFst() {
        return fst;
    }

    public B getSnd() {
        return snd;
    }

    public C getThird() {
        return third;
    }

    public static <A, B, C> Three<A, B, C> of(A var0, B var1, C var2) {
        return new Three(var0, var1, var2);
    }

    @Override
    public String toString() {
        return fst.toString() + "|" + snd.toString() + "|" + third.toString();
    }
}
