package com.wosai.upay.transaction.service.model.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * WalletChangeLogPo
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class WalletChangeLogPo {

    private String id;

    private Integer type;

    private Long amount;

    private Integer sign;

    private Long balance;

    private String remark;

    private Map<String, Object> detail;

    private String merchantId;

    private String actionId;

    private Long ctime;

    private Long mtime;

    private Long version;

    private String name;
}
