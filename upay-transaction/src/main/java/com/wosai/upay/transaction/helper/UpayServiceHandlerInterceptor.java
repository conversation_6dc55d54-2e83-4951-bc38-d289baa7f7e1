package com.wosai.upay.transaction.helper;

import com.wosai.service.exception.ServiceException;
import com.wosai.upay.transaction.util.ApolloUtil;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/***
 * @ClassName: UpayServiceHandlerInterceptor
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/8/14 6:02 PM
 */
public class UpayServiceHandlerInterceptor implements HandlerInterceptor {

    private static final String FAKE_FLAG = "fake";
    private static final String FAKE_REQUEST = "1";


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        boolean isFakeRequest = FAKE_REQUEST.equals(request.getHeader(FAKE_FLAG));
        if (!ApolloUtil.getFakeRequestEnable() && isFakeRequest) {
            throw new ServiceException("禁止fake数据进入");
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
