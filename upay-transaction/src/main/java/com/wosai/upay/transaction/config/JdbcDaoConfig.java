package com.wosai.upay.transaction.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.dao.common.TimedDaoBase;
import com.wosai.data.dao.jdbc.JdbcDaoBase;
import com.wosai.upay.common.dao.JsonBlobAwareDao;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.Set;

@Configuration
public class JdbcDaoConfig {
    @Resource
    private ObjectMapper objectMapper;

    @Bean("jdbcTemplate")
    public JdbcTemplate jdbcTemplate(DataSource datasource) {
        return new JdbcTemplate(datasource);
    }

    @Bean("statementConfigDao")
    public TimedDaoBase statementConfigDao(JdbcTemplate jdbcTemplate) {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra_info");

        JdbcDaoBase merchantJdbcDao = new JdbcDaoBase("statement_config", jdbcTemplate);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(merchantJdbcDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    /**
     * 设置JsonBlobAwareDao的objectMapper
     *
     * 说明：使用new方式创建的类且不是return new *** 时，不会自动注入objectMapper
     * @param jsonBlobAwareDao
     */
    private void appendObjectMapperConfig(JsonBlobAwareDao jsonBlobAwareDao) {
        Field field = ReflectionUtils.findField(JsonBlobAwareDao.class, "objectMapper");
        field.setAccessible(true);
        ReflectionUtils.setField(field, jsonBlobAwareDao, objectMapper);
    }
}
