package com.wosai.upay.transaction.util;

import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.meta.Payway;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PrepaidBookUtil {

    public static final String AMOUNT_DEFAULT_VALUE = "0.00";

    public static Map<Integer, String> PAYWAY_MAP = new HashMap();

    static {
        List<Payway> payways = Payway.getAll();
        for (Payway payway : payways) {
            PAYWAY_MAP.put(payway.getCode(), payway.getName());
        }
    }

    public static void appendBaseHeader(Map context, SXSSFSheet sheet) {
        //头合并策略
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 1, 2));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 1));
        addLine(sheet, String.format(
                "起始日期" + ":[%s]:[%s]",
                context.get(ConstantUtil.KEY_DATE_START), context.get(ConstantUtil.KEY_DATE_END)));

        addLine(sheet, "对账单编号", (String) context.get("object_number"));
        addLine(sheet, "[商户基本信息及对账单类型]");
        addLine(sheet, "商户号", "商户名称", "对账类型");
        addLine(sheet, (String) context.get("merchant_sn"), (String) context.get("merchant_name"), "储值对账单");

    }

    public static void appendSummaryMerchantHeader(SXSSFSheet summarySheet) {
        addLine(summarySheet, "", "", "");
        summarySheet.addMergedRegion(new CellRangeAddress(summarySheet.getLastRowNum() + 1, summarySheet.getLastRowNum() + 1, 0, 10));
        addLine(summarySheet, "注：核销金额中，充值金额与赠送金额按9:1的比例组成，若某项金额不足，则由另一项金额补齐");
        summarySheet.addMergedRegion(new CellRangeAddress(summarySheet.getLastRowNum() + 1, summarySheet.getLastRowNum() + 1, 0, 10));
        addLine(summarySheet, "当充值和核销中出现负数，代表该笔订单为退款");
        addLine(summarySheet, "", "", "");
        //单元格合并
        summarySheet.addMergedRegion(new CellRangeAddress(summarySheet.getLastRowNum() + 1, summarySheet.getLastRowNum() + 1, 1, 3));
        summarySheet.addMergedRegion(new CellRangeAddress(summarySheet.getLastRowNum() + 1, summarySheet.getLastRowNum() + 1, 4, 6));
        summarySheet.addMergedRegion(new CellRangeAddress(summarySheet.getLastRowNum() + 1, summarySheet.getLastRowNum() + 1, 7, 9));

        addLine(summarySheet, "商户对账汇总",
                "充值", "", "", "核销", "", "", "账户余额", "", "");
        addLine(summarySheet, "日期", "充值金额", "赠送金额", "小计", "充值金额", "赠送金额", "小计", "充值金额", "赠送金额", "小计");
    }

    public static void appendSummaryStoreHeader(SXSSFSheet storeSummarySheet, String storeName) {
        addLine(storeSummarySheet, "");
        addLine(storeSummarySheet, "");
        int lastRowNum = storeSummarySheet.getLastRowNum();
        storeSummarySheet.addMergedRegion(new CellRangeAddress(lastRowNum + 1, lastRowNum + 1, 2, 4));
        storeSummarySheet.addMergedRegion(new CellRangeAddress(lastRowNum + 1, lastRowNum + 1, 5, 7));
        addLine(storeSummarySheet, storeName + "对账汇总", "", "充值", "", "", "核销", "");
        addLine(storeSummarySheet, "", "门店汇总", "充值金额", "赠送金额", "小计", "充值金额", "赠送金额", "小计");
    }

    public static void appendRechargeDetailHeader(SXSSFSheet rechargeSheet) {
        addLine(rechargeSheet, "", "", "");
        rechargeSheet.addMergedRegion(new CellRangeAddress(rechargeSheet.getLastRowNum() + 1 , rechargeSheet.getLastRowNum() + 1 , 0 , 2));
        addLine(rechargeSheet, "注：当充值中出现负数，代表该笔订单为退款");
        addLine(rechargeSheet, "", "", "");
        addLine(rechargeSheet, "充值日期", "充值时间", "充值门店", "订单号", "收款方式", "会员手机号", "充值金额", "赠送金额", "充值前账户余额", "充值后账户余额");
    }

    public static void addVerificationDetailHeader(SXSSFSheet VerificationSheet) {
        addLine(VerificationSheet, "", "", "");
        VerificationSheet.addMergedRegion(new CellRangeAddress(VerificationSheet.getLastRowNum() + 1, VerificationSheet.getLastRowNum() + 1, 0, 10));
        addLine(VerificationSheet, "注：核销金额中，充值金额与赠送金额按9:1的比例组成，若某项金额不足，则由另一项金额补齐");
        addLine(VerificationSheet, "", "", "");
        addLine(VerificationSheet, "消费日期", "消费时间", "消费门店", "订单号", "操作类型", "会员手机号", "核销金额（实充部分）", "核销金额（赠送金额）", "核销总额", "核销前账户余额", "核销后账户余额", "操作人");

    }

    public static void addLine(SXSSFSheet sxssfSheet, String... s) {
        SheetHelper.appendLine(sxssfSheet, Arrays.asList(s));
    }


}
