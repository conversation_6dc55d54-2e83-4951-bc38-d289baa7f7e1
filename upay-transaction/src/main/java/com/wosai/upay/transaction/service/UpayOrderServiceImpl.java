package com.wosai.upay.transaction.service;

import static java.util.stream.Collectors.groupingBy;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.consts.ExceptionBase;
import com.wosai.common.consts.ExceptionConstants;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.cashdesk.api.request.CashDeskTradeQueryListRequest;
import com.wosai.upay.cashdesk.api.result.SummaryTradeResponse;
import com.wosai.upay.cashdesk.api.service.CashDeskTradeService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.OrderBy.OrderType;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.bean.request.ChangeShiftsBatchQueryRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsQueryRequest;
import com.wosai.upay.core.bean.request.UpdateChangeShiftsExtraRequest;
import com.wosai.upay.core.bean.response.ChangeShiftsBatchQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsInfo;
import com.wosai.upay.core.bean.response.ChangeShiftsQueryResponse;
import com.wosai.upay.core.model.CashDeskDevice;
import com.wosai.upay.core.model.ChangeShifts;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.ChangeShiftsService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.model.Payment;
import com.wosai.upay.transaction.constant.TransactionConstant;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.model.ChangeShiftsParam;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.TAccountSumV;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.model.UpayOrderParam;
import com.wosai.upay.transaction.repository.DataRepository;
import com.wosai.upay.transaction.service.dao.hbase.OrderHBaseDao;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.OrderHBaseQuery;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionSumV;
import com.wosai.upay.transaction.util.CommonUtil;
import com.wosai.upay.transaction.util.NeedESQueryUtil;
import com.wosai.upay.transaction.util.PageInfoChecker;
import com.wosai.upay.transaction.util.Three;
import com.wosai.upay.transaction.util.TransactionTypeRelatedUtil;
import com.wosai.upay.transaction.util.TransactionUtil;
import org.springframework.stereotype.Service;

@AutoJsonRpcServiceImpl
@Service
public class UpayOrderServiceImpl implements UpayOrderService {
    public static final Logger logger = LoggerFactory.getLogger(UpayOrderServiceImpl.class);

    @Autowired
    public DataRepository dataRepository;
    @Autowired
    public OrderHBaseDao orderHbaseDao;
    @Autowired
    public TransactionHBaseDao transactionHbaseDao;
    @Autowired
    public MerchantService merchantService;
    @Autowired
    public StoreService storeService;
    @Autowired
    public TerminalService terminalService;
    @Autowired
    public BusinessService businessService;
    @Autowired
    private ChangeShiftsService changeShiftsService;
    @Autowired
    private CashDeskTradeService cashDeskTradeService;

    private static long EXPIRE_DATE_TIME = 30 * 24 * 60 * 60 * 1000L;

    // 订单金额需要转换Long
    private static final Set<String> ORDRE_LONG_COLUMN = CollectionUtil.hashSet(Order.ORIGINAL_TOTAL, Order.NET_ORIGINAL, Order.EFFECTIVE_TOTAL,
            Order.NET_EFFECTIVE, Order.TOTAL_DISCOUNT, Order.NET_DISCOUNT,
            DaoConstants.CTIME, DaoConstants.MTIME, DaoConstants.VERSION);

    // 订单金额需要转换Long
    private static final Set<String> TRANSACTION_LONG_COLUMN = CollectionUtil.hashSet(Transaction.EFFECTIVE_AMOUNT, Transaction.ORIGINAL_AMOUNT,
            Transaction.PAID_AMOUNT, Transaction.RECEIVED_AMOUNT, Transaction.FINISH_TIME, Transaction.CHANNEL_FINISH_TIME,
            DaoConstants.CTIME, DaoConstants.MTIME, DaoConstants.VERSION);

    //-------------------------订单列表-------------------------

    @Override
    public Map getOrderDetailByOrderSn(String orderSn) {
        if (StringUtil.empty(orderSn)) {
            return null;
        }
        OrderHBaseQuery hbaseQuery = new OrderHBaseQuery();
        hbaseQuery.setOrderSn(orderSn);
        List result = orderHbaseDao.queryList(hbaseQuery);
        if (null == result || result.isEmpty()) {
            hbaseQuery.setOrderSn(null);
            hbaseQuery.setTradeNo(orderSn);
            hbaseQuery.setLimit(2);

            result = orderHbaseDao.queryList(hbaseQuery);
        }
        Map order = null;
        if (null != result && result.size() == 1) {
            order = (Map) result.get(0);
            formatOrders(order, TransactionConstant.UPAY_ORDER_DEFAULT_ORDER_QUERY_FLAG);
        }

        return order;
    }

    /**
     * 订单添加费率，是否待结算等信息
     *
     * @param orders
     */
    private void addTransactionInfo(Object orders) {
        List<Map<String, Object>> orderList;
        if (orders instanceof Map) {
            orderList = new ArrayList<>();
            orderList.add((Map<String, Object>) orders);
        } else if (orders instanceof List) {
            orderList = (List) orders;
        } else {
            return;
        }
        List<String> orderSns = new ArrayList<>();
        Set<String> merchantIds = new HashSet<String>();
        Long startTime = System.currentTimeMillis();
        for (Map order : orderList) {
            orderSns.add(MapUtil.getString(order, Order.SN));
            merchantIds.add(MapUtil.getString(order, Order.MERCHANT_ID));
            if (startTime > MapUtil.getLongValue(order, DaoConstants.CTIME)) {
                startTime = MapUtil.getLongValue(order, DaoConstants.CTIME);
            }
        }
        if (orderSns.size() > 0) {
            Map<String, Map<String, Object>> cached = new HashMap(16);

            TransactionHBaseQuery query = new TransactionHBaseQuery();
            query.setOrderSns(orderSns);
            query.setMerchantIds(CollectionUtil.listOf(merchantIds));
            StatusTypeSubPayWayQuery queryTypes = new StatusTypeSubPayWayQuery();
            queryTypes.setTypeList(TransactionTypeRelatedUtil.PAY_TYPE_FOR_TMP_QUERY);
            query.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, queryTypes);
            query.setFilterColumns(TransactionConstant.QUERY_ADD_TRANSACTION_COLUMNS);
            query.setStartTime(startTime);

            List<Map<String, Object>> transactions = transactionHbaseDao.queryList(query);
            transactions.forEach(transaction -> {
                TransactionUtil.expandTransactionInfo(transaction);
                cached.put(MapUtil.getString(transaction, Transaction.ORDER_SN), transaction);
            });

            orderList.forEach(order -> {
                Map<String, Object> transaction = cached.get(MapUtil.getString(order, Order.SN));
                if (transaction == null) {
                    return;
                }
                order.putAll(transaction);
            });
        }
    }

    private void formatTransactions(Object transactions, List queryFlags, boolean removeExtraOutFields) {
        List<Map<String, Object>> list;
        if (transactions instanceof List) {
            list = (List) transactions;
        } else if (transactions instanceof Map) {
            list = new LinkedList();
            list.add((Map<String, Object>) transactions);
        } else {
            return;
        }
        businessService.addBusinessInfoWithQueryFlag(list, queryFlags);
        for (Map<String, Object> transaction : list) {
            TransactionUtil.expandTransactionInfo(transaction);
            caculateTransactionFee(transaction, removeExtraOutFields);
            caculateTransactionExtendFields(transaction, removeExtraOutFields);

            transaction.put(UpayOrderParam.TERMINAL_DEVICE_SN, transaction.get(UpayOrderParam.TERMINAL_DEVICE_FINGERPRINT));
            transaction.remove(UpayOrderParam.TERMINAL_DEVICE_FINGERPRINT);
            transaction.remove(TransactionParam.MERCHANT_COUNTRY);
        }
    }


    private void caculateTransactionExtendFields(Map<String, Object> transaction, boolean removeExtraOutFields) {
        Long hongbaoWosaiAmount = null;
        Long hongbaoWosaiMchAmount = null;
        Long discountWosaiAmount = null;
        Long discountWosaiMchAmount = null;

        Long discountChannel = null;
        Long discountChannelMch = null;
        Long discountChannelMchTopUp = null;
        Long hongbaoChannel = null;
        Long hongbaoChannelMch = null;
        Long hongbaoChannelMchTopUp = null;

        Long discountChannelAmount = null;
        Long discountChannelMchAmount = null;
        Long discountChannelMchTopUpAmount = null;
        Long hongbaoChannelAmount = null;
        Long hongbaoChannelMchAmount = null;
        Long hongbaoChannelMchTopUpAmount = null;


        List<Map<String, Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, Transaction.ITEMS + "." + Transaction.PAYMENTS);

        if (payments != null && payments.size() > 0) {
            for (Map<String, Object> payment : payments) {
                String type = MapUtil.getString(payment, Payment.TYPE);
                long amount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                switch (type) {
                    case Payment.TYPE_HONGBAO_WOSAI:
                        hongbaoWosaiAmount = (null == hongbaoWosaiAmount ? 0 : hongbaoWosaiAmount) + amount;
                        break;
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        hongbaoWosaiMchAmount = (null == hongbaoWosaiMchAmount ? 0 : hongbaoWosaiMchAmount) + amount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI:
                        discountWosaiAmount = (null == discountWosaiAmount ? 0 : discountWosaiAmount) + amount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        discountWosaiMchAmount = (null == discountWosaiMchAmount ? 0 : discountWosaiMchAmount) + amount;
                        break;
                    default:
                        break;
                }
            }
        } else {
            //处理早期收钱吧立减红包订单 在这里区分不了是红包还是立减了，全部当做是立减
            long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
            long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);

            if (originalAmount - effectiveAmount > 0) {
                discountWosaiAmount = (null == discountWosaiAmount ? 0 : discountWosaiAmount) + (originalAmount - effectiveAmount);
            }
        }

        List<Map> extraPayments = (List<Map>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.PAYMENTS);
        if (removeExtraOutFields) {
            transaction.remove(Transaction.EXTRA_OUT_FIELDS);
        }
        if (extraPayments != null && extraPayments.size() > 0) {
            for (Map<String, Object> payment : extraPayments) {
                String type = MapUtil.getString(payment, Payment.TYPE);
                long amount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                switch (type) {
                    case Payment.TYPE_DISCOUNT_CHANNEL:
                        discountChannel = (null == discountChannel ? 0 : discountChannel) + amount;
                        discountChannelAmount = (null == discountChannelAmount ? 0 : discountChannelAmount) + amount;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH:
                        discountChannelMch = (null == discountChannelMch ? 0 : discountChannelMch) + amount;
                        discountChannelMchAmount = (null == discountChannelMchAmount ? 0 : discountChannelMchAmount) + amount;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP:
                        discountChannelMchTopUp = (null == discountChannelMchTopUp ? 0 : discountChannelMchTopUp) + amount;
                        discountChannelMchTopUpAmount = (null == discountChannelMchTopUpAmount ? 0 : discountChannelMchTopUpAmount) + amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL:
                        hongbaoChannel = (null == hongbaoChannel ? 0 : hongbaoChannel) + amount;
                        hongbaoChannelAmount = (null == hongbaoChannelAmount ? 0 : hongbaoChannelAmount) + amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH:
                        hongbaoChannelMch = (null == hongbaoChannelMch ? 0 : hongbaoChannelMch) + amount;
                        hongbaoChannelMchAmount = (null == hongbaoChannelMchAmount ? 0 : hongbaoChannelMchAmount) + amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH_TOP_UP:
                        hongbaoChannelMchTopUp = (null == hongbaoChannelMchTopUp ? 0 : hongbaoChannelMchTopUp) + amount;
                        hongbaoChannelMchTopUpAmount = (null == hongbaoChannelMchTopUpAmount ? 0 : hongbaoChannelMchTopUpAmount) + amount;
                        break;
                    default:
                        break;
                }
            }
        }
        Map<String, Object> channelAmounts = CollectionUtil.hashMap(TransactionConstant.HONGBAO_WOSAI_AMOUNT, hongbaoWosaiAmount,
                TransactionConstant.HONGBAO_WOSAI_MCH_AMOUNT, hongbaoWosaiMchAmount,
                TransactionConstant.DISCOUNT_WOSAI_AMOUNT, discountWosaiAmount,
                TransactionConstant.DISCOUNT_WOSAI_MCH_AMOUNT, discountWosaiMchAmount,
                TransactionConstant.DISCOUNT_CHANNEL, discountChannel,
                TransactionConstant.DISCOUNT_CHANNEL_MCH, discountChannelMch,
                TransactionConstant.DISCOUNT_CHANNEL_MCH_TOP_UP, discountChannelMchTopUp,
                TransactionConstant.HONGBAO_CHANNEL, hongbaoChannel,
                TransactionConstant.HONGBAO_CHANNEL_MCH, hongbaoChannelMch,
                TransactionConstant.HONGBAO_CHANNEL_MCH_TOP_UP, hongbaoChannelMchTopUp,
                TransactionConstant.DISCOUNT_CHANNEL_AMOUNT, discountChannelAmount,
                TransactionConstant.DISCOUNT_CHANNEL_MCH_AMOUNT, discountChannelMchAmount,
                TransactionConstant.DISCOUNT_CHANNEL_MCH_TOP_UP_AMOUNT, discountChannelMchTopUpAmount,
                TransactionConstant.HONGBAO_CHANNEL_AMOUNT, hongbaoChannelAmount,
                TransactionConstant.HONGBAO_CHANNEL_MCH_AMOUNT, hongbaoChannelMchAmount,
                TransactionConstant.HONGBAO_CHANNEL_MCH_TOP_UP_AMOUNTS, hongbaoChannelMchTopUpAmount
        );

        channelAmounts.forEach((channel, amount) -> {
            if (null != amount) {
                transaction.put(channel, amount);
            }
        });
    }

    private void caculateTransactionFee(Map<String, Object> transaction, boolean removeExtraOutFields) {
        if (null == transaction) {
            return;
        }
        String liquation = MapUtil.getString(transaction, TransactionParam.LIQUIDATION_NEXT_DAY);
        String feeRateString = MapUtil.getString(transaction, TransactionParam.FEE_RATE);
        String fee = MapUtil.getString(transaction, TransactionParam.FEE);
        if (liquation == null || feeRateString == null || fee == null) {
            Map tradeParams = getTradeConfigFromConfigSnapshot((Map) transaction.get(Transaction.CONFIG_SNAPSHOT));
            if (tradeParams != null) {
                liquation = MapUtil.getString(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY);
                feeRateString = MapUtil.getString(tradeParams, TransactionParam.FEE_RATE, "0.0");
                if (feeRateString.trim().isEmpty()) {
                    feeRateString = "0.0";
                }
                fee = MapUtil.getString(tradeParams, TransactionParam.FEE);
            }
        }
        transaction.put(TransactionParam.LIQUIDATION_NEXT_DAY, liquation);
        transaction.put(TransactionParam.FEE_RATE, feeRateString);
        transaction.put(TransactionParam.FEE, StringUtil.empty(fee) ? TransactionUtil.applyRate(BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT), feeRateString) : Long.parseLong(fee));
        transaction.remove(Transaction.CONFIG_SNAPSHOT);
        transaction.remove(Transaction.EXTENDED_PARAMS);
        if (removeExtraOutFields) {
            transaction.remove(Transaction.EXTRA_OUT_FIELDS);
        }
        transaction.remove(Transaction.EXTRA_PARAMS);
    }

    private void formatOrders(Object orders, List queryFlags) {
        List<Map<String, Object>> list = null;
        if (orders instanceof List) {
            list = (List) orders;
        } else if (orders instanceof Map) {
            list = new LinkedList();
            list.add((Map<String, Object>) orders);
        } else {
            return;
        }
        addTransactionInfo(list);
        businessService.addBusinessInfoWithQueryFlag(list, queryFlags);
        list.forEach(order -> {
            caculateOrderFee(order);
            caculateOrderExtendFields(order);

            order.put(UpayOrderParam.TERMINAL_DEVICE_SN, order.get(UpayOrderParam.TERMINAL_DEVICE_FINGERPRINT));
            order.remove(UpayOrderParam.TERMINAL_DEVICE_FINGERPRINT);
            order.remove(UpayOrderParam.TERMINAL_CLIENT_SN);
            order.remove(UpayOrderParam.STORE_CLIENT_SN);
            order.remove(TransactionParam.MERCHANT_COUNTRY);

        });
    }

    private void caculateOrderExtendFields(Map<String, Object> order) {
        Long hongbaoWosaiTotal = null;
        Long hongbaoWosaiMchTotal = null;
        Long discountWosaiTotal = null;
        Long discountWosaiMchTotal = null;
        Long netHongbaoWosai = null;
        Long netHongbaoWosaiMch = null;
        Long netDiscountWosai = null;
        Long netDiscountWosaiMch = null;

        // 前端不需要用下面的discountChannel 非net total参数后，删除掉
        Long discountChannel = null;
        Long discountChannelMch = null;
        Long discountChannelMchTopUp = null;
        Long hongbaoChannel = null;
        Long hongbaoChannelMch = null;
        Long hongbaoChannelMchTopUp = null;

        Long netDiscountChannel = null;
        Long netDiscountChannelMch = null;
        Long netDiscountChannelMchTopUp = null;
        Long netHongbaoChannel = null;
        Long netHongbaoChannelMch = null;
        Long netHongbaoChannelMchTopUp = null;

        Long discountChannelTotal = null;
        Long discountChannelMchTotal = null;
        Long discountChannelMchTopUpTotal = null;
        Long hongbaoChannelTotal = null;
        Long hongbaoChannelMchTotal = null;
        Long hongbaoChannelMchTopUpTotal = null;

        List<Map> payments = (List<Map>) BeanUtil.getNestedProperty(order, Order.ITEMS + "." + Order.PAYMENTS);

        if (payments != null && payments.size() > 0) {
            for (Map payment : payments) {
                String type = MapUtil.getString(payment, Payment.TYPE);
                long amountTotal = BeanUtil.getPropLong(payment, Payment.AMOUNT_TOTAL);
                long netAmount = BeanUtil.getPropLong(payment, Payment.NET_AMOUNT);
                switch (type) {
                    case Payment.TYPE_HONGBAO_WOSAI:
                        hongbaoWosaiTotal = (hongbaoWosaiTotal == null ? 0 : hongbaoWosaiTotal) + amountTotal;
                        netHongbaoWosai = (netHongbaoWosai == null ? 0 : netHongbaoWosai) + netAmount;
                        break;
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        hongbaoWosaiMchTotal = (hongbaoWosaiMchTotal == null ? 0 : hongbaoWosaiMchTotal) + amountTotal;
                        netHongbaoWosaiMch = (netHongbaoWosaiMch == null ? 0 : netHongbaoWosaiMch) + netAmount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI:
                        discountWosaiTotal = (null == discountWosaiTotal ? 0 : discountWosaiTotal) + amountTotal;
                        netDiscountWosai = (null == netDiscountWosai ? 0 : netDiscountWosai) + netAmount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        discountWosaiMchTotal = (null == discountWosaiMchTotal ? 0 : discountWosaiMchTotal) + amountTotal;
                        netDiscountWosaiMch = (null == netDiscountWosaiMch ? 0 : netDiscountWosaiMch) + netAmount;
                        break;
                    default:
                        break;
                }
            }
        } else {
            //处理早期收钱吧立减红包订单 在这里区分不了是红包还是立减了，全部当做是立减
            long originalTotal = BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL);
            long netOriginal = BeanUtil.getPropLong(order, Order.NET_ORIGINAL);
            long effectiveTotal = BeanUtil.getPropLong(order, Order.EFFECTIVE_TOTAL);
            long netEffective = BeanUtil.getPropLong(order, Order.NET_EFFECTIVE);

            if (originalTotal - effectiveTotal > 0) {
                discountWosaiTotal = (null == discountWosaiTotal ? 0 : discountWosaiTotal) + (originalTotal - effectiveTotal);
                netDiscountWosai = (null == netDiscountWosai ? 0 : netDiscountWosai) + (netOriginal - netEffective);
            }
        }

        List<Map> extraPayments = (List<Map>) BeanUtil.getNestedProperty(order, Order.ITEMS + "." + Order.CHANNEL_PAYMENTS);
        if (extraPayments != null && extraPayments.size() > 0) {
            for (Map<String, Object> payment : extraPayments) {
                String type = MapUtil.getString(payment, Payment.TYPE);
                long amountTotal = BeanUtil.getPropLong(payment, Payment.AMOUNT_TOTAL);
                long netAmount = BeanUtil.getPropLong(payment, Payment.NET_AMOUNT);
                long amount = netAmount;
                switch (type) {
                    case Payment.TYPE_DISCOUNT_CHANNEL:
                        discountChannel = (null == discountChannel ? 0 : discountChannel) + amount;
                        netDiscountChannel = (null == netDiscountChannel ? 0 : netDiscountChannel) + netAmount;
                        discountChannelTotal = (null == discountChannelTotal ? 0 : discountChannelTotal) + amountTotal;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH:
                        discountChannelMch = (null == discountChannelMch ? 0 : discountChannelMch) + amount;
                        netDiscountChannelMch = (null == netDiscountChannelMch ? 0 : netDiscountChannelMch) + netAmount;
                        discountChannelMchTotal = (null == discountChannelMchTotal ? 0 : discountChannelMchTotal) + amountTotal;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP:
                        discountChannelMchTopUp = (null == discountChannelMchTopUp ? 0 : discountChannelMchTopUp) + amount;
                        netDiscountChannelMchTopUp = (null == netDiscountChannelMchTopUp ? 0 : netDiscountChannelMchTopUp) + netAmount;
                        discountChannelMchTopUpTotal = (null == discountChannelMchTopUpTotal ? 0 : discountChannelMchTopUpTotal) + amountTotal;

                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL:
                        hongbaoChannel = (null == hongbaoChannel ? 0 : hongbaoChannel) + amount;
                        netHongbaoChannel = (null == netHongbaoChannel ? 0 : netHongbaoChannel) + netAmount;
                        hongbaoChannelTotal = (null == hongbaoChannelTotal ? 0 : hongbaoChannelTotal) + amountTotal;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH:
                        hongbaoChannelMch = (null == hongbaoChannelMch ? 0 : hongbaoChannelMch) + amount;
                        netHongbaoChannelMch = (null == netHongbaoChannelMch ? 0 : netHongbaoChannelMch) + netAmount;
                        hongbaoChannelMchTotal = (null == hongbaoChannelMchTotal ? 0 : hongbaoChannelMchTotal) + amountTotal;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH_TOP_UP:
                        hongbaoChannelMchTopUp = (null == hongbaoChannelMchTopUp ? 0 : hongbaoChannelMchTopUp) + amount;
                        netHongbaoChannelMchTopUp = (null == netHongbaoChannelMchTopUp ? 0 : netHongbaoChannelMchTopUp) + netAmount;
                        hongbaoChannelMchTopUpTotal = (null == hongbaoChannelMchTopUpTotal ? 0 : hongbaoChannelMchTopUpTotal) + amountTotal;
                        break;
                    default:
                        break;
                }
            }
        }

        Map<String, Object> paymentAmounts = CollectionUtil.hashMap(TransactionConstant.HONGBAO_WOSAI_TOTAL, hongbaoWosaiTotal,
                TransactionConstant.NET_HONGBAO_WOSAI, netHongbaoWosai,
                TransactionConstant.HONGBAO_WOSAI_MCH_TOTAL, hongbaoWosaiMchTotal,
                TransactionConstant.NET_HONGBAO_WOSAI_MCH, netHongbaoWosaiMch,
                TransactionConstant.DISCOUNT_WOSAI_TOTAL, discountWosaiTotal,
                TransactionConstant.NET_DISCOUNT_WOSAI, netDiscountWosai,
                TransactionConstant.DISCOUNT_WOSAI_MCH_TOTAL, discountWosaiMchTotal,
                TransactionConstant.NET_DISCOUNT_WOSAI_MCH, netDiscountWosaiMch,
                TransactionConstant.DISCOUNT_CHANNEL, discountChannel,
                TransactionConstant.DISCOUNT_CHANNEL_MCH, discountChannelMch,
                TransactionConstant.DISCOUNT_CHANNEL_MCH_TOP_UP, discountChannelMchTopUp,
                TransactionConstant.HONGBAO_CHANNEL, hongbaoChannel,
                TransactionConstant.HONGBAO_CHANNEL_MCH, hongbaoChannelMch,
                TransactionConstant.HONGBAO_CHANNEL_MCH_TOP_UP, hongbaoChannelMchTopUp,
                TransactionConstant.NET_DISCOUNT_CHANNEL, netDiscountChannel,
                TransactionConstant.NET_DISCOUNT_CHANNEL_MCH, netDiscountChannelMch,
                TransactionConstant.NET_DISCOUNT_CHANNEL_MCH_TOP_UP, netDiscountChannelMchTopUp,
                TransactionConstant.HONGBAO_CHANNEL, hongbaoChannel,
                TransactionConstant.NET_HONGBAO_CHANNEL_MCH, netHongbaoChannelMch,
                TransactionConstant.NET_HONGBAO_CHANNEL_MCH_TOP_UP, netHongbaoChannelMchTopUp,
                TransactionConstant.DISCOUNT_CHANNEL_TOTAL, discountChannelTotal,
                TransactionConstant.DISCOUNT_CHANNEL_MCH_TOTAL, discountChannelMchTotal,
                TransactionConstant.DISCOUNT_CHANNEL_MCH_TOP_UP, discountChannelMchTopUpTotal,
                TransactionConstant.HONGBAO_CHANNEL_TOTAL, hongbaoChannelTotal,
                TransactionConstant.HONGBAO_CHANNEL_MCH_TOTAL, hongbaoChannelMchTotal,
                TransactionConstant.HONGBAO_CHANNEL_MCH_TOP_UP_TOTAL, hongbaoChannelMchTopUpTotal
        );
        paymentAmounts.forEach((channel, amount) -> {
            if (null != amount) {
                order.put(channel, amount);
            }
        });
    }

    private void caculateOrderFee(Map<String, Object> order) {
        if (null == order) {
            return;
        }
        Map extraOut = (Map) order.get(Transaction.EXTRA_OUT_FIELDS);
        List payments = new ArrayList();
        List channelPayments = (List) BeanUtil.getProperty(extraOut, Transaction.PAYMENTS);
        Map items = (Map) order.get(Transaction.ITEMS); //items值有可能是order里面的， 也有可能是transaction里面的，存的字段稍稍有点不一样
        List<Map<String, Object>> wosaiPayments = (List<Map<String, Object>>) BeanUtil.getProperty(items, Transaction.PAYMENTS);
        if (null != channelPayments) {
            payments.addAll(channelPayments);
        }
        if (null != wosaiPayments) {
            for (Map<String, Object> payment : wosaiPayments) {
                if (!payment.containsKey(Payment.AMOUNT)) {
                    payment.put(Payment.AMOUNT, payment.get(Payment.AMOUNT_TOTAL));
                }
            }
            payments.addAll(wosaiPayments);
        }
        Map tradeParams = getTradeConfigFromConfigSnapshot((Map) order.get(Transaction.CONFIG_SNAPSHOT));
        String liquidation;
        String feeRateString;
        if (tradeParams != null) {//trade_config
            liquidation = MapUtil.getString(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY);
            feeRateString = MapUtil.getString(tradeParams, TransactionParam.FEE_RATE, "0.0");
        } else {//中间表
            liquidation = MapUtil.getString(order, TransactionConstant.IS_LIQUIDATION_NEXT_DAY);
            feeRateString = MapUtil.getString(order, TransactionConstant.TRADE_FEE_RATE, "0.0");
            order.remove(TransactionConstant.IS_LIQUIDATION_NEXT_DAY);
            order.remove(TransactionConstant.TRADE_FEE_RATE);
        }
        if (feeRateString.trim().isEmpty()) {
            feeRateString = "0.0";
        }
        int status = MapUtil.getIntValue(order, Order.STATUS);
        if (Order.STATUS_PAY_CANCELED == status || Order.STATUS_PAY_ERROR == status) {
            order.put(Order.NET_ORIGINAL, 0);
        }
        order.put(TransactionParam.FEE_RATE, feeRateString);
        order.put(TransactionParam.LIQUIDATION_NEXT_DAY, liquidation);
        order.put(Transaction.PAYMENTS, payments);
        order.remove(Transaction.CONFIG_SNAPSHOT);
        order.remove(Transaction.EXTRA_OUT_FIELDS);
    }

    /**
     * 取得交易参数
     *
     * @param configSnapshot
     * @return
     */
    private Map getTradeConfigFromConfigSnapshot(Map<String, Object> configSnapshot) {
        Map tradeConfig = null;
        if (null == configSnapshot) {
            return tradeConfig;
        }
        for (String key : configSnapshot.keySet()) {
            Object value = configSnapshot.get(key);
            if (value instanceof Map
                    && ((Map) value).containsKey(TransactionParam.FEE_RATE)
                    && MapUtil.getBooleanValue((Map) value, TransactionParam.ACTIVE, false)) {
                tradeConfig = (Map) value;
            }
        }
        if (tradeConfig != null) {
            return tradeConfig;
        }
        for (String key : configSnapshot.keySet()) {
            Object value = configSnapshot.get(key);
            if (value instanceof Map
                    && ((Map) value).containsKey(TransactionParam.LIQUIDATION_NEXT_DAY)
                    && ((Map) value).containsKey(TransactionParam.FEE_RATE)) {
                tradeConfig = (Map) value;
            }
        }
        return tradeConfig;
    }

    @Override
    public ListResult getTransactionList(String merchantId, String storeId, PageInfo pageInfo, Map queryFilter) {
        if (null == pageInfo) {
            pageInfo = new PageInfo();
        }
        if (null == pageInfo.getPage() || pageInfo.getPage() <= 0) {
            pageInfo.setPage(1);
        }
        if (null == pageInfo.getPageSize() || pageInfo.getPageSize() <= 0) {
            pageInfo.setPageSize(20);
        }
        // msp 有调用
        if (pageInfo.getPageSize() > 50000) {
            throw new BizException("分页查询，每次最多查询50000条");
        }

        Object status = BeanUtil.getProperty(queryFilter, Transaction.STATUS);
        Object type = BeanUtil.getProperty(queryFilter, Transaction.TYPE);
        boolean removeExtraOutFields = MapUtil.getBooleanValue(queryFilter, TransactionConstant.REMOVE_EXTRAOUTFIELDS, true);
        String storeName = MapUtil.getString(queryFilter, UpayOrderParam.STORE_NAME);
        String orderSn = MapUtil.getString(queryFilter, UpayOrderParam.ORDER_SN);
        List<String> orderSns = (List) BeanUtil.getProperty(queryFilter, UpayOrderParam.ORDER_SNS);
        List<String> storeIds = (List) BeanUtil.getProperty(queryFilter, UpayOrderParam.STORE_IDS);

        if (StringUtils.empty(merchantId)
                && StringUtils.empty(storeId)) {
            throw new ExceptionBase(ExceptionConstants.PRAMETER_ERROR, "merchantId 与 storeId 不能同时为空");
        }
        List<List<String>> allMerchantIds = new ArrayList<>();
        List<List<String>> allStoreIds = new ArrayList<>();
        List<List<String>> allTerminalIds = new ArrayList<>();

        orderSns = (orderSns == null) ? new ArrayList<>() : orderSns;
        if (!StringUtils.empty(orderSn)) {
            orderSns.add(orderSn);
        }
        if (!StringUtils.empty(storeId)) {
            Map storeInfo = storeService.getStore(storeId);
            if (null == storeInfo) {
                return ListResult.emptyListResult();
            }
            allStoreIds.add(Arrays.asList(MapUtil.getString(storeInfo, DaoConstants.ID)));
            allMerchantIds.add(Arrays.asList(MapUtil.getString(storeInfo, Store.MERCHANT_ID)));
        }
        if (storeIds != null) {
            if (storeIds.isEmpty()) {
                return ListResult.emptyListResult();
            }
            List<String> localMerchantIds = new ArrayList<String>();
            List<String> localStoreIds = new ArrayList<String>();
            for (String localstoreId : storeIds) {
                Map<String, Object> store = storeService.getStore(localstoreId);
                if (store == null) {
                    continue;
                }
                String localMerchantId = MapUtil.getString(store, Store.MERCHANT_ID);
                localStoreIds.add(localstoreId);
                localMerchantIds.add(localMerchantId);
            }
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        }
        if (!StringUtil.empty(storeName)) {
            Set<String> allSameMerchantIds = CommonUtil.getAllSameElementInLists(allMerchantIds);
            String searchStoreOrTerminalMerchantId = null;
            // 仅merchantId过滤到一个时才这么做
            if (allSameMerchantIds.size() == 1) {
                searchStoreOrTerminalMerchantId = allSameMerchantIds.iterator().next();
            }
            List<Map> storeInfos = businessService.getStoreInfosByStoreName(searchStoreOrTerminalMerchantId, storeName);
            if (storeInfos == null || storeInfos.size() == 0) {
                return ListResult.emptyListResult();
            }
            List<String> localStoreIds = new ArrayList<>();
            List<String> localMerchantIds = new ArrayList<>();
            for (Map localStore : storeInfos) {
                String localStoreId = MapUtil.getString(localStore, DaoConstants.ID);
                String localMerchantId = MapUtil.getString(localStore, Store.MERCHANT_ID);
                localStoreIds.add(localStoreId);
                localMerchantIds.add(localMerchantId);
            }
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        }
        if (!StringUtils.empty(merchantId)) {
            Map<String, Object> merchant = merchantService.getMerchantByMerchantId(merchantId);
            if (null == merchant) {
                return ListResult.emptyListResult();
            }
            allMerchantIds.add(Arrays.asList(merchantId));
        }

        TransactionHBaseQuery hbaseQuery = new TransactionHBaseQuery();
        if (allMerchantIds.size() > 0) {
            Set<String> allSameMerchantIds = CommonUtil.getAllSameElementInLists(allMerchantIds);
            if (allSameMerchantIds.size() == 0) {
                return ListResult.emptyListResult();
            }
            hbaseQuery.setMerchantIds(CollectionUtil.listOf(allSameMerchantIds));
        }

        if (allStoreIds.size() > 0) {
            Set<String> allSameStoreIds = CommonUtil.getAllSameElementInLists(allStoreIds);
            if (allSameStoreIds.size() == 0) {
                return ListResult.emptyListResult();
            }
            hbaseQuery.setStoreIds(CollectionUtil.listOf(allSameStoreIds));
        }

        if (allTerminalIds.size() > 0) {
            Set<String> allSameTerminalIds = CommonUtil.getAllSameElementInLists(allTerminalIds);
            if (allSameTerminalIds.size() == 0) {
                return ListResult.emptyListResult();
            }
            hbaseQuery.setTerminals(CollectionUtil.listOf(allSameTerminalIds));
        }
        // 订单编号
        if (!orderSns.isEmpty()) {
            hbaseQuery.setOrderSns(orderSns);
        }
        // 订单状态
        if (null != status) {
            List<Integer> queryStatus = new ArrayList<Integer>();
            if (status instanceof List && ((List) status).size() > 0) {
                for (Object tmp : (List) status) {
                    if (null != tmp) {
                        queryStatus.add(tmp instanceof Integer ? (Integer) tmp : Integer.valueOf(tmp.toString()));
                    }
                }
            } else {
                queryStatus.add(status instanceof Integer ? (Integer) status : Integer.valueOf(status.toString()));

            }
            StatusTypeSubPayWayQuery query = new StatusTypeSubPayWayQuery();
            query.setStatusList(queryStatus);
            hbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, query);
        }

        // 订单类型
        if (null != type) {
            List<Integer> queryTypes = new ArrayList<Integer>();
            if (type instanceof List && ((List) type).size() > 0) {
                for (Object tmp : (List) type) {
                    if (null != tmp) {
                        queryTypes.add(tmp instanceof Integer ? (Integer) tmp : Integer.valueOf(tmp.toString()));
                    }
                }
            } else {
                queryTypes.add(type instanceof Integer ? (Integer) type : Integer.valueOf(type.toString()));
            }
            StatusTypeSubPayWayQuery query = hbaseQuery.getStatusTypeSubPayWayQueries().get(CommonStatus.SUCCESS);
            if (null == query) {
                query = new StatusTypeSubPayWayQuery();
                hbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, query);
            }
            query.setTypeList(queryTypes);
        }
        hbaseQuery.setStartTime(pageInfo.getDateStart());
        hbaseQuery.setEndTime(pageInfo.getDateEnd());

        //统计总条数
        long totalCount = transactionHbaseDao.count(hbaseQuery);

        if (totalCount == 0) {
            return ListResult.emptyListResult();
        }

        if (pageInfo.getOrderBy() == null || pageInfo.getOrderBy().size() == 0) {
            OrderBy orderBy = new OrderBy();
            orderBy.setField(DaoConstants.CTIME);
            orderBy.setOrder(OrderType.ASC);
            hbaseQuery.getOrderBys().add(orderBy);
        } else {
            hbaseQuery.getOrderBys().addAll(pageInfo.getOrderBy());
        }

        int limit = pageInfo.getPageSize();
        int offset = (pageInfo.getPage() - 1) * limit;
        hbaseQuery.setLimit(limit);
        hbaseQuery.setOffset(offset);

        List list = transactionHbaseDao.queryList(hbaseQuery);
        formatTransactions(list, TransactionConstant.UPAY_ORDER_DEFAULT_TRANSACTION_QUERY_FLAG, removeExtraOutFields);
        return new ListResult(totalCount, list);
    }

    @Override
    public ListResult getOrderListForPcDesktop(String terminalSn, PageInfo pageInfo, Map queryFilter) {
        if (StringUtils.empty(terminalSn)) {
            return ListResult.emptyListResult();
        }

        Map terminal = terminalService.getTerminalBySn(terminalSn);
        if (null == terminal) {
            return ListResult.emptyListResult();
        }

        if (pageInfo == null) {
            pageInfo = new PageInfo(1, 10);
        }
        if (pageInfo.getPage() == null) {
            pageInfo.setPage(1);
        }
        if (pageInfo.getPageSize() == null) {
            pageInfo.setPageSize(10);
        }
        if (pageInfo.getOrderBy() == null || pageInfo.getOrderBy().size() == 0) {
            pageInfo.setOrderBy(Arrays.asList(
                    new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)
            ));
        }
        PageInfoChecker.check(pageInfo);

        String orderSn = MapUtil.getString(queryFilter, UpayOrderParam.ORDER_SN);
        OrderHBaseQuery hbaseQuery = new OrderHBaseQuery();
        hbaseQuery.setOrderSn(orderSn);
        hbaseQuery.setMerchantIds(Arrays.asList(MapUtil.getString(terminal, Terminal.MERCHANT_ID)));
        hbaseQuery.setTerminalIds(Arrays.asList(MapUtil.getString(terminal, DaoConstants.ID)));
        hbaseQuery.setStartTime(pageInfo.getDateStart());
        hbaseQuery.setEndTime(pageInfo.getDateEnd());
        hbaseQuery.getOrderBys().addAll(pageInfo.getOrderBy());
        hbaseQuery.setLimit(pageInfo.getPageSize());
        hbaseQuery.setOffset((pageInfo.getPage() - 1) * pageInfo.getPageSize());

        Set<String> queryStatus = new HashSet<>();
        String statuses = MapUtil.getString(queryFilter, UpayOrderParam.STATUSES);
        String status = MapUtil.getString(queryFilter, UpayOrderParam.STATUS);
        if (!StringUtil.empty(statuses)) {
            String[] tmpStatus = statuses.split(",");
            for (String tmp : tmpStatus) {
                if (!StringUtil.empty(tmp)) {
                    queryStatus.add(tmp);
                }
            }

        }
        if (!StringUtil.empty(status)) {
            queryStatus.add(status);
        }
        if (!queryStatus.isEmpty()) {
            hbaseQuery.setStatusList(new ArrayList(queryStatus));
        }
        hbaseQuery.setFilterColumns(TransactionConstant.QUERY_QRDER_COLUMNS_PC_DESKTOP);
        List result = orderHbaseDao.queryList(hbaseQuery);

        //为了提升效率， 直接返回固定条数。
        return new ListResult(1024, result);
    }


    @Override
    public ListResult getTransactionListForPcDesktop(String terminalSn, PageInfo pageInfo, Map queryFilter) {
        if (StringUtils.empty(terminalSn)) {
            return ListResult.emptyListResult();
        }

        Map terminal = terminalService.getTerminalBySn(terminalSn);
        if (null == terminal) {
            return ListResult.emptyListResult();
        }

        pageInfo = CommonUtil.setPageInfoDefaultValueIfNull(pageInfo);
        PageInfoChecker.check(pageInfo);

        boolean returnTsn = MapUtil.getBooleanValue(queryFilter, UpayOrderParam.RETURN_TSN);
        String orderSn = MapUtil.getString(queryFilter, UpayOrderParam.ORDER_SN);
        Set<String> queryStatus = new HashSet<>();
        String statuses = MapUtil.getString(queryFilter, UpayOrderParam.STATUSES);
        String status = MapUtil.getString(queryFilter, UpayOrderParam.STATUS);
        if (!StringUtil.empty(statuses)) {
            String[] tmpStatus = statuses.split(",");
            for (String tmp : tmpStatus) {
                if (!StringUtil.empty(tmp)) {
                    queryStatus.add(tmp);
                }
            }

        }
        if (!StringUtil.empty(status)) {
            queryStatus.add(status);
        }
        TransactionHBaseQuery hbaseQuery = new TransactionHBaseQuery();
        hbaseQuery.setQueryEs(NeedESQueryUtil.getTransactionListForPcDesktopMatch(MapUtil.getString(terminal, Terminal.MERCHANT_ID), pageInfo));
        if (!StringUtil.empty(orderSn)) {
            hbaseQuery.setOrderSns(Arrays.asList(orderSn));
        }
        hbaseQuery.setMerchantIds(Arrays.asList(MapUtil.getString(terminal, Terminal.MERCHANT_ID)));
        hbaseQuery.setTerminals(Arrays.asList(MapUtil.getString(terminal, DaoConstants.ID)));
        hbaseQuery.setStartTime(pageInfo.getDateStart());
        hbaseQuery.setEndTime(pageInfo.getDateEnd());
        hbaseQuery.getOrderBys().addAll(pageInfo.getOrderBy());
        hbaseQuery.setLimit(pageInfo.getPageSize());
        hbaseQuery.setOffset((pageInfo.getPage() - 1) * pageInfo.getPageSize());

        if (!queryStatus.isEmpty()) {
            StatusTypeSubPayWayQuery remappingFunction = new StatusTypeSubPayWayQuery();
            remappingFunction.setStatusList(new ArrayList(queryStatus));
            hbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, remappingFunction);
        }
        hbaseQuery.setFilterColumns(TransactionConstant.QUERY_TRANSACTION_COLUMNS_PC_DESKTOP);
        if (returnTsn) {
            hbaseQuery.getFilterColumns().add(Transaction.TSN);
        }
        List result = transactionHbaseDao.queryList(hbaseQuery);

        //为了提升效率， 直接返回固定条数。
        return new ListResult(1024, result);
    }

    @Override
    public List getTransactionListByOrderSn(String orderSn) {
        if (StringUtil.empty(orderSn)) {
            return null;
        }
        TransactionHBaseQuery hbaseQuery = new TransactionHBaseQuery();
        hbaseQuery.setOrderSns(Arrays.asList(orderSn));
        List transactions = transactionHbaseDao.queryList(hbaseQuery);
        formatTransactions(transactions, TransactionConstant.UPAY_ORDER_DEFAULT_TRANSACTION_QUERY_FLAG, true);
        return transactions;
    }

    @Override
    public List<Map<String, Object>> getOriginalTransactionListByOrderSn(String orderSn) {
        if (StringUtil.empty(orderSn)) {
            return null;
        }
        TransactionHBaseQuery hbaseQuery = new TransactionHBaseQuery();
        hbaseQuery.setOrderSns(Arrays.asList(orderSn));
        List<Map<String, Object>> result = transactionHbaseDao.queryList(hbaseQuery);
        // 原始数据没有排序，按照创建时间由小到大排序
        if(!com.wosai.pantheon.util.CollectionUtil.isEmpty(result)) {
            result.sort((r1, r2) -> MapUtil.getLong(r1, DaoConstants.CTIME) < MapUtil.getLong(r2, DaoConstants.CTIME) ? -1 : 1);
        }
        return result;
    }

    @Override
    public Map getOrderBySn(String merchantId, String orderSn, String clientSn) {
        if (StringUtils.empty(orderSn) && StringUtils.empty(clientSn)) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "订单号和商户订单号不能同时都为空");
        }
        OrderHBaseQuery hbaseQuery = new OrderHBaseQuery();
        if (!StringUtil.empty(orderSn)) {
            hbaseQuery.setOrderSn(orderSn);
        }
        if (StringUtil.empty(orderSn) && !StringUtil.empty(clientSn)) {
            hbaseQuery.setClientSn(clientSn);
        }
        if (!StringUtil.empty(merchantId)) {
            hbaseQuery.setMerchantIds(Arrays.asList(merchantId));
        }
        List<Map<String, Object>> result = orderHbaseDao.queryList(hbaseQuery);
        // sn传的值有可能是拉卡拉或者兴业的订单号
        if (null == result && !StringUtil.empty(orderSn)) {
            hbaseQuery.setOrderSn(null);
            hbaseQuery.setClientSn(null);
            hbaseQuery.setTradeNo(orderSn);
            result = orderHbaseDao.queryList(hbaseQuery);
        }

        return (null != result && result.size() == 1) ? result.get(0) : null;
    }

    @Override
    public boolean updateOrder(Map<String, Object> order) {
        if (null == order || order.isEmpty()) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "订单信息不能为空");
        }

        order.putIfAbsent(DaoConstants.MTIME, System.currentTimeMillis());

        List<Three<String, Object, Class<?>>> columnValueTypeList = new ArrayList<Three<String, Object, Class<?>>>();
        for (String key : order.keySet()) {
            Object val = order.get(key);
            if (null == val) {
                continue;
            }
            if (val instanceof Integer && ORDRE_LONG_COLUMN.contains(key)) {
                val = new Long((Integer) val);
            }
            columnValueTypeList.add(new Three<>(key, val, val.getClass()));
        }
        orderHbaseDao.putRow(MapUtil.getString(order, Transaction.MERCHANT_ID),
                MapUtil.getLong(order, DaoConstants.CTIME),
                MapUtil.getString(order, DaoConstants.ID),
                columnValueTypeList);
        return true;
    }

    public Map getTerminalChangeShiftsStatisticsInfo(String terminalSn, String batchSn) {
        ChangeShiftsParam param = new ChangeShiftsParam();
        param.setTerminalSn(terminalSn);
        param.setBatchSn(batchSn);
        param.setUseBatchSn(true);
        return getChangeShiftsStatisticsInfoProxy(param, Boolean.FALSE);
    }

    public Map getChangeShiftsStatisticsInfo(ChangeShiftsParam param) {
        return getChangeShiftsStatisticsInfoProxy(param, Boolean.TRUE);
    }

    private Map getChangeShiftsStatisticsInfoProxy(ChangeShiftsParam param, boolean isNew) {
        ChangeShiftsParam.commonParam(param);
        ChangeShiftsQueryRequest request = new ChangeShiftsQueryRequest();
        request.setBatchSn(param.getBatchSn());
        request.setCashDeskId(param.getCashDeskId());
        request.setTerminalSn(param.getTerminalSn());
        request.setUseBatchSn(param.getUseBatchSn());
        request.setCsStoreId(param.getCsStoreId());
        request.setAccessCashDesk(param.isAccessCashDesk());
        ChangeShiftsQueryResponse changeShifsInfo = changeShiftsService.getChangeShiftsInfo(request);
        if (null == changeShifsInfo) {
            throw new ExceptionBase(ExceptionConstants.PRAMETER_ERROR, "批次结算信息未找到");
        }
        long end = changeShifsInfo.getEndDate() != null ? changeShifsInfo.getEndDate() : System.currentTimeMillis();
        if (end - changeShifsInfo.getStartDate() > EXPIRE_DATE_TIME) {
            throw new ExceptionBase(ExceptionConstants.AUTH_ERROR, "签到与签退时间相隔时间过长，查询失败");
        }
        return queryChangeShiftsStatisticsByChangeShifts(changeShifsInfo, isNew, TransactionUtil.summaryDeposit(param.getSummaryExts()));
    }

    private Map queryChangeShiftsStatisticsByChangeShifts(ChangeShiftsInfo changeShifsInfo, boolean isNew, boolean addDeposit) {
        Map<String, Object> extraInfo = changeShifsInfo.getExtra();
        Map<String, Object> tradeInfo = MapUtil.getMap(extraInfo, ChangeShifts.EXTRA_TRADE_INFO);
        long startTime = changeShifsInfo.getStartDate();
        long endTime = changeShifsInfo.getEndDate() != null ? changeShifsInfo.getEndDate() : System.currentTimeMillis();

        Map result = CollectionUtil.hashMap(ChangeShifts.START_DATE, startTime,
                ChangeShifts.END_DATE, endTime,
                ChangeShifts.BATCH_SN, changeShifsInfo.getBatchSn(),
                ChangeShifts.CASHIER_NO, changeShifsInfo.getCashierNo()
        );
        if (isNew) {
            // 老版本不返回收银员和收银台信息（有些厂商只能进行固定返回，多加参数后会导致解析报错）
            result.put(ChangeShifts.CASHIER_NAME, changeShifsInfo.getCashierName());
            if (changeShifsInfo.getType() == ChangeShifts.TYPE_CASHDESK) {
                result.put(CashDeskDevice.CASH_DESK_ID, changeShifsInfo.getServiceId());
                result.put(ChangeShifts.CASH_DESK_NAME, changeShifsInfo.getCashDeskName());
            }
        }
        if (MapUtil.isEmpty(tradeInfo)) {
            tradeInfo = new HashMap<String, Object>();
            long paySum = 0L;
            long payCount = 0L;
            long refundSum = 0L;
            long refundCount = 0L;
            List<Map<String, Object>> payDetail = new ArrayList<>();
            List<Map<String, Object>> refundDetail = new ArrayList<>();
            List<TAccountSumV> sumVs = new ArrayList<>();
            TAccountSumV sumV = new TAccountSumV();
            if (endTime - startTime < EXPIRE_DATE_TIME) {
                if (changeShifsInfo.getType() == ChangeShifts.TYPE_TERMINAL) {
                    TransactionHBaseQuery hbaseQuery = new TransactionHBaseQuery();
                    hbaseQuery.setQueryEs(NeedESQueryUtil.queryChangeShiftsStatisticsByChangeShiftsMatch(changeShifsInfo.getMerchantId(), startTime, endTime));
                    hbaseQuery.setTerminals(Arrays.asList(changeShifsInfo.getServiceId()));
                    if (!StringUtil.empty(changeShifsInfo.getMerchantId())) {
                        hbaseQuery.setMerchantIds(Arrays.asList(changeShifsInfo.getMerchantId()));
                    }
                    hbaseQuery.setStartTime(startTime);
                    hbaseQuery.setEndTime(endTime);
        
                    StatusTypeSubPayWayQuery query = new StatusTypeSubPayWayQuery();
                    query.setStatusList(Arrays.asList(Transaction.STATUS_SUCCESS));
                    hbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, query);
                    hbaseQuery.setGroupByKey(Transaction.PAYWAY);
                    List<TransactionSummaryPo> summaryPoList = transactionHbaseDao.summaryTxGroupByKey(hbaseQuery);
                    sumVs = summaryPoList.stream().map(TransactionSummaryPo::buildSumV).map(TransactionSumV::buildTAccountSumV).collect(Collectors.toList());
                } else {
                    CashDeskTradeQueryListRequest request = new CashDeskTradeQueryListRequest();
                    request.setCashDeskIds(Arrays.asList(changeShifsInfo.getServiceId()));
                    request.setStartTime(startTime);
                    request.setEndTime(endTime);
                    com.wosai.upay.cashdesk.api.param.StatusTypeSubPayWayQuery successPayWayQuery = new com.wosai.upay.cashdesk.api.param.StatusTypeSubPayWayQuery();
                    successPayWayQuery.setStatusList(Arrays.asList(Transaction.STATUS_SUCCESS));
                    request.getStatusTypeSubPayWayQueries().put(com.wosai.upay.cashdesk.api.enums.CommonStatus.SUCCESS, successPayWayQuery);
                    List<SummaryTradeResponse> strs = cashDeskTradeService.sumCashDeskTradeGroup(request);
                    Map<String, Map<String, Object>> payGroup = new HashMap<>();
                    Map<String, Map<String, Object>> refundGroup = new HashMap<>();
                    List<TransactionSummaryPo> sumPos = new ArrayList<>();
                    for (SummaryTradeResponse str : strs) {
                        sumPos.add(TransactionSummaryPo.buildFromCashDeskSummary(str));
                    }
                    Map<String, List<TransactionSummaryPo>> collect = sumPos.stream().collect(groupingBy((po -> Objects.toString(po.getKey()))));
                    sumVs = collect.values().stream()
                            .map(r -> {
                                TransactionSummaryPo po = new TransactionSummaryPo();
                                po.setKey(r.get(0).getKey());
                                return r.stream().reduce(po, TransactionSummaryPo::summarySelf, TransactionSummaryPo::summarySelf);
                            })
                            .map(r -> r.buildSumV().buildTAccountSumV())
                            .collect(Collectors.toList());
                }
            }
            if (!sumVs.isEmpty()) {
                for (TAccountSumV tAccountSumV : sumVs) {
                    //pay
                    paySum += tAccountSumV.getPaidAmount();
                    payCount += tAccountSumV.getPaidCount();
                    // refund cancel
                    int curRefundCount = tAccountSumV.getRefundedCount() + tAccountSumV.getCanceldCount();
                    long curRefundAmount = tAccountSumV.getRefundedAmount() + tAccountSumV.getCanceldAmount();
                    refundCount += curRefundCount;
                    refundSum += curRefundAmount;
                    if (tAccountSumV.getPaidCount() > 0) {
                        payDetail.add(CollectionUtil.hashMap(UpayOrderParam.NAME, tAccountSumV.getPayWay() + "",
                                UpayOrderParam.CNT, tAccountSumV.getPaidCount(),
                                UpayOrderParam.FEE, tAccountSumV.getPaidAmount(),
                                "act_merchant", null,
                                "act_cus", null));
                    }
                    if (curRefundCount > 0) {
                        refundDetail.add(CollectionUtil.hashMap(UpayOrderParam.NAME, tAccountSumV.getPayWay() + "",
                                UpayOrderParam.CNT, curRefundCount,
                                UpayOrderParam.FEE, curRefundAmount));
                    }
                    sumV.setDepositAmount(sumV.getDepositAmount() + tAccountSumV.getDepositAmount());
                    sumV.setDepositCount(sumV.getDepositCount() + tAccountSumV.getDepositCount());
                    sumV.setSalesCount(sumV.getSalesCount() + tAccountSumV.getSalesCount());
                    sumV.setSalesAmount(sumV.getSalesAmount() + tAccountSumV.getSalesAmount());
                    sumV.setRefundedCount(sumV.getRefundedCount() + tAccountSumV.getRefundedCount());
                    sumV.setRefundedAmount(sumV.getRefundedAmount() + tAccountSumV.getRefundedAmount());
                    sumV.setPaidAmount(sumV.getPaidAmount() + tAccountSumV.getPaidAmount());
                    sumV.setPaidCount(sumV.getPaidCount() + tAccountSumV.getPaidCount());
                    sumV.setCanceldCount(sumV.getCanceldCount() + tAccountSumV.getCanceldCount());
                    sumV.setCanceldAmount(sumV.getCanceldAmount() + tAccountSumV.getCanceldAmount());
                    sumV.setDepositCancelAmount(sumV.getDepositCancelAmount() + tAccountSumV.getDepositCancelAmount());
                    sumV.setDepositCancelCount(sumV.getDepositCancelCount() + tAccountSumV.getDepositCancelCount());
                    sumV.setDepositFreezeAmount(sumV.getDepositFreezeAmount() + tAccountSumV.getDepositFreezeAmount());
                    sumV.setDepositFreezeCount(sumV.getDepositFreezeCount() + tAccountSumV.getDepositFreezeCount());
                    sumV.setDepositFreezeCanceledAmount(sumV.getDepositFreezeCanceledAmount() + tAccountSumV.getDepositFreezeCanceledAmount());
                    sumV.setDepositFreezeCanceledCount(sumV.getDepositFreezeCanceledCount() + tAccountSumV.getDepositFreezeCanceledCount());
                }
                payDetail.sort((s1, s2) -> {
                    return MapUtil.getIntValue(s1, UpayOrderParam.NAME) < MapUtil.getIntValue(s2, UpayOrderParam.NAME)  ? -1 : 1;
                });
                refundDetail.sort((s1, s2) -> {
                    return MapUtil.getIntValue(s1, UpayOrderParam.NAME) < MapUtil.getIntValue(s2, UpayOrderParam.NAME)  ? -1 : 1;
                });
                sumVs.sort((s1, s2) -> {
                    return s1.getPayWay() < s2.getPayWay() ? -1 : 1;
                });
            }
            sumVs.add(sumV);
            tradeInfo.put(UpayOrderParam.PAY_AMOUNT, paySum);
            tradeInfo.put(UpayOrderParam.PAY_COUNT, payCount);
            tradeInfo.put(UpayOrderParam.PAY_DETAIL, payDetail);
            tradeInfo.put(UpayOrderParam.REFUND_COUNT, refundCount);
            tradeInfo.put(UpayOrderParam.REFUND_AMOUNT, refundSum);
            Map refundDetailMap = new HashMap<>();
            if (null != refundDetail && refundDetail.size() > 0) {
                refundDetail.forEach(detail -> {
                    refundDetailMap.put(MapUtil.getString(detail, UpayOrderParam.NAME), detail);
                });
            }
            tradeInfo.put(UpayOrderParam.REFUND_DETAIL, refundDetailMap);
            tradeInfo.put(UpayOrderParam.SUMMARY_DETAIL, sumVs);

            // 批次已做签退后，将汇总信息更新到班次中
            if (changeShifsInfo.getEndDate() != null) {
                Map<String, Object> extra = changeShifsInfo.getExtra();
                if (extra == null) {
                    extra = new HashMap<>();
                }
                extra.put(ChangeShifts.EXTRA_TRADE_INFO, tradeInfo);
                changeShiftsService.updateChangeShiftsExtra(new UpdateChangeShiftsExtraRequest(changeShifsInfo.getId(), extra));
            }
        }
        if (isNew) {
            // 汇总中不包含预授权和预授权撤销笔数，如果业务方需要这部分数据时，需要加上这部分数据
            List<Map<String, Object>> summaryDetail = (List<Map<String, Object>>) MapUtil.getObject(tradeInfo, UpayOrderParam.SUMMARY_DETAIL);
            if (addDeposit && com.wosai.pantheon.util.CollectionUtil.isNotEmpty(summaryDetail) && summaryDetail.size() > 1) {
                for (Map<String, Object> summary : summaryDetail) {
                    int depositFreezeCount = MapUtil.getIntValue(summary, Transaction.DEPOSIT_FREEZE_COUNT);
                    if (depositFreezeCount > 0) {
                        summary.put("salesCount", MapUtil.getIntValue(summary, "salesCount") + depositFreezeCount);
                    }
                    int depositFreezeCanceledCount = MapUtil.getIntValue(summary, Transaction.DEPOSIT_FREEZE_CANCELED_COUNT);
                    if (depositFreezeCanceledCount > 0) {
                        summary.put("salesCount", MapUtil.getIntValue(summary, "salesCount") + depositFreezeCanceledCount);
                    }
                }
            }
            tradeInfo.remove(UpayOrderParam.PAY_AMOUNT);
            tradeInfo.remove(UpayOrderParam.PAY_COUNT);
            tradeInfo.remove(UpayOrderParam.PAY_DETAIL);
            tradeInfo.remove(UpayOrderParam.REFUND_AMOUNT);
            tradeInfo.remove(UpayOrderParam.REFUND_COUNT);
            tradeInfo.remove(UpayOrderParam.REFUND_DETAIL);
            tradeInfo.remove(ChangeShifts.CASHIER_NAME);
        } else {
            tradeInfo.remove(UpayOrderParam.SUMMARY_DETAIL);
        }
        result.putAll(tradeInfo);
        return result;
    }

    @Override
    public ListResult getTerminalChangeShiftsStatisticsList(String terminalSn, Long startDate, Long endDate,
                                                            PageInfo pageInfo) {
        PageInfoChecker.check(pageInfo);
        ChangeShiftsParam param = new ChangeShiftsParam();
        param.setTerminalSn(terminalSn);
        param.setStartDate(startDate);
        param.setEndDate(endDate);
        param.setReturnUnCheckout(Boolean.TRUE);
        if (pageInfo != null) {
            param.setPage(pageInfo.getPage());
            param.setPageSize(pageInfo.getPageSize());
            if (pageInfo.getOrderBy() != null) {
                for (OrderBy orderBy : pageInfo.getOrderBy()) {
                    if (DaoConstants.CTIME.equals(orderBy.getField())) {
                        if (orderBy.getOrder() != null  && orderBy.getOrder() == OrderType.ASC) {
                            param.setOrderBy(ChangeShiftsParam.ORDER_BY_CTIME_ASC);
                        } else {
                            param.setOrderBy(ChangeShiftsParam.ORDER_BY_CTIME_DESC);
                        }
                    }
                }
            }   
        }
        return getChangeShiftsStatisticsListProxy(param, false);
    }

    @Override
    public ListResult getChangeShiftsStatisticsList(ChangeShiftsParam param) {
        return getChangeShiftsStatisticsListProxy(param, Boolean.TRUE);
    }

    private ListResult getChangeShiftsStatisticsListProxy(ChangeShiftsParam param, boolean isNew) {
        ChangeShiftsBatchQueryRequest request = new ChangeShiftsBatchQueryRequest();
        request.setBatchSn(param.getBatchSn());
        request.setCashDeskId(param.getCashDeskId());
        request.setTerminalSn(param.getTerminalSn());
        request.setStartDate(param.getStartDate());
        request.setEndDate(param.getEndDate());
        request.setPage(param.getPage());
        request.setPageSize(param.getPageSize());
        request.setOrderBy(param.getOrderBy());
        request.setCsStoreId(param.getCsStoreId());
        request.setReturnUnCheckout(param.getReturnUnCheckout());
        request.setAccessCashDesk(param.isAccessCashDesk());
        ChangeShiftsBatchQueryResponse result = changeShiftsService.getChangeShiftsList(request);
        ListResult lr = new ListResult(result.getTotal(), null);
        if (result.getRecords() != null) {
            lr.setRecords(result.getRecords().stream().map(record -> queryChangeShiftsStatisticsByChangeShifts(record, isNew, TransactionUtil.summaryDeposit(param.getSummaryExts()))).collect(Collectors.toList()));
        }
        return lr;
    }

    @Override
    public boolean updateTransaction(Map<String, Object> transaction) {
        if (null == transaction || transaction.isEmpty()) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "流水信息不能为空");
        }

        //储值充值，储值退款的type在hbase特殊处理
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY, 0);
        if (TransactionUtil.isStoreInFlag(MapUtil.getString(transaction, Transaction.PRODUCT_FLAG)) || payway == Order.PAYWAY_STORE) {
            int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
            if (payway == Order.PAYWAY_STORE && TransactionTypeRelatedUtil.GATEWAY_TYPE_CONVERT_REVERSE_2.containsKey(type)){
                transaction.put(Transaction.TYPE, TransactionTypeRelatedUtil.GATEWAY_TYPE_CONVERT_REVERSE_2.get(type));
            } else if (TransactionTypeRelatedUtil.GATEWAY_TYPE_CONVERT_REVERSE.containsKey(type)) {
                transaction.put(Transaction.TYPE, TransactionTypeRelatedUtil.GATEWAY_TYPE_CONVERT_REVERSE.get(type));
            }
        }

        transaction.putIfAbsent(DaoConstants.MTIME, System.currentTimeMillis());

        List<Three<String, Object, Class<?>>> columnValueTypeList = new ArrayList<Three<String, Object, Class<?>>>();
        for (String key : transaction.keySet()) {
            Object val = transaction.get(key);
            if (null == val) {
                continue;
            }
            if (val instanceof Integer && TRANSACTION_LONG_COLUMN.contains(key)) {
                val = new Long((Integer) val);
            }
            columnValueTypeList.add(new Three<>(key, val, val.getClass()));
        }
        transactionHbaseDao.putRow(MapUtil.getString(transaction, Transaction.MERCHANT_ID),
                MapUtil.getLong(transaction, DaoConstants.CTIME),
                MapUtil.getString(transaction, DaoConstants.ID),
                columnValueTypeList);
        return true;
    }

}
