package com.wosai.upay.transaction.service.service.common;

import com.google.common.collect.Lists;
import com.wosai.ads.coeus.api.service.NexusAccountService;
import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.util.BaseAsyncTask;
import com.wosai.upay.transaction.util.ExecutorServiceSupport;
import com.wosai.upay.transaction.util.Pair;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BuyerCommonService {

    /**
     * 头像列表
     */
    @Value("#{'${avatar.avatarList}'.split(',')}")
    private List<String> avatarList;

    /**
     * 头像URL前缀
     */
    @Value("${avatar.avatarPrefix}")
    private String avatarPrefix;

    @Autowired
    private NexusAccountService nexusAccountService;

    @Resource
    private BusinessService businessService;

    /**
     * 根据 buyer_uid 和支付方式获取买家信息
     *
     * @param buyerUid 买家 buyerUid
     * @param payWay   支付方式
     * @return 买家信息
     * buyerName 付款人名称 CommonConstant.BUYER_NAME
     * buyerIcon 付款人头像 CommonConstant.BUYER_ICON
     */
    public Map<String, Object> getBuyerInfo(String buyerUid, Integer payWay) {
        Map<String, Object> buyerInfoMap = null;
        // 微信
        if (Objects.equals(payWay, Order.PAYWAY_WEIXIN)) {
            try {
                buyerInfoMap = getWeChatBuyerInfo(buyerUid).get(100, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("获取微信用户信息出错，openId => {}", buyerUid, e);
            }
        }

        // 如果有些数据为空，则需要处理默认值
        String buyerName = null;
        String buyerIcon = null;
        if (buyerInfoMap != null) {
            buyerName = MapUtils.getString(buyerInfoMap, CommonConstant.BUYER_NAME);
            buyerIcon = MapUtils.getString(buyerInfoMap, CommonConstant.BUYER_ICON);
        } else {
            buyerInfoMap = new TreeMap<>();
        }

        // 空名称默认填支付方式名称+用户后缀
        if (StringUtils.isEmpty(buyerName)) {
            Map<Integer, Object> payWayNames = businessService.getAllPayWayName();
            buyerName = MapUtils.getString(payWayNames, payWay, Strings.EMPTY);
        }

        // 头像
        if (StringUtils.isEmpty(buyerIcon)) {
            if (StringUtils.isNotEmpty(buyerUid)) {
                buyerIcon = avatarPrefix + avatarList.get(Math.abs(buyerUid.hashCode() % avatarList.size()));
            } else {
                buyerIcon = avatarPrefix + avatarList.get(ThreadLocalRandom.current().nextInt(0, avatarList.size()));
            }
        }

        buyerInfoMap.put(CommonConstant.BUYER_NAME, buyerName);
        buyerInfoMap.put(CommonConstant.BUYER_ICON, buyerIcon);
        return buyerInfoMap;
    }

    /**
     * 微信用户
     */
    private FutureTask<Map<String, Object>> getWeChatBuyerInfo(String openId) {
        CarrierItem carrierItem = new CarrierItem(TraceContext.traceId());
        FutureTask<Map<String, Object>> futureTask = new FutureTask<>(new BaseAsyncTask<Map<String, Object>>() {
            @Override
            public Map<String, Object> run() {
                TracingUtil.storeThreadLocalTraceInfo(carrierItem);
                if (StringUtils.isEmpty(openId)) {
                    return null;
                }
                String buyerName = null;
                String buyerIcon = null;

                Map<String, Object> nexusResponse = nexusAccountService.listByOpenIds(Lists.newArrayList(openId));
                if (MapUtils.isNotEmpty(nexusResponse)) {
                    List<Map<String, Object>> data = (List<Map<String, Object>>) nexusResponse.get(CommonConstant.DATA);
                    if (CollectionUtils.isNotEmpty(data)) {
                        Map<String, Object> nexusUserMap = data.get(0);
                        buyerName = MapUtils.getString(nexusUserMap, CommonConstant.NICK_NAME);
                        buyerIcon = MapUtils.getString(nexusUserMap, CommonConstant.AVATAR);
                    }
                }

                Map<String, Object> buyerInfo = new HashMap<>(3);
                buyerInfo.put(CommonConstant.BUYER_NAME, buyerName);
                buyerInfo.put(CommonConstant.BUYER_ICON, buyerIcon);

                return buyerInfo;
            }
        });
        ExecutorServiceSupport.getDefaultExecutorService().submit(futureTask);
        return futureTask;
    }

}
