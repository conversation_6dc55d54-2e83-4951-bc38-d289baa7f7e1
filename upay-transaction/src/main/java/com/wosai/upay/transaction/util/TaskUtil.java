package com.wosai.upay.transaction.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.transaction.model.StatementTaskLog;
import java.util.Map;

public class TaskUtil {

    public static final String TASK_RUN_INFO = "task_run_info";

    /**
     * 导出任务执行信息.
     */
    private static ThreadLocal<Map> exportTaskRunInfo = ThreadLocal.withInitial(()->CollectionUtil.hashMap("start_time", CrudUtil.getCurTime(), "end_time", -1, StatementTaskLog.OPERATE_TIME, -1));

    public static Map getExportTaskRunInfo() {
        return exportTaskRunInfo.get();
    }

    public static void putStartTimeToRunInfo() {
        TaskUtil.getExportTaskRunInfo().put("start_time", CrudUtil.getCurTime());
    }

    public static Map putEndTimeAndReturnRunInfo() {
        Map taskRunInfo = TaskUtil.getExportTaskRunInfo();
        long taskStartTime = BeanUtil.getPropLong(taskRunInfo, "start_time");
        long taskEndTime = CrudUtil.getCurTime();
        taskRunInfo.put("end_time", taskEndTime);
        taskRunInfo.put(StatementTaskLog.OPERATE_TIME, taskEndTime - taskStartTime);
        return taskRunInfo;
    }

}
