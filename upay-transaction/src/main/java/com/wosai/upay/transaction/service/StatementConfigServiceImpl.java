package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.transaction.model.StatementConfig;
import com.wosai.upay.transaction.repository.DataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
@AutoJsonRpcServiceImpl
@Service
public class StatementConfigServiceImpl implements StatementConfigService {

    @Autowired
    DataRepository dataRepository;
    private Dao<Map<String, Object>> statementConfigDao;

    @PostConstruct
    public void init() {
        statementConfigDao = dataRepository.getStatementConfigDao();
    }


    public List getPaywayRecords() {
        Criteria criteria = Criteria.where(StatementConfig.TYPE).is(StatementConfig.TYPE_PAYWAY);
        Filter filter = statementConfigDao.filter(criteria);
        return CollectionUtil.iterator2list(filter.fetchAll());
    }

    public void update(Map map) {
        statementConfigDao.updatePart(map);
    }
}
