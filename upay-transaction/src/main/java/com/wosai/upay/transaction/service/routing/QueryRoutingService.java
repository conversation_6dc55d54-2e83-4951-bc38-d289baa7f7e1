package com.wosai.upay.transaction.service.routing;

import com.wosai.upay.transaction.service.config.QueryRoutingConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Service to determine whether to use MySQL or HBase/Solr for queries
 * based on feature toggle configuration
 */
@Service
public class QueryRoutingService {

    private static final Logger logger = LoggerFactory.getLogger(QueryRoutingService.class);

    @Autowired
    private QueryRoutingConfig queryRoutingConfig;

    /**
     * Determine if MySQL should be used for a merchant
     */
    public boolean shouldUseMySQL(String merchantId) {
        if (!queryRoutingConfig.isMysqlEnabled()) {
            return false;
        }

        // Check blacklist first
        if (isInBlacklist(merchantId)) {
            logger.debug("Merchant {} is in blacklist, using HBase/Solr", merchantId);
            return false;
        }

        // Check whitelist
        if (isInWhitelist(merchantId)) {
            logger.debug("Merchant {} is in whitelist, using MySQL", merchantId);
            return true;
        }

        // Check percentage-based rollout
        int percentage = queryRoutingConfig.getMysqlPercentage();
        if (percentage <= 0) {
            return false;
        }
        if (percentage >= 100) {
            return true;
        }

        // Use merchantId for consistent hashing
        int hash = Math.abs(merchantId.hashCode() % 100);
        boolean useMySQL = hash < percentage;
        
        logger.debug("Merchant {}: percentage-based routing, using MySQL: {}", merchantId, useMySQL);
        return useMySQL;
    }

    private boolean isInWhitelist(String merchantId) {
        String whitelist = queryRoutingConfig.getMysqlMerchantWhitelist();
        if (whitelist == null || whitelist.trim().isEmpty()) {
            return false;
        }
        Set<String> whitelistSet = parseMerchantSet(whitelist);
        return whitelistSet.contains(merchantId);
    }

    private boolean isInBlacklist(String merchantId) {
        String blacklist = queryRoutingConfig.getMysqlMerchantBlacklist();
        if (blacklist == null || blacklist.trim().isEmpty()) {
            return false;
        }
        Set<String> blacklistSet = parseMerchantSet(blacklist);
        return blacklistSet.contains(merchantId);
    }

    private Set<String> parseMerchantSet(String merchantList) {
        Set<String> result = new HashSet<>();
        if (merchantList == null || merchantList.trim().isEmpty()) {
            return result;
        }
        
        String[] merchants = merchantList.split(",");
        for (String merchant : merchants) {
            String trimmed = merchant.trim();
            if (!trimmed.isEmpty()) {
                result.add(trimmed);
            }
        }
        return result;
    }

    /**
     * Get routing decision details for debugging
     */
    public String getRoutingDecision(String merchantId) {
        StringBuilder details = new StringBuilder();
        details.append("Routing decision for merchant ").append(merchantId).append(":\n");
        details.append("- MySQL enabled: ").append(queryRoutingConfig.isMysqlEnabled()).append("\n");
        details.append("- Percentage: ").append(queryRoutingConfig.getMysqlPercentage()).append("%\n");
        details.append("- Whitelist: ").append(queryRoutingConfig.getMysqlMerchantWhitelist()).append("\n");
        details.append("- Blacklist: ").append(queryRoutingConfig.getMysqlMerchantBlacklist()).append("\n");
        details.append("- Use MySQL: ").append(shouldUseMySQL(merchantId));
        return details.toString();
    }
}