package com.wosai.upay.transaction.service.config;

import com.wosai.upay.transaction.util.PartitionConcurrentLinkedQueue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Map;

/***
 * @ClassName: KafkaConfig
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/8/27 15:17
 */
@Configuration
@EnableScheduling
public class BeanConfig {

    @Bean
    public PartitionConcurrentLinkedQueue<Map> partitionConcurrentLinkedQueue(){
        return new PartitionConcurrentLinkedQueue<>();
    }
}