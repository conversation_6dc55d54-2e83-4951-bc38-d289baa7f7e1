package com.wosai.upay.transaction.service.service.common;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.cashdesk.api.bean.CashDeskTransaction;
import com.wosai.upay.cashdesk.api.exception.UpayBizException;
import com.wosai.upay.cashdesk.api.service.CashDeskTradeService;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.CashDesk;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.CashDeskService;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.CacheService;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;
import com.wosai.upay.transaction.util.TransactionUtil;

import lombok.extern.slf4j.Slf4j;


/***
 * @ClassName: CashDeskService
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/10/21 5:54 PM
 */
@Slf4j
@Service
public class CashDeskInfoService {

    @Resource
    private CashDeskTradeService cashDeskTradeService;

    @Resource
    private CashDeskService cashDeskService;

    @Resource
    private CacheService cacheService;

    @Lazy
    @Resource
    TransactionHBaseDao transactionHBaseDao;

    public TransactionVo setCashDeskInfo(TransactionVo transactionVo){
        setCashDesk(transactionVo);
        return transactionVo;
    }

    public void setCashDeskInfo(List<Map<String, Object>> transactionList){
        Map<String, Map<String, Object>> transactionMap = new HashMap();
        transactionList.stream().forEach(transaction -> {
            if (TransactionUtil.isCommonSwitchOpen(MapUtil.getString(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.COMMON_SWITCH), TransactionParam.TYPE_COMMON_SWITCH_CASH_DESK)) {
                transactionMap.put(MapUtil.getString(transaction, Transaction.TSN), transaction);
            }
        });
        List<String> tsns = new ArrayList(transactionMap.keySet());
        Lists.partition(tsns, 1000)
            .stream()
            .forEach(pt -> {
                cashDeskTradeService.getCashDeskIdByIds(pt).forEach((tsn, cashDeskId) -> {
                    Map<String, Object> transaction = transactionMap.get(tsn);
                    transaction.put("cash_desk_id", cashDeskId);
                    transaction.put("cash_desk_name", getCashDeskNameFormCache(cashDeskId));
                });
            });
    }

    private void setCashDesk(TransactionVo transactionVo){
        String tsn = transactionVo.getTsn();

        String commonSwitchStr = transactionVo.getCommonSwitch();

        //开通了收银台的商户才会补全收银台相关信息
        if (!TransactionUtil.isCommonSwitchOpen(commonSwitchStr, TransactionParam.TYPE_COMMON_SWITCH_CASH_DESK)) {
            return;
        }

        Map<String, Object> cashDeskInfo = buildCashDeskTradeInfo(tsn);
        transactionVo.setCashDeskId(BeanUtil.getPropString(cashDeskInfo, DaoConstants.ID));
        transactionVo.setCashDeskName(BeanUtil.getPropString(cashDeskInfo, CashDesk.NAME));
    }

    public String getCashDeskIdByTsn(String tsn) {

        CashDeskTransaction transaction = null;
        //通过收银台流水服务 获取收银台id
        try {
            transaction = cashDeskTradeService.getCashDeskTradeById(tsn);
        } catch (UpayBizException exception) {
            log.error("查询收银台流水异常");
        }
        if (Objects.isNull(transaction)) {
            return null;
        }
        return transaction.getCashDeskId();
    }

    public Map<String, Object> buildCashDeskTradeInfo(String tsn) {

        Map<String, Object> cashDeskTradeInfo = new HashMap<>();
        String cashDeskId = getCashDeskIdByTsn(tsn);

        if (StringUtils.isEmpty(cashDeskId)) {
            return null;
        }
        cashDeskTradeInfo.put(Transaction.TSN, tsn);
        cashDeskTradeInfo.put(DaoConstants.ID, cashDeskId);
        cashDeskTradeInfo.put(CashDesk.NAME, getCashDeskNameFormCache(cashDeskId));

        return cashDeskTradeInfo;
    }

    //根据收银台id获取最新的收银台名称
    public String getCashDeskNameFormCache(String cashDeskId) {
        Map<String, Object> cashDeskInfo = cacheService.getCacheMap(CommonConstant.CACHE_KEY_CASH_DESK, cashDeskId);
        if (MapUtil.isEmpty(cashDeskInfo)) {
            cashDeskInfo = cashDeskService.getSimpleCashDeskById(cashDeskId);
            cacheService.setCache(CommonConstant.CACHE_KEY_CASH_DESK, cashDeskId, cashDeskInfo, TimeUnit.MINUTES, 15);
        }
        return MapUtil.getString(cashDeskInfo, CashDesk.NAME);
    }

    //根据收银台id和时间获取交易流水
    public List<Map<String, Object>> queryCashDeskTransactions(long start, long end, int pageSize, String cashDeskId) {
        TransactionHBaseQuery hBaseQuery = new TransactionHBaseQuery();
        hBaseQuery.setCashDeskIds(Arrays.asList(cashDeskId));
        hBaseQuery.setQueryCashDesk(true);
        hBaseQuery.setStartTime(start);
        hBaseQuery.setEndTime(end);
        hBaseQuery.setLimit(pageSize);
        hBaseQuery.getOrderBys().add(new OrderBy("ctime", OrderBy.OrderType.ASC));
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
        statusTypeSubPayWayQuery.setStatusList(Arrays.asList(Transaction.STATUS_SUCCESS));
        hBaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
        return transactionHBaseDao.queryList(hBaseQuery);
    }
}