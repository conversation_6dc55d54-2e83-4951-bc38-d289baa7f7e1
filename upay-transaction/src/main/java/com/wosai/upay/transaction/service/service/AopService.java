package com.wosai.upay.transaction.service.service;

import com.google.common.collect.Lists;
import com.wosai.upay.transaction.constant.AopStateCodeConst;
import com.wosai.upay.transaction.constant.AopTargetTypeConst;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.enums.TransactionStatus;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.SwipeCardReceiveRequest;
import com.wosai.upay.transaction.model.SwipeCardReceiveResponse;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionSumV;
import com.wosai.upay.transaction.util.TransactionTypeRelatedUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * 对接 AOP 系统的业务逻辑类
 *
 * <AUTHOR>
 */
@Service
public class AopService {

    /**
     * 一百倍（可用于分、元转换）
     */
    private static final BigDecimal HUNDREDFOLD = BigDecimal.valueOf(100);

    /**
     * 默认交易笔数 0
     */
    private static final String DEFAULT_TRADE_COUNT = "0";

    /**
     * 默认交易金额 0
     */
    private static final String DEFAULT_ORIGINAL_AMOUNT = "0";

    @Autowired
    private IAccountBookBaseService accountBookBaseService;

    /**
     * 获取刷卡收款金额
     */
    public SwipeCardReceiveResponse getSwipeCardOriginalAmount(SwipeCardReceiveRequest request) {

        LocalDateTime requestDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(request.getTimestamp()), ZoneId.systemDefault());
        ZonedDateTime startDateTime;
        // 刷卡交易的统计时间段为昨天23点至今天22点59分59秒
        if (requestDateTime.getHour() < 23) {
            startDateTime = requestDateTime.toLocalDate().atStartOfDay(ZoneId.systemDefault()).plusHours(-1);
        } else {
            startDateTime = requestDateTime.toLocalDate().atStartOfDay(ZoneId.systemDefault()).plusHours(23);
        }

        List<TransactionSumV> transactionSumVList;
        try {
            //  选择刷卡交易库
            DBSelectContext.getContext().initDB(UpayQueryType.UPAY_SWIPE.getCode());

            // 从数据库查询刷卡收款金额
            TransactionQuery query = new TransactionQuery();
            query.setMerchantId(request.getMerchant_id());
            long startTime = startDateTime.toInstant().toEpochMilli();
            long endTime = startDateTime.plusDays(1).toInstant().toEpochMilli();
            query.setStartTime(startTime);
            query.setEndTime(endTime);
            StatusTypeSubPayWayQuery condition = new StatusTypeSubPayWayQuery();
            condition.setStatusList(Lists.newArrayList(TransactionStatus.SUCCESS.getCode()));
            condition.setTypeList(TransactionTypeRelatedUtil.SWIPE_QUERY_TYPE);
            query.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, condition);
            transactionSumVList = this.accountBookBaseService.summary(query);
        } finally {
            DBSelectContext.removeContext();
        }

        if (CollectionUtils.isEmpty(transactionSumVList)) {
            return getDefaultResponse(request);
        }

        long tradeCount = 0;
        long originalAmountFen = 0;
        for (TransactionSumV transactionSumV : transactionSumVList) {
            tradeCount += transactionSumV.getSalesCount();
            originalAmountFen += transactionSumV.getSalesAmount();
        }

        // 格式化小数点后两位，RoundingMode.HALF_UP 不会影响值的精确性
        String originalAmountYuan = BigDecimal.valueOf(originalAmountFen).divide(HUNDREDFOLD, 2, RoundingMode.HALF_UP).toString();

        return new SwipeCardReceiveResponse()
                .setState_code(AopStateCodeConst.BANKCARD_PAY_DAILY_AMOUNT)
                .setTarget_type(AopTargetTypeConst.MERCHANT_ID_TYPE)
                .setTarget_id(request.getMerchant_id())
                .setData(new SwipeCardReceiveResponse.Data().setTrade_count(Long.toString(tradeCount)).setOriginal_amount(originalAmountYuan))
                .setTimestamp(request.getTimestamp());
    }

    private SwipeCardReceiveResponse getDefaultResponse(SwipeCardReceiveRequest request) {
        return new SwipeCardReceiveResponse()
                .setState_code(AopStateCodeConst.BANKCARD_PAY_DAILY_AMOUNT)
                .setTarget_id(request.getMerchant_id())
                .setTarget_type(AopTargetTypeConst.MERCHANT_ID_TYPE)
                .setData(new SwipeCardReceiveResponse.Data().setTrade_count(DEFAULT_TRADE_COUNT).setOriginal_amount(DEFAULT_ORIGINAL_AMOUNT))
                .setTimestamp(request.getTimestamp());
    }

}
