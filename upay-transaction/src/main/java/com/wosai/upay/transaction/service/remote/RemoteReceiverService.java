package com.wosai.upay.transaction.service.remote;


import com.googlecode.jsonrpc4j.JsonRpcService;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/8/21.
 */
@JsonRpcService("/rpc/receiver")
public interface RemoteReceiverService {

    /**
     * 根据接收方Id列表获取接收方信息
     * @param receiverIds
     * @return
     */
    Map<String, Map<String, String>> getReceiverTypeAndAliasByIds(List<String> receiverIds);
}
