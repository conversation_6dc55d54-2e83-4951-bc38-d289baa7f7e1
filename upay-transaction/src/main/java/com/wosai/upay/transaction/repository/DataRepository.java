package com.wosai.upay.transaction.repository;

import com.wosai.data.dao.Dao;
import com.wosai.upay.common.util.SpringContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class DataRepository {

    private Dao<Map<String, Object>> statementConfigDao;

    private JdbcTemplate statementJdbcTemplate;

    public DataRepository(@Autowired
                          JdbcTemplate statementJdbcTemplate,
                          @Autowired
                          @Qualifier("statementConfigDao")
                          Dao<Map<String, Object>> statementConfigDao) {
        this.statementConfigDao = statementConfigDao;
        this.statementJdbcTemplate = statementJdbcTemplate;
    }

    public Dao<Map<String, Object>> getStatementConfigDao() {
        return statementConfigDao;
    }

    public void setStatementConfigDao(Dao<Map<String, Object>> statementConfigDao) {
        this.statementConfigDao = statementConfigDao;
    }

    public JdbcTemplate getStatementJdbcTemplate() {
        return statementJdbcTemplate;
    }

    public void setStatementJdbcTemplate(JdbcTemplate statementJdbcTemplate) {
        this.statementJdbcTemplate = statementJdbcTemplate;
    }

}
