package com.wosai.upay.transaction.util;

import com.google.common.collect.Lists;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.model.Transaction;
import org.apache.commons.collections.MapUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 处理和transaction  type相关判断的util
 */
public class TransactionTypeRelatedUtil {
    /**
     * 流水重写支付方式
     */
    public static void rewritePayWayForStore(List<Map<String, Object>> transactions) {
//        if (transactions != null) {
//            for (Map<String, Object> transaction : transactions) {
//                int payWay = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
                //新的储值支付 改为原来的礼品卡支付
//                if (payWay == Order.PAYWAY_STORE) {
//                    transaction.put(Transaction.PAYWAY, Order.PAYWAY_GIFT_CARD);
//                }
//            }
//        }
    }


//    ====================================  支付网关接口相关逻辑开始  ====================================
    /**
     * 支付网关的paymentType
     */
    public static List<Integer> GATEWAY_ALL_TYPE = Lists.newArrayList(
            Transaction.TYPE_PAYMENT, Transaction.TYPE_REFUND_REVOKE, Transaction.TYPE_REFUND
            , Transaction.TYPE_CANCEL, Transaction.TYPE_DEPOSIT_FREEZE, Transaction.TYPE_DEPOSIT_CANCEL
            , Transaction.TYPE_DEPOSIT_CONSUME, Transaction.TYPE_DEPOSIT_CONSUME_CANCEL,
            Transaction.TYPE_STORE_IN, Transaction.TYPE_STORE_IN_REFUND);
    public static List<Integer> PAY_TYPE_FOR_GATEWAY = Lists.newArrayList(Transaction.TYPE_PAYMENT, Transaction.TYPE_DEPOSIT_CONSUME, Transaction.TYPE_STORE_IN, Transaction.TYPE_STORE_PAY);
    public static List<Integer> REFUND_FOR_GATEWAY = Lists.newArrayList(Transaction.TYPE_REFUND, Transaction.TYPE_STORE_IN_REFUND, Transaction.TYPE_STORE_REFUND);
    // 需要转换类型的配置
    public static final Map<Integer, Integer> GATEWAY_TYPE_CONVERT = MapUtil.hashMap(Transaction.TYPE_STORE_IN, Transaction.TYPE_PAYMENT,
            Transaction.TYPE_STORE_IN_REFUND, Transaction.TYPE_REFUND,
            Transaction.TYPE_STORE_PAY, Transaction.TYPE_PAYMENT,
            Transaction.TYPE_STORE_REFUND, Transaction.TYPE_REFUND);

    //储值充值
    public static final Map<Integer, Integer> GATEWAY_TYPE_CONVERT_REVERSE = MapUtil.hashMap(Transaction.TYPE_PAYMENT, Transaction.TYPE_STORE_IN,
            Transaction.TYPE_REFUND, Transaction.TYPE_STORE_IN_REFUND);

    //储值核销
    public static final Map<Integer, Integer> GATEWAY_TYPE_CONVERT_REVERSE_2 = MapUtil.hashMap(Transaction.TYPE_PAYMENT, Transaction.TYPE_STORE_PAY,
            Transaction.TYPE_REFUND, Transaction.TYPE_STORE_REFUND);

    /**
     * 判断type是支付网关的支付类型
     */
    public static boolean isPayTypeForGateWay(Integer type) {
        return type != null && PAY_TYPE_FOR_GATEWAY.contains(type);
    }

//    ====================================  支付网关接口相关逻辑结束  ====================================


    /**
     * 顾客查询相关接口，支付类的查询过滤条件
     */
    public static List<Integer> PAY_TYPE_FOR_CUSTOMER_QUERY = Lists.newArrayList(Transaction.TYPE_DEPOSIT_CONSUME, Transaction.TYPE_PAYMENT, Transaction.TYPE_CANCEL, Transaction.TYPE_REFUND, Transaction.TYPE_STORE_IN, Transaction.TYPE_STORE_IN_REFUND);


    //订单查询接口使用，支付类型的接口,+几个新类型
    public static List<Integer> PAY_TYPE_FOR_TMP_QUERY = Arrays.asList(Transaction.TYPE_PAYMENT, Transaction.TYPE_DEPOSIT_FREEZE,
            Transaction.TYPE_CHARGE, Transaction.TYPE_ORDER_TAKE, Transaction.TYPE_STORE_PAY, Transaction.TYPE_STORE_IN);


    public static boolean isPayment(Integer type) {
        return type != null && PAY_TYPE.contains(type);
    }

    /**
     * 支付成功
     */
    private static List<Integer> PAY_TYPE = Lists.newArrayList(Transaction.TYPE_PAYMENT,
            Transaction.TYPE_DEPOSIT_CONSUME, Transaction.TYPE_CHARGE,
            Transaction.TYPE_ORDER_TAKE, Transaction.TYPE_STORE_PAY, Transaction.TYPE_STORE_IN
    );


    public static List<Integer> ORDER_QUERY_FILTER = Lists.newArrayList(
            Transaction.TYPE_DEPOSIT_CONSUME,
            Transaction.TYPE_PAYMENT,
            Transaction.TYPE_CANCEL, Transaction.TYPE_REFUND,
            Transaction.TYPE_CHARGE, Transaction.TYPE_CHARGE_REFUND,
            Transaction.TYPE_ORDER_TAKE, Transaction.TYPE_ORDER_TAKE_REFUND,
            Transaction.TYPE_STORE_PAY, Transaction.TYPE_STORE_REFUND,
            Transaction.TYPE_STORE_IN, Transaction.TYPE_STORE_IN_REFUND
    );


    /**
     * 刷卡交易需要筛选的type
     */
    public static List<Integer> SWIPE_QUERY_TYPE = Lists.newArrayList(Transaction.TYPE_CANCEL,
            Transaction.TYPE_REFUND, Transaction.TYPE_DEPOSIT_CONSUME_CANCEL,
            Transaction.TYPE_DEPOSIT_CONSUME, Transaction.TYPE_PAYMENT);


    public static boolean isRefundType(Integer type) {
        return type != null && IS_REFUND_TYPE.contains(type);
    }

    /**
     * 对账单导出时，退款的时 用于判断退款单号时使用
     */
    private static List<Integer> IS_REFUND_TYPE = Lists.newArrayList(
            Transaction.TYPE_REFUND, Transaction.TYPE_CHARGE_REFUND,
            Transaction.TYPE_ORDER_TAKE_REFUND, Transaction.TYPE_STORE_REFUND,
            Transaction.TYPE_STORE_IN_REFUND);


    public static List<Integer> getStatementTypesByInclude(String include) {
        List<Integer> result = Lists.newArrayList();
        if (include.contains(StatementTaskLog.INCLUDE_PAY + "")) {
            //移动支付类（支付网关的收款，储值核销和储值核销退款，不包括储值充值和储值充值退款）
            result.addAll(Lists.newArrayList(
                    //支付网关的收款
                    Transaction.TYPE_PAYMENT
                    , Transaction.TYPE_DEPOSIT_CONSUME
                    , Transaction.TYPE_CANCEL
                    , Transaction.TYPE_DEPOSIT_CONSUME_CANCEL
                    , Transaction.TYPE_REFUND
                    , Transaction.TYPE_REFUND_REVOKE
                    //储值核销和储值核销退款
                    , Transaction.TYPE_STORE_PAY
                    , Transaction.TYPE_STORE_REFUND
            ));
        }
        if (include.contains(StatementTaskLog.INCLUDE_CHARGE + "")) {
            result.addAll(Lists.newArrayList(
                    Transaction.TYPE_CHARGE
                    , Transaction.TYPE_CHARGE_REFUND
                    , Transaction.TYPE_ORDER_TAKE
                    , Transaction.TYPE_ORDER_TAKE_REFUND));
        }
        if (include.contains(StatementTaskLog.INCLUDE_STORE_IN + "")) {
            result.addAll(STORE_TYPE);
        }
        return result;
    }

    public static List<Integer> STORE_TYPE = Lists.newArrayList(Transaction.TYPE_STORE_IN,
            Transaction.TYPE_STORE_IN_REFUND);

    /**
     * 是否归属于储值类对账单计算的
     */
    public static boolean isStoreType(Integer type) {
        return type != null && STORE_TYPE.contains(type);
    }


    /**
     * 对账单需要导出流水类型
     */
    public static final List<Integer> STATEMENT_TYPES = Lists.newArrayList(
            Transaction.TYPE_PAYMENT
            , Transaction.TYPE_DEPOSIT_CONSUME
            , Transaction.TYPE_CANCEL
            , Transaction.TYPE_DEPOSIT_CONSUME_CANCEL
            , Transaction.TYPE_REFUND
            , Transaction.TYPE_REFUND_REVOKE

            , Transaction.TYPE_CHARGE
            , Transaction.TYPE_CHARGE_REFUND
            , Transaction.TYPE_ORDER_TAKE
            , Transaction.TYPE_ORDER_TAKE_REFUND

            , Transaction.TYPE_STORE_PAY
            , Transaction.TYPE_STORE_REFUND

            , Transaction.TYPE_STORE_IN
            , Transaction.TYPE_STORE_IN_REFUND
    );

    /**
     * 对账单导出，判断交易金额，笔数使用(正交易)
     */
    public static boolean isPositiveType(Integer type) {
        return type == Transaction.TYPE_DEPOSIT_CONSUME
                || type == Transaction.TYPE_PAYMENT
                || type == Transaction.TYPE_REFUND_REVOKE
                || type == Transaction.TYPE_CHARGE
                || type == Transaction.TYPE_ORDER_TAKE
                || type == Transaction.TYPE_STORE_PAY
                || type == Transaction.TYPE_STORE_IN;
    }

    /**
     * 对账单导出，计算退款笔数，金额使用(负交易)
     */
    public static boolean isNegativeType(Integer type) {
        return type != null && STATEMENT_TYPES.contains(type) && !isPositiveType(type);
    }

    /**
     * 流水导出使用，判断是否是储值充值或储值充值退款
     */
    public static boolean isStoreInOrRefund(Integer type) {
        return type != null && (Transaction.TYPE_STORE_IN == type || Transaction.TYPE_STORE_IN_REFUND == type);
    }

    /**
     * 流水导出使用，判断是否是储值核销或储值核销退款
     */
    public static boolean isStorePayOrRefund(Integer type) {
        return type != null && (Transaction.TYPE_STORE_PAY == type || Transaction.TYPE_STORE_REFUND == type);
    }

    /**
     * 流水导出使用，判断是否是记账或记账退款
     */
    public static boolean isChargePayOrRefund(Integer type) {
        return type != null && (Transaction.TYPE_CHARGE == type || Transaction.TYPE_CHARGE_REFUND == type);
    }

    public static Map<String, String> TYPE_LANGUAGE_KEY_MAP;
    public static Map<String, String> TYPE_LANGUAGE_KEY_MAP_IGNORE_DEPOSIT;

    static {
        TYPE_LANGUAGE_KEY_MAP = CollectionUtil.hashMap(
                "30", LanguageUtil.TRANSACTION_TYPE_PAYMENT,
                "31", LanguageUtil.REFUND_WITHDRAWAL,
                "11", LanguageUtil.TRANSACTION_TYPE_RETURN,
                "10", LanguageUtil.WITHDRAWAL,
                "32", LanguageUtil.TRANSACTION_DEPOSIT_FREEZE,
                "12", LanguageUtil.TRANSACTION_DEPOSIT_CANCEL,
                "13", LanguageUtil.TRANSACTION_DEPOSIT_CONSUME,
                "14", LanguageUtil.TRANSACTION_DEPOSIT_CONSUME_CANCEL,


                Transaction.TYPE_CHARGE + "", LanguageUtil.TRANSACTION_TYPE_PAYMENT,
                Transaction.TYPE_ORDER_TAKE + "", LanguageUtil.TRANSACTION_TYPE_PAYMENT,
                Transaction.TYPE_STORE_IN + "", LanguageUtil.TRANSACTION_TYPE_STORE_IN_PAYMENT,
                Transaction.TYPE_STORE_PAY + "", LanguageUtil.TRANSACTION_TYPE_STORE_PAY_PAYMENT,

                Transaction.TYPE_CHARGE_REFUND + "", LanguageUtil.TRANSACTION_TYPE_RETURN,
                Transaction.TYPE_ORDER_TAKE_REFUND + "", LanguageUtil.TRANSACTION_TYPE_RETURN,
                Transaction.TYPE_STORE_IN_REFUND + "", LanguageUtil.TRANSACTION_TYPE_STORE_IN_RETURN,
                Transaction.TYPE_STORE_REFUND + "", LanguageUtil.TRANSACTION_TYPE_STORE_PAY_RETURN

        );
        // 13 转30,14 转10
        TYPE_LANGUAGE_KEY_MAP_IGNORE_DEPOSIT = new HashMap<>(TYPE_LANGUAGE_KEY_MAP);
        TYPE_LANGUAGE_KEY_MAP_IGNORE_DEPOSIT.put("13", LanguageUtil.TRANSACTION_TYPE_PAYMENT);
        TYPE_LANGUAGE_KEY_MAP_IGNORE_DEPOSIT.put("14", LanguageUtil.WITHDRAWAL);
    }

    public static String getTransactionTypeLanguageKey(Object type) {
        if (type == null) {
            return null;
        }
        return TYPE_LANGUAGE_KEY_MAP.get(type + "");
    }

    public static String getTransactionTypeLanguageKeyIgnoreDeposit(Object type) {
        if (type == null) {
            return null;
        }
        return TYPE_LANGUAGE_KEY_MAP_IGNORE_DEPOSIT.get(type + "");
    }


}
