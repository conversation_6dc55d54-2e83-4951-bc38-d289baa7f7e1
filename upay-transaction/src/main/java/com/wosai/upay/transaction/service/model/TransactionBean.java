package com.wosai.upay.transaction.service.model;


import com.wosai.upay.transaction.constant.EsConstant;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.hadoop.hbase.util.Bytes;
import org.elasticsearch.common.collect.MapBuilder;

import java.util.Map;

@Data
@Accessors(chain = true)
public class TransactionBean {

    private String id;

    private String tsn;

    private String merchantId;

    private Object reflect;

    private String reflectStr;

    private long ctime;

    public byte[] rowKey() {
        return Bytes.add(Bytes.toBytes(getMerchantId()), Bytes.toBytes(getCtime()), Bytes.toBytes(getId()));
    }

    public Map build2Map() {
        MapBuilder mapBuilder = new MapBuilder();
        mapBuilder.put(EsConstant.ID, rowKey())
                .put(EsConstant.MERCHANT_ID, getMerchantId())
                .put(EsConstant.TSN, getTsn())
                .put(EsConstant.REFLECT, getReflectStr())
                .put(EsConstant.CTIME, getCtime());
        return mapBuilder.map();
    }
}