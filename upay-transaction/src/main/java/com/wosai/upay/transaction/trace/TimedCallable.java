package com.wosai.upay.transaction.trace;


import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.Trace;

import java.util.concurrent.Callable;

public class TimedCallable<V> implements Callable<V> {
    private Callable<V> callable;
    private String name;

    public TimedCallable(String name, Callable<V> callable) {
        this.name = name;
        this.callable = callable;
    }

    public static <V> TimedCallable<V> of(String name, Callable<V> r) {
        return new TimedCallable(name, r);
    }

    public V call() throws Exception {
        return callInTime(name);
    }

    @Timed(value = "arg[0]")
    @Trace
    public V callInTime(String name) throws Exception {
        return this.callable.call();
    }

}
