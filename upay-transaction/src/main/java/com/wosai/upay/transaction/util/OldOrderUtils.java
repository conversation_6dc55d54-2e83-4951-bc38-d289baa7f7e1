package com.wosai.upay.transaction.util;

import com.google.common.collect.ImmutableMap;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.model.Order;
import com.wosai.upay.transaction.constant.OldOrderStatusConst;
import com.wosai.upay.transaction.service.model.UpayOldOrder;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 关于旧订单的工具类，比如把订单的状态值转为很久很久之前的旧状态值
 *
 * <AUTHOR>
 */
@Slf4j
public final class OldOrderUtils {

    /**
     * 新支付方式到旧支付方式的对账表
     */
    private static final Map<Integer, String> PAY_WAY_MAP = CollectionUtil.hashMap(
            Order.PAYWAY_ALIPAY, UpayOldOrder.PAY_WAY_ALIPAY,
            Order.PAYWAY_ALIPAY2, UpayOldOrder.PAY_WAY_ALIPAY_OPEN,
            Order.PAYWAY_WEIXIN, UpayOldOrder.PAY_WAY_WEIXIN,
            Order.PAYWAY_JDWALLET, UpayOldOrder.PAY_WAY_JD,
            Order.PAYWAY_BAIFUBAO, UpayOldOrder.PAY_WAY_BAIFUBAO,
            Order.PAYWAY_QQWALLET, UpayOldOrder.PAY_WAY_QQ,
            Order.PAYWAY_NFC, UpayOldOrder.PAY_WAY_NFC,
            Order.PAYWAY_LKL_WEIXIN, UpayOldOrder.PAY_WAY_WEIXIN,
            17, UpayOldOrder.PAY_WAY_UNIONPAY,
            18, "BESTPAY"
    );

    /**
     * 支付方式名称
     */
    private static final ImmutableMap<String, String> PAY_WAY_TEXT_MAP = ImmutableMap.<String, String>builder()
            .put("ALIPAY", "支付宝")
            .put("ALIPAY_OPEN", "支付宝")
            .put("WEIXIN", "微信")
            .put("BAIFUBAO", "百度钱包")
            .put("JD", "京东钱包")
            .put("QQ", "QQ钱包")
            .put("NFC", "云闪付")
            .put("UNIONPAY", "银联二维码")
            .put("CASH", "现金支付")
            .put("BANKCARD", "刷卡支付")
            .put("BESTPAY", "翼支付")
            .build();

    public static int toOldOrderStatus(int newOrderStatus) {
        if (newOrderStatus == com.wosai.upay.model.Order.STATUS_CREATED) {
            return OldOrderStatusConst.ORDER_STATUS_NOTPAY;
        }

        if (newOrderStatus == com.wosai.upay.model.Order.STATUS_PAID
                || newOrderStatus == com.wosai.upay.model.Order.STATUS_REFUND_INPROGRESS
                || newOrderStatus == com.wosai.upay.model.Order.STATUS_REFUND_ERROR
                || newOrderStatus == com.wosai.upay.model.Order.STATUS_CANCEL_ERROR
                || newOrderStatus == com.wosai.upay.model.Order.STATUS_CANCEL_INPROGRESS) {
            return OldOrderStatusConst.ORDER_STATUS_PAY_FINISHED;
        }

        if (newOrderStatus == com.wosai.upay.model.Order.STATUS_REFUNDED
                || newOrderStatus == com.wosai.upay.model.Order.STATUS_PARTIAL_REFUNDED) {
            return OldOrderStatusConst.ORDER_STATUS_REFUND;
        }

        if (newOrderStatus == com.wosai.upay.model.Order.STATUS_CANCELED
                || newOrderStatus == com.wosai.upay.model.Order.STATUS_PAY_CANCELED
                || newOrderStatus == com.wosai.upay.model.Order.STATUS_PAY_ERROR) {
            return OldOrderStatusConst.ORDER_STATUS_CLOSED;
        }

        log.warn("can not change new order status ${newOrderStatus} to old order status");
        return newOrderStatus;
    }

    public static String toOldPayWay(int newPayWay) {
        return PAY_WAY_MAP.get(newPayWay);
    }

    /**
     * @param oldStatus 旧状态
     * @param originFee 单位分
     * @param totalFee  单位分
     * @return refundFee 元
     */
    public static String getRefundFee(String oldStatus, long originFee, long totalFee) {
        if ("1".equals(oldStatus)) {
            return "0";
        }
        if ("2".equals(oldStatus)) {
            return String.valueOf((originFee - totalFee) / 100.0);
        }
        if ("3".equals(oldStatus)) {
            return String.valueOf(originFee / 100.0);
        }
        if ("404".equals(oldStatus)) {
            return "0";
        }
        return "0";
    }

    public static String getPayWayText(String oldPayWay) {
        return PAY_WAY_TEXT_MAP.get(oldPayWay);
    }

}
