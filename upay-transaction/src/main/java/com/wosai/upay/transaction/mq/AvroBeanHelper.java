package com.wosai.upay.transaction.mq;

import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.model.TransactionBean;
import com.wosai.upay.transaction.util.TransactionUtil;
import lombok.SneakyThrows;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections.MapUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 21/03/2018.
 */

public class AvroBeanHelper {

    @SneakyThrows
    public static TransactionBean getTransactionMapFromBean(ConsumerRecord<?, GenericRecord> record) {
        String value = String.valueOf(record.value());
        if (StringUtil.isBlank(value)) {
            return null;
        }
        Map<String, Object> recordMap = JsonUtil.jsonStrToObject(value, Map.class);

        TransactionBean transactionBean = new TransactionBean();
        transactionBean.setId(MapUtils.getString(recordMap, DaoConstants.ID));
        transactionBean.setTsn(MapUtils.getString(recordMap, Transaction.TSN));
        transactionBean.setMerchantId(MapUtils.getString(recordMap, Transaction.MERCHANTID));
        transactionBean.setReflect(MapUtils.getObject(recordMap, Transaction.REFLECT));
        transactionBean.setCtime(MapUtils.getLongValue(recordMap, DaoConstants.CTIME));
        return transactionBean;
    }

    public static void enhanceTransactionBean(TransactionBean transactionBean) {
        String reflectStr = TransactionUtil.convertReflectStr(transactionBean.getReflect());
        if (StringUtil.isNotBlank(reflectStr)) {
            transactionBean.setReflectStr(reflectStr);
        }
    }
}