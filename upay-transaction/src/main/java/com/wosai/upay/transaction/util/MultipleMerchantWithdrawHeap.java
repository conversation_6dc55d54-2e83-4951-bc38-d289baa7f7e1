package com.wosai.upay.transaction.util;

import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.upay.Withdraw;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.wosai.upay.transaction.constant.DatabaseQueryConstant;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


/**集团结算对账单 按商户维度拆分处理
 * <AUTHOR>
 */
public class MultipleMerchantWithdrawHeap {
    public static final Logger logger = LoggerFactory.getLogger(MultipleMerchantWithdrawHeap.class);
    private Map<List<String>, WithdrawCondition> merchantCounter = Maps.newConcurrentMap();
    private Integer DEFAULT_BATCH_SIZE = 5000;
    private Map commonQueryFilter;
    private WithdrawExportUtils withdrawExportUtils;
    private List<OrderBy> orderBy;
    private PriorityQueue withdrawQueue;

    public MultipleMerchantWithdrawHeap(List<String> merchantIds, Map queryFilter, WithdrawExportUtils withdrawExportUtils) {
        this.orderBy = Collections.singletonList(new OrderBy(ConstantUtil.KEY_CTIME, OrderBy.OrderType.DESC));
        this.withdrawQueue = new PriorityQueue((o1, o2) -> NumberUtils.compare(BeanUtil.getPropLong((o2), orderBy.get(0).getField()), BeanUtil.getPropLong(o1, orderBy.get(0).getField())));
        addMerchant(merchantIds, BeanUtil.getPropLong(queryFilter,"date_end"));
        this.commonQueryFilter = queryFilter;
        this.withdrawExportUtils = withdrawExportUtils;
        merchantCounter.forEach((k, v) -> {
            supplementData(k);
        });
    }

    private void addMerchant(List<String> merchantIds, Long dateEnd){
        Lists.partition(merchantIds, ConfigService.getAppConfig().getIntProperty("group_divide_threshold", 400)).forEach(subMerchantIds -> merchantCounter.put(subMerchantIds, new WithdrawCondition(subMerchantIds, dateEnd)));
    }

    public Object getWithdraw() {
        Map top = ((Map) withdrawQueue.poll());
        String merchant_id = BeanUtil.getPropString(top, Withdraw.MERCHANT_ID);
        List<String> subMerchantIds = merchantCounter.keySet().stream().filter(l -> l.contains(merchant_id)).findFirst().orElse(null);
        if (Objects.nonNull(subMerchantIds)){
            WithdrawCondition withdrawCondition = merchantCounter.get(subMerchantIds);
            Long endTime = CommonUtil.getTime(top);
            String id = MapUtil.getString(top, DaoConstants.ID);
            //记录边界数据
            if (endTime == withdrawCondition.nextEnd){
                withdrawCondition.addEdgeRecord(id);
            }
            if (withdrawCondition.getCount().decrementAndGet() == 0){
                supplementData(subMerchantIds);
            }
        }
        return top;
    }

    private void supplementData(List<String> merchantIds) {
        WithdrawCondition withdrawCondition = merchantCounter.get(merchantIds);
        if (Objects.isNull(withdrawCondition)){
            return;
        }
        List<Map<String, Object>> withdrawList = withdrawCondition.supplementData();
        if (CollectionUtils.isNotEmpty(withdrawList)) {
            withdrawQueue.addAll(withdrawList);
        } else{
            merchantCounter.remove(merchantIds);
        }
    }

    public List getWithdrawList(){
        if (withdrawQueue.isEmpty()){
            return Lists.newArrayList();
        }
        Integer batchSize = DEFAULT_BATCH_SIZE;
        List res = Lists.newLinkedList();
        int size = withdrawQueue.size();
        int min = Math.min(size, batchSize);
        for (int i = 0; i < min; i++) {
            res.add(getWithdraw());
        }
        return res;
    }

    @Data
    private class WithdrawCondition {
        private AtomicInteger count = new AtomicInteger(0);
        private long nextEnd = Long.MAX_VALUE;
        private List<String> merchantIds;
        private List<String> edgeRecordList = Lists.newLinkedList();

        public WithdrawCondition(List<String> merchantIds, long nextEnd) {
            this.merchantIds = merchantIds;
            this.nextEnd = nextEnd;
        }

        public List<Map<String, Object>> supplementData(){
            if (CollectionUtils.isEmpty(merchantIds)){
                return null;
            }
            Map mapParam = buildQueryParams();
            List<Map<String, Object>> withdrawList = retryGetMerchantWithdrawList(mapParam);
            if (CollectionUtils.isNotEmpty(withdrawList)) {
                if (withdrawList.size() == DEFAULT_BATCH_SIZE) {
                    long firstRecordTime = CommonUtil.getTime(withdrawList.get(0));
                    long lastRecordTime = CommonUtil.getTime(withdrawList.get(withdrawList.size() - 1));
                    if (firstRecordTime == lastRecordTime){
                        logger.error("(单毫秒内) 指定商户集合的结算记录超过批次上限， merchantIds:{}, batch:{}, queryFilter:{}", merchantIds, withdrawList.size(), mapParam);
                        throw new RuntimeException("单毫秒内的结算记录超过上限，程序执行异常。");
                    }
                }
                //去重边界数据,维护边界map
                CommonUtil.removeDuplicatesRecord(withdrawList, edgeRecordList, nextEnd);
                edgeRecordList.clear();
                if (CollectionUtils.isNotEmpty(withdrawList)){
                    count.addAndGet(withdrawList.size());
                    nextEnd = CommonUtil.getTime(withdrawList.get(withdrawList.size() - 1));
                }
            }
            return withdrawList;
        }

        private Map buildQueryParams(){
            Map<String, Object> map = Maps.newHashMap();
            Map<Object, Object> innerQueryFilter = Maps.newHashMap();
            innerQueryFilter.putAll(commonQueryFilter);
            map.put("queryFilter", innerQueryFilter);
            innerQueryFilter.put("merchant_ids", merchantIds);
            map.put("nextEndTime", nextEnd + 1);
            return map;
        }

        public void addEdgeRecord(String id){
            edgeRecordList.add(id);
        }

        private List<Map<String, Object>> retryGetMerchantWithdrawList(Map mapParam){
            Long paramNextEndTime = BeanUtil.getPropLong(mapParam, "nextEndTime");
            Long paramStart = ((Long) BeanUtil.getNestedProperty(mapParam, "queryFilter.date_start"));
            PageInfo pageInfo = new PageInfo(1, DatabaseQueryConstant.MAX_PAGE_SIZE_MYSQL_LIMIT, paramStart, paramNextEndTime, orderBy);
            try {
                return withdrawExportUtils.retryGetMerchantWithdrawList(pageInfo, (Map) BeanUtil.getProperty(mapParam, "queryFilter"));
            } catch (Exception e) {
                logger.error("get withdraw error. params ={}", mapParam);
                throw new RuntimeException(e);
            }
        }
    }

}
