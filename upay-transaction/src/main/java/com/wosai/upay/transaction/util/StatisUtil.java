package com.wosai.upay.transaction.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.text.DecimalFormat;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class StatisUtil {

    public static final Logger logger = LoggerFactory.getLogger(StatisUtil.class);

    private static ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(1);

    private static List<StatisticCallback> statisticCallbacks = new CopyOnWriteArrayList();

    static {
        scheduledExecutor.scheduleAtFixedRate(() -> {
            try {
                //logMemoryStatistic();
                traceStatisticCallback();
            } catch (Exception var1) {
                logger.error("执行定时任务的过程出现异常", var1);
            }

        }, 10000L, 1000L, TimeUnit.MILLISECONDS);
    }


    public static void registerStatisticCallback(StatisticCallback callback) {
        if (callback != null) {
            statisticCallbacks.add(callback);
        }
    }

    private static void logMemoryStatistic() {
        logger.info("[upay-transaction-memoryStatistic] {} ", new Object[]{memoryStatistic()});
    }

    private static String memoryStatistic() {
        Runtime runtime = Runtime.getRuntime();
        double freeMemory = (double)runtime.freeMemory() / 1048576.0D;
        double maxMemory = (double)runtime.maxMemory() / 1048576.0D;
        double totalMemory = (double)runtime.totalMemory() / 1048576.0D;
        double usedMemory = totalMemory - freeMemory;
        double percentFree = (maxMemory - usedMemory) / maxMemory * 100.0D;
        double percentUsed = 100.0D - percentFree;
        DecimalFormat mbFormat = new DecimalFormat("#0.00");
        DecimalFormat percentFormat = new DecimalFormat("#0.0");
        StringBuilder sb = new StringBuilder();
        sb.append(mbFormat.format(usedMemory)).append("MB of ").append(mbFormat.format(maxMemory)).append(" MB (").append(percentFormat.format(percentUsed)).append("%) used");
        return sb.toString();
    }

    private static void traceStatisticCallback() {
        statisticCallbacks.stream().forEach((item) -> {
            item.statisticCallback();
        });
    }
}
