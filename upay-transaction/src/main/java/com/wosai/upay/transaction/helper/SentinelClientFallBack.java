package com.wosai.upay.transaction.helper;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;

import java.lang.reflect.Method;

public class SentinelClientFallBack extends JsonRPCFallbackDefine {
    @Override
    public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
        return new JsonRPCMethodFallbackHandler[]{new JsonRPCMethodFallbackHandler() {
            public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                return ElementMatchers.any();
            }

            @Override
            public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                com.wosai.upay.transaction.exception.BlockException t = new com.wosai.upay.transaction.exception.BlockException("请求过于频繁，请稍后再试");
                t.setStackTrace(exception.getStackTrace());
                throw t;
            }
        }};
    }

    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return ElementMatchers.any();
    }

    @Override
    public Provider getProvider() {
        return Provider.CLIENT;
    }
}