package com.wosai.upay.transaction.service.routing;

import com.wosai.upay.transaction.service.dao.mysql.OrderMySQLDao;
import com.wosai.upay.transaction.service.dao.hbase.OrderHBaseDao;
import com.wosai.upay.transaction.service.model.query.OrderHBaseQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Service that routes order queries between MySQL and HBase/Solr
 * based on feature toggle configuration
 */
@Service
public class RoutingOrderService {

    private static final Logger logger = LoggerFactory.getLogger(RoutingOrderService.class);

    @Autowired
    private OrderMySQLDao orderMySQLDao;

    @Autowired
    private OrderHBaseDao orderHBaseDao;

    @Autowired
    private QueryRoutingService queryRoutingService;

    public List<Map<String, Object>> queryList(OrderHBaseQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for order queryList, merchant: {}", merchantId);
            return orderMySQLDao.queryList(query);
        } else {
            logger.info("Using HBase/Solr for order queryList, merchant: {}", merchantId);
            return orderHBaseDao.queryList(query);
        }
    }

    public Long count(OrderHBaseQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for order count, merchant: {}", merchantId);
            return orderMySQLDao.count(query);
        } else {
            logger.info("Using HBase/Solr for order count, merchant: {}", merchantId);
            return orderHBaseDao.count(query);
        }
    }

    public Map<String, Object> queryBySn(String merchantId, String sn, long start, long end) {
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for order queryBySn, merchant: {}", merchantId);
            return orderMySQLDao.queryBySn(merchantId, sn, start, end);
        } else {
            logger.info("Using HBase/Solr for order queryBySn, merchant: {}", merchantId);
            return orderHBaseDao.queryBySn(merchantId, sn, start, end);
        }
    }

    private String getFirstMerchantId(OrderHBaseQuery query) {
        if (query.getMerchantIds() != null && !query.getMerchantIds().isEmpty()) {
            return query.getMerchantIds().get(0);
        }
        return null;
    }
}