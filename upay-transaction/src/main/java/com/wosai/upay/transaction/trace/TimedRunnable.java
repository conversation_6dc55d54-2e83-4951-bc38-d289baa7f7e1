package com.wosai.upay.transaction.trace;

import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.Trace;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/9/14.
 */
public class TimedRunnable implements Runnable{
    private String name;
    private Runnable runnable;

    public TimedRunnable(String name, Runnable runnable){
        this.name = name;
        this.runnable = runnable;
    }

    public static TimedRunnable of(String name, Runnable runnable){
        return new TimedRunnable(name, runnable);
    }


    @Override
    public void run() {
        runInTimed(name);
    }

    @Timed(value = "arg[0]")
    @Trace
    private void runInTimed(String name){
        runnable.run();
    }
}
