package com.wosai.upay.transaction.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.wosai.common.utils.WosaiJsonUtils;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Hex;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.solr.client.solrj.util.ClientUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

public class SolrUtils {

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static final String SEPARATOR_OR = " OR ";

    public static final String SEPARATOR_AND = " AND ";

    public static final String SEPARATOR_SPACE = " ";


    public static String formatEqualFq(String field, Object value) {
        return field + ":" + value;
    }

    public static String formatNullFq(String field) {
        return "-" + field + ":[* TO *]";
    }


    public static String formatNotEqualFq(String field, Object value) {
        return "-" + field + ":" + value;
    }

    public static String formatNotEmpty(String field) {
        return String.format("{!func}exists(%s)", field);
    }

    public static String formatInFq(String field, Collection<?> values) {
        return field + ":" + bracketsWrapper(Joiner.on(SEPARATOR_SPACE).join(values));
    }


    public static String formatRangeFq(String field, Object start, Object end, boolean include) {
        String rangeFq = "";
        if (!Objects.isNull(start) && !Objects.isNull(end)) {
            rangeFq = field + ":[" + start + " TO " + end + (include ? "]" : "}");
        } else if (!Objects.isNull(start)) {
            rangeFq = field + ":[" + start + " TO * }";
        } else if (!Objects.isNull(end)) {
            rangeFq = field + ":[ * TO " + end + (include ? "]" : "}");
        }
        return rangeFq;
    }


    public static String formatFqList(List<String> fqs) {
        return bracketsWrapper(Joiner.on(SEPARATOR_AND).join(fqs));
    }


    public static String formatNotInFq(String field, Collection<?> values) {
        return " NOT " + field + ":" + bracketsWrapper(Joiner.on(SEPARATOR_SPACE).join(values));
    }


    public static String formatLikeInFq(String field, Collection<?> values) {
        Set<String> orderSns = values.stream().map(value -> field + ":" + "*" + value + "*").collect(Collectors.toSet());
        return bracketsWrapper(Joiner.on(SEPARATOR_OR).join(orderSns));
    }

    public static String formatLeftLikeInFq(String field, Collection<?> values) {
        Set<String> orderSns = values.stream().map(value -> field + ":" + "*" + value).collect(Collectors.toSet());
        return bracketsWrapper(Joiner.on(SEPARATOR_OR).join(orderSns));
    }


    @SneakyThrows
    public static Pair<byte[], Long> formatId(Pair<String, Long> idCtime) {
        byte[] bytes = Hex.decodeHex(idCtime.fst.toCharArray());
        return Pair.of(bytes, idCtime.snd);
    }


    public static Long double2Long(Object value) {
        if (value == null) {
            return null;
        }
        return ((Double) value).longValue();
    }

    public static long double2LongValue(Object value) {
        if (value == null) {
            return 0;
        }
        return ((Double) value).longValue();
    }

    public static Integer double2Integer(Object value) {
        if (value == null) {
            return null;
        }
        return ((Double) value).intValue();
    }

    public static Integer double2IntValue(Object value) {
        if (value == null) {
            return 0;
        }
        return ((Double) value).intValue();
    }


    @SneakyThrows
    public static Object bytes2Object(byte[] bytes) {
        if (ObjectUtils.isEmpty(bytes)) {
            return null;
        }
        return objectMapper.readValue(bytes, Object.class);
    }


    public static byte[] convert2ByteArray(Object value, Class clazz) {
        if (clazz.isAssignableFrom(Long.class)) {
            return Bytes.toBytes(Long.parseLong(value.toString()));
        } else if (clazz.isAssignableFrom(Map.class) || clazz.isAssignableFrom(LinkedHashMap.class)) {
            return Bytes.toBytes(WosaiJsonUtils.toJSONString(value));
        } else if (clazz.isAssignableFrom(Integer.class)) {
            return Bytes.toBytes(Integer.valueOf(value.toString()));
        } else {
            return Bytes.toBytes(String.valueOf(value));
        }
    }

    public static byte[] byteMerger(byte[] bt1, byte[] bt2) {
        byte[] bt3 = new byte[bt1.length + bt2.length];
        System.arraycopy(bt1, 0, bt3, 0, bt1.length);
        System.arraycopy(bt2, 0, bt3, bt1.length, bt2.length);
        return bt3;
    }


    public static String bracketsWrapper(String value) {
        return "(" + value + ")";
    }

    public static String escapeQueryChars(String value) {
        if (value == null || value.length() == 0) {
            return value;
        }

        return ClientUtils.escapeQueryChars(value);
    }

    public static List<String> escapeQueryChars(List<String> values) {
        if (values == null || values.size() == 0) {
            return values;
        }

        return values.stream()
                .filter(Objects::nonNull)
                .map(ClientUtils::escapeQueryChars)
                .collect(Collectors.toList());
    }

    public static Set<String> escapeQueryChars(Set<String> values) {
        if (values == null || values.size() == 0) {
            return values;
        }

        return values.stream()
                .filter(Objects::nonNull)
                .map(ClientUtils::escapeQueryChars)
                .collect(Collectors.toSet());
    }
}
