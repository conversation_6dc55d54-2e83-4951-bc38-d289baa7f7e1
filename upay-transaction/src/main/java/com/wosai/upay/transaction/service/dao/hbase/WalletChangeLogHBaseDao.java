package com.wosai.upay.transaction.service.dao.hbase;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.service.model.metadata.WalletChangeLogHBaseColumns;
import com.wosai.upay.transaction.service.model.metadata.WalletChangeLogSolrFields;
import com.wosai.upay.transaction.service.model.po.WalletChangeLogPo;
import com.wosai.upay.transaction.service.model.query.WalletChangeLogQuery;
import com.wosai.upay.transaction.util.*;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.hbase.util.Triple;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.YearMonth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * WalletChangeLogHBaseDao
 *
 * <AUTHOR>
 */
@Repository
public class WalletChangeLogHBaseDao {

    /**
     * Solr Collection 前缀
     */
    private static final String SOLR_COLLECTION_PREFIX;
    /**
     * HBase Namespace
     */
    private static final String HBASE_NAMESPACE;
    /**
     * HBase 表名前缀
     */
    private static final String HBASE_TABLE_NAME_PREFIX;
    private static final byte[] F1;
    private static final String METHOD_QUERY_FOR_LIST;
    private static final String METHOD_QUERY_FOR_COUNT;
    private static final int DEFAULT_SOLR_TIMEOUT;
    private static ObjectMapper objectMapper = new ObjectMapper();

    static {
        SOLR_COLLECTION_PREFIX = "wallet_change_log_";
        HBASE_NAMESPACE = "wallet";
        HBASE_TABLE_NAME_PREFIX = "wallet_change_log_";
        METHOD_QUERY_FOR_LIST = "WalletChangeLogQueryForList";
        METHOD_QUERY_FOR_COUNT = "WalletChangeLogQueryForCount";
        F1 = "f1".getBytes();
        DEFAULT_SOLR_TIMEOUT = 10 * 1000;
    }

    @Autowired
    private Executor walletChangeLogHBaseExecutor;

    /**
     * count
     *
     * @param query WalletChangeLogQuery
     * @return 数量
     */
    public long count(WalletChangeLogQuery query) {
        return countBySolr(query);
    }

    /**
     * 列表查询
     */
    public List<WalletChangeLogPo> queryForList(WalletChangeLogQuery query) {
        // 查询 solr
        List<Triple<String, Long, String>> merchantIdCtimeIdList = querySolr(query);
        if (CollectionUtils.isEmpty(merchantIdCtimeIdList)) {
            return Lists.newArrayList();
        }
        // 查 HBase
        return queryHBaseForList(merchantIdCtimeIdList);
    }

    private long countBySolr(WalletChangeLogQuery query) {
        String solrCollection = getSolrCollections(query.getStartTime(), query.getEndTime());
        if (StringUtils.isEmpty(solrCollection)) {
            return 0;
        }

        return SolrHBaseUtils.count(solrCollection, query.buildSolrQuery(), METHOD_QUERY_FOR_COUNT, DEFAULT_SOLR_TIMEOUT);
    }

    /**
     * 查 Solr
     *
     * @return first 为 merchant_id，second 为 ctime，third 为 id
     */
    private List<Triple<String, Long, String>> querySolr(WalletChangeLogQuery query) {
        // 获取 Solr Collection
        String solrCollections = getSolrCollections(query.getStartTime(), query.getEndTime());
        if (StringUtils.isEmpty(solrCollections)) {
            return Lists.newArrayList();
        }

        List<QueryResponse> queryResponseList = SolrHBaseUtils.query(
                Lists.newArrayList(solrCollections), query.buildSolrQuery(), METHOD_QUERY_FOR_LIST, DEFAULT_SOLR_TIMEOUT);
        if (CollectionUtils.isEmpty(queryResponseList)) {
            return Lists.newArrayList();
        }

        // first 为 merchant_id，second 为 ctime，third 为 id
        return queryResponseList.stream()
                .flatMap(response -> response.getResults().parallelStream())
                .map(document -> Triple.create(
                        document.getFieldValue(WalletChangeLogSolrFields.MERCHANT_ID).toString(),
                        (Long) document.getFieldValue(WalletChangeLogSolrFields.CTIME),
                        document.getFieldValue(WalletChangeLogSolrFields.ID).toString()))
                .collect(Collectors.toList());
    }

    /**
     * 根据起始时间和截止时间获取 Solr Collection 列表，逗号分割
     */
    private String getSolrCollections(Long startTime, Long endTime) {
        // 当前基准时间戳
        long currentTimeMillis = System.currentTimeMillis();
        // 当前日期
        LocalDate today = LocalDate.fromDateFields(new Date(currentTimeMillis));

        // 空值处理
        if (endTime == null) {
            endTime = today.plusDays(1).toDate().getTime(); // 默认为今天24点
        }
        if (startTime == null) {
            // 导余额对账单最大时间跨度为1个月，这里默认时间跨度为1个月
            startTime = LocalDateTime.fromDateFields(new Date(endTime)).minusMonths(1).toDate().getTime();
        }

        // 边界有效值处理
        // 数据起始时间 2020-03-01
        long dataStartTime = 1582992000000L;
        if (startTime < dataStartTime) {
            startTime = dataStartTime;
        }
        if (endTime > currentTimeMillis) {
            endTime = currentTimeMillis;
        }

        // 日期时间无效的条件
        if (startTime >= endTime) {
            return null;
        }

        return calculateSolrCollections(startTime, endTime);
    }

    /**
     * 根据其实时间和截至时间计算 Solr Collection 列表
     */
    private String calculateSolrCollections(long startTime, long endTime) {
        // startTime 必须小于 endTime，否则无意义
        if (startTime >= endTime) {
            return null;
        }

        YearMonth startMonth = YearMonth.fromDateFields(new Date(startTime));
        YearMonth endMonth = YearMonth.fromDateFields(new Date(endTime));

        StringBuilder sb = new StringBuilder();
        while (!startMonth.isAfter(endMonth)) {
            sb.append(SOLR_COLLECTION_PREFIX).append(startMonth.toString("yyyyMM"));
            startMonth = startMonth.plusMonths(1);
            if (startMonth.isAfter(startMonth)) {
                break;
            }
            sb.append(",");
        }

        return sb.toString();
    }

    /**
     * 查 HBase
     */
    @SneakyThrows
    private List<WalletChangeLogPo> queryHBaseForList(List<Triple<String, Long, String>> merchantIdCtimeIdList) {
        Map<TableName, List<Get>> tableNameAndGetsMap = new LinkedHashMap<>();
        // 记录id序号，用于 HBase 查询后的排序
        Map<String, Integer> idPositionMap = new HashMap<>((int) (merchantIdCtimeIdList.size() / 0.75F) + 1);
        final AtomicInteger positionMarker = new AtomicInteger(0);
        merchantIdCtimeIdList.forEach(merchantIdCtimeId -> {
            idPositionMap.put(merchantIdCtimeId.getThird(), positionMarker.getAndIncrement());// 记住id位置
            Get get = new Get(Bytes.add(
                    Bytes.toBytes(merchantIdCtimeId.getFirst()),
                    Bytes.toBytes(merchantIdCtimeId.getSecond()),
                    Bytes.toBytes(merchantIdCtimeId.getThird())));
            get.addColumn(F1, WalletChangeLogHBaseColumns.ID);
            get.addColumn(F1, WalletChangeLogHBaseColumns.TYPE);
            get.addColumn(F1, WalletChangeLogHBaseColumns.AMOUNT);
            get.addColumn(F1, WalletChangeLogHBaseColumns.SIGN);
            get.addColumn(F1, WalletChangeLogHBaseColumns.BALANCE);
            get.addColumn(F1, WalletChangeLogHBaseColumns.REMARK);
            get.addColumn(F1, WalletChangeLogHBaseColumns.DETAIL);
            get.addColumn(F1, WalletChangeLogHBaseColumns.MERCHANT_ID);
            get.addColumn(F1, WalletChangeLogHBaseColumns.ACTION_ID);
            get.addColumn(F1, WalletChangeLogHBaseColumns.CTIME);
            get.addColumn(F1, WalletChangeLogHBaseColumns.MTIME);
            TableName hBaseTableName = getTableName(merchantIdCtimeId.getSecond());
            tableNameAndGetsMap.computeIfAbsent(hBaseTableName, tableName -> new ArrayList<>()).add(get);
        });

        List<Result> resultList = new ArrayList<>(merchantIdCtimeIdList.size());
        if (MapUtils.isNotEmpty(tableNameAndGetsMap)) {
            CarrierItem carrierItem = new CarrierItem(TraceContext.traceId());
            //  分表查询
            List<CompletableFuture<List<Result>>> futures = new ArrayList<>(merchantIdCtimeIdList.size());
            tableNameAndGetsMap.forEach((tableName, getList) -> 
                futures.add(CompletableFuture.supplyAsync(() -> {
                        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
                        return Arrays.asList(SolrHBaseUtils.batchGet(tableName, getList, 3000));
                    }, walletChangeLogHBaseExecutor))
            );
            for (CompletableFuture<List<Result>> completableFuture : futures) {
                resultList.addAll(completableFuture.get());
            }
            resultList = resultList.stream()
                    .filter(r -> r.getRow() != null)
                    .map(r -> new HbaseResult(r, idPositionMap.get(Bytes.toString(r.getValue(F1, WalletChangeLogHBaseColumns.ID)))))
                    // 排序
                    .sorted(Comparator.comparing(HbaseResult::getPosition))
                    .collect(Collectors.toList());
        }

        return resultList.parallelStream()
                .filter(r -> Objects.nonNull(r.getRow()))
                .map(this::convertToWalletChangeLogPo)
                .collect(Collectors.toList());

    }

    private WalletChangeLogPo convertToWalletChangeLogPo(Result r) {
        WalletChangeLogPo walletChangeLogPo = new WalletChangeLogPo();
        walletChangeLogPo.setId(Bytes.toString(r.getValue(F1, WalletChangeLogHBaseColumns.ID)));

        Optional.ofNullable(r.getValue(F1, WalletChangeLogHBaseColumns.TYPE))
                .ifPresent(value -> walletChangeLogPo.setType(Bytes.toInt(value)));
        Optional.ofNullable(r.getValue(F1, WalletChangeLogHBaseColumns.AMOUNT))
                .ifPresent(value -> walletChangeLogPo.setAmount(Bytes.toLong(value)));
        Optional.ofNullable(r.getValue(F1, WalletChangeLogHBaseColumns.SIGN))
                .ifPresent(value -> walletChangeLogPo.setSign(Bytes.toInt(value)));
        Optional.ofNullable(r.getValue(F1, WalletChangeLogHBaseColumns.BALANCE))
                .ifPresent(value -> walletChangeLogPo.setBalance(Bytes.toLong(value)));

        walletChangeLogPo.setRemark(Bytes.toString(r.getValue(F1, WalletChangeLogHBaseColumns.REMARK)));

        String detailStr = Bytes.toString(r.getValue(F1, WalletChangeLogHBaseColumns.DETAIL));
        if (StringUtils.isNotEmpty(detailStr)) {
            try {
                walletChangeLogPo.setDetail(JSON.parseObject(detailStr, new TypeReference<Map<String, Object>>() {
                }));
            } catch (Exception e) {
            }

        }
        walletChangeLogPo.setMerchantId(Bytes.toString(r.getValue(F1, WalletChangeLogHBaseColumns.MERCHANT_ID)));
        walletChangeLogPo.setActionId(Bytes.toString(r.getValue(F1, WalletChangeLogHBaseColumns.ACTION_ID)));

        Optional.ofNullable(r.getValue(F1, WalletChangeLogHBaseColumns.CTIME))
                .ifPresent(value -> walletChangeLogPo.setCtime(Bytes.toLong(value)));
        Optional.ofNullable(r.getValue(F1, WalletChangeLogHBaseColumns.MTIME))
                .ifPresent(value -> walletChangeLogPo.setMtime(Bytes.toLong(value)));
        //展示业务名称
        byte[] detail = r.getValue(F1, WalletChangeLogHBaseColumns.DETAIL);
        if (detail != null) {
            try {
                Map<String, Object> detailMap = objectMapper.readValue(detail, Map.class);
                if(detailMap != null && detailMap.containsKey("name")) {
                    walletChangeLogPo.setName(MapUtil.getString(detailMap, "name"));
                }
            } catch (Exception e) {
            }
        }

        return walletChangeLogPo;
    }

    /**
     * 年月与对应的 {@link TableName} 缓存
     */
    private static final Map<YearMonth, TableName> TABLE_NAME_CACHE = new ConcurrentHashMap<>();

    private TableName getTableName(long timestamp) {
        YearMonth yearMonth = YearMonth.fromDateFields(new Date(timestamp));
        TableName tableName = TABLE_NAME_CACHE.get(yearMonth);
        if (tableName == null) {
            synchronized (WalletChangeLogHBaseDao.class) {
                tableName = TABLE_NAME_CACHE.get(yearMonth);
                if (tableName == null) {
                    tableName = TableName.valueOf(HBASE_NAMESPACE, HBASE_TABLE_NAME_PREFIX + yearMonth.toString("yyyyMM"));
                    TABLE_NAME_CACHE.put(yearMonth, tableName);
                }
            }
        }

        return tableName;
    }

}
