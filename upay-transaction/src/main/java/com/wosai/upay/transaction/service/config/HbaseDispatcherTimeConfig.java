package com.wosai.upay.transaction.service.config;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import java.util.function.Supplier;

import org.springframework.context.annotation.Configuration;

import com.wosai.upay.transaction.util.ApolloUtil;
import com.wosai.upay.transaction.util.DateTimeUtil;

@Configuration
public class HbaseDispatcherTimeConfig {
    // 热存储索引配置
    private volatile int limitMonth = 24;
    private volatile int limitYear = 2;
    private volatile long hotIndexStartTime = 0L;
    // 热存储宽表起始时间（近3个月）
    private volatile long hotStorageStartTime = 0L;
    // 查询请求缓存开始时间（近4个月）
    private volatile long queryCacheStartTime = 0L;

    private Supplier<Void> reloadHotIndex = () -> {
        limitMonth = ApolloUtil.getSolrHotIndexMonth();
        limitYear = limitMonth / 12 + ((limitMonth % 12) > 0 ? 1 : 0);
        hotIndexStartTime = LocalDate.now().withDayOfMonth(1).minusMonths(limitMonth).atStartOfDay().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        return null;
    };

    private Supplier<Void> reloadHotStorageStartTime = () -> {
        hotStorageStartTime = LocalDate.now().withDayOfMonth(1).minusMonths(2).atStartOfDay().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        return null;
    };

    private Supplier<Void> reloadQueryCacheStartTime = () -> {
        queryCacheStartTime = LocalDate.now().minusMonths(3).with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        return null;
    }; 

    public HbaseDispatcherTimeConfig() {
        reloadHotIndex.get();
        reloadHotStorageStartTime.get();
        reloadQueryCacheStartTime.get();

        // 支持动态变更搜索月份
        ApolloUtil.registryChangeListener(ApolloUtil.SOLR_HOT_INDEX_MONTH, (cc) -> {
            reloadHotIndex.get();
        });

        // 每日重新计算一次热存储起始时间
        new Timer().scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                reloadHotIndex.get();
                reloadHotStorageStartTime.get();
                reloadQueryCacheStartTime.get();
            }
        }, DateTimeUtil.getNextDayStart(new Date()) , DateTimeUtil.ONE_DAY_MILLIS);
    }

    public long getHotIndexStartTime() {
        return hotIndexStartTime;
    }

    public long getSolrLimitStartTime(Long startTime) {
        if (startTime == null) {
            return hotIndexStartTime;
        }
        return Math.max(hotIndexStartTime, startTime);
    }

    public int getHotIndexLimitYear() {
        return limitYear;
    }

    public boolean isHostStorage(long time) {
        return time >= hotStorageStartTime  ? true : false;
    }

    public boolean needCache(Long startTime) {
        if (startTime == null || startTime < queryCacheStartTime) {
            return false;
        }
        return true;
    }
}
