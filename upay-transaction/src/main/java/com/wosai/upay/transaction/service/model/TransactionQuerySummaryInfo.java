package com.wosai.upay.transaction.service.model;

import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.util.TransactionTypeRelatedUtil;
import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class TransactionQuerySummaryInfo {

    private long allCount = 0;

    private double amount = 0;
    private double merchantDiscount = 0;
    private double actualReceiveAmount = 0;
    private double fee = 0;
    private double sharingAmount = 0;
    private double clearingAmount = 0;
    private long count;

    //记账交易
    private double chargeAmount = 0;
    private double chargeActualReceiveAmount = 0;
    private double chargeClearingAmount = 0;
    private long chargeCount;

    //直清交易 liquidation_next_day = false
    //但需要剔除记账交易、储值核销交易、现金储值充值交易
    private double liquidationAmount = 0;
    private double liquidationActualReceiveAmount = 0;
    private double liquidationClearingAmount = 0;
    private long liquidationCount;

    private double storePayAmount = 0;
    private double storePayReceiveAmount = 0;
    private double storePayClearingAmount = 0;

    private double storeInAmount = 0;
    private double storeInMerchantDiscount = 0;
    private double storeInActualReceiveAmount = 0;
    private double storeInFee = 0;
    private double storeInSharingAmount = 0;
    private double storeInClearingAmount = 0;
    private long storeInCount;
    private double storeInCashAmount = 0;
    private double storeInCashClearingAmount = 0;
    private long storeInCashCount = 0;

    //其他方式记账储值充值
    private double storeInOtherChargeAmount = 0;
    private double storeInOtherChargeClearingAmount = 0;
    private long storeInOtherChargeCount = 0;

    //储值直清交易
    private double storeInLiquidationAmount = 0;
    private double storeInLiquidationActualReceiveAmount = 0;
    private double storeInLiquidationClearingAmount = 0;
    private long storeInLiquidationCount;

    public void recordAmountByType(double amount, double merchantDiscount, double actualReceiveAmount,
                                   double fee, double sharingAmount, double clearingAmount, boolean liquidationNextDay,
                                   Integer type, Integer payway) {
        if (TransactionTypeRelatedUtil.isStoreInOrRefund(type)) {
            storeInAmount += amount;
            storeInMerchantDiscount += merchantDiscount;
            storeInActualReceiveAmount += actualReceiveAmount;
            storeInFee += fee;
            storeInSharingAmount += sharingAmount;
            storeInClearingAmount += clearingAmount;
            storeInCount++;
            if (Order.PAYWAY_CASH == payway) {
                storeInCashAmount += amount;
                storeInCashClearingAmount += amount;
                storeInCashCount++;
            } else if (Order.PAYWAY_OTH_CHG == payway){
                storeInOtherChargeAmount += amount;
                storeInOtherChargeClearingAmount += amount;
                storeInOtherChargeCount++;
            } else if (!liquidationNextDay){
                storeInLiquidationAmount += amount;
                storeInLiquidationActualReceiveAmount += actualReceiveAmount;
                storeInLiquidationClearingAmount += clearingAmount;
                storeInLiquidationCount += count;
            }
        } else {
            this.amount += amount;
            this.merchantDiscount += merchantDiscount;
            this.actualReceiveAmount += actualReceiveAmount;
            this.fee += fee;
            this.sharingAmount += sharingAmount;
            this.clearingAmount += clearingAmount;
            count++;
            if (TransactionTypeRelatedUtil.isStorePayOrRefund(type)) {
                storePayAmount += amount;
                storePayReceiveAmount += actualReceiveAmount;
                storePayClearingAmount += clearingAmount;
            } else if (TransactionTypeRelatedUtil.isChargePayOrRefund(type)) {
                chargeAmount += amount;
                chargeActualReceiveAmount += actualReceiveAmount;
                chargeClearingAmount += clearingAmount;
                chargeCount += count;
            } else if (!liquidationNextDay) {
                liquidationAmount += amount;
                liquidationActualReceiveAmount += actualReceiveAmount;
                liquidationClearingAmount += clearingAmount;
                liquidationCount += count;
            }
        }
    }

    public void recordAllCount() {
        allCount++;
    }

    public long allCount() {
        return allCount;
    }

}
