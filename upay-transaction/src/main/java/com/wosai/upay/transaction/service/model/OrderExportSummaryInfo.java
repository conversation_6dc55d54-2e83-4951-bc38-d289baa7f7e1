package com.wosai.upay.transaction.service.model;

import com.wosai.upay.transaction.util.TransactionUtil;
import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class OrderExportSummaryInfo {
    public long totalCount = 0;
    public double totalAmount = 0;
    public long storeInCount = 0;
    public double storeInAmount = 0;



    /**
     * 记录笔数，金额
     */
    public void recordAmountByFlag(double amount, String flag) {
        if (TransactionUtil.isStoreInFlag(flag)) {
            storeInAmount += amount;
            storeInCount++;
        } else {
            totalAmount += amount;
            totalCount++;
        }
    }

    public long allCount() {
        return totalCount + storeInCount;
    }

}
