package com.wosai.upay.transaction.util;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ctrip.framework.apollo.ConfigService;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.model.param.AccountBookOrderByType;
import com.wosai.upay.transaction.model.param.AccountRecordParam;

/**
 * 当前类解决调用方法是否走ES进行交易查询
 *
 * <AUTHOR>
 */
public class NeedESQueryUtil {

    public static Logger logger = LoggerFactory.getLogger(NeedESQueryUtil.class);

    public static volatile long ES_DATA_START = DateTimeUtil.getTodayStart().getTimeInMillis() - DateTimeUtil.ONE_DAY_MILLIS * 2;

    public static final String KEY_TEMPLATE_1 = "template1";
    public static final String KEY_TEMPLATE_2 = "template2";
    public static final String KEY_TEMPLATE_3 = "template3";
    public static final String KEY_TEMPLATE_4 = "template4";
    public static final String KEY_TEMPLATE_5 = "template5";
    public static final String KEY_TEMPLATE_6 = "template6";
    public static final String KEY_TEMPLATE_7 = "template7";
    public static final String KEY_TEMPLATE_8 = "template8";
    public static final String KEY_TEMPLATE_9 = "template9";
    public static final String KEY_TEMPLATE_10 = "template10";
    public static final String KEY_TEMPLATE_11 = "template11";

    private static final String DEFAULT_QUERY_STATUS = "1,2";

    private static Map<String, Integer> proportions = new ConcurrentHashMap<>();

    static {
        String prop = ConfigService.getAppConfig().getProperty(CommonConstant.QUERY_ES_PROPORTION, "{}");
        proportions.putAll(JsonUtil.jsonStrToObject(prop, Map.class));
        ConfigService.getAppConfig().addChangeListener(changeEvent -> {
            if (null != changeEvent.changedKeys() && changeEvent.changedKeys().contains(CommonConstant.QUERY_ES_PROPORTION)) {
                String tmp = ConfigService.getAppConfig().getProperty(CommonConstant.QUERY_ES_PROPORTION, "{}");
                proportions.putAll(JsonUtil.jsonStrToObject(tmp, Map.class));
            }
        });

        // 每天凌晨更新ES缓存数据的开始时间
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                ES_DATA_START = DateTimeUtil.getTodayStart().getTimeInMillis() - DateTimeUtil.ONE_DAY_MILLIS * 2;
            }
        }, DateTimeUtil.getTomorrowStart().getTime(), 24 * 60 * 60 * 1000);
    }

    /**
     * 校验是否使用ES查询数据
     * <p>
     * 条件
     * 1、查询开始时间是近2天之内扫码交易
     */
    public static boolean getAccountBookRecordsMatch(AccountRecordParam param) {
        if (param != null
                && param.getUpayQueryType() == UpayQueryType.UPAY.getCode()
                && null != param.getStartTime()
                && param.getStartTime().getTime() >= ES_DATA_START
                && (null == param.getEndTime() || param.getStartTime().before(param.getEndTime()))) {

            String templateStr = (null != param.getSimple() && param.getSimple()) ? KEY_TEMPLATE_3 : KEY_TEMPLATE_4;
            return throwCoin(templateStr, param.getMerchant_id());
        }

        return false;
    }

    /**
     * 校验是否使用ES查询数据
     * <p>
     * 条件
     * 1、查询开始时间是近2天之内
     */
    public static boolean getTransactionListForPcDesktopMatch(String merchantId, PageInfo pageInfo) {
        if (null != pageInfo.getDateStart()
                && pageInfo.getDateStart() >= ES_DATA_START
                && (null == pageInfo.getDateEnd() || pageInfo.getDateStart() < pageInfo.getDateEnd())) {

            return throwCoin(KEY_TEMPLATE_5, merchantId);
        }

        return false;
    }

    /**
     * 校验是否使用ES查询数据
     * <p>
     * 条件
     * 1、查询开始时间和结束时间都为空，tradeStatus = 1,2
     * 2、开始时间 > es的存储时间
     * 3、开始时间是3个月前的一天 (重写参数startTime为默认一个月前 && pageSize为4 )
     * 4、开始时间和结算时间间隔3个月，排序方式为倒序，pageSize = 30
     */
    public static boolean getMoreAccountBookRecordsMatchAndRebuildIfNecessary(AccountRecordParam param) {
        if (param == null || param.getUpayQueryType() != UpayQueryType.UPAY.getCode()) {
            return false;
        }

        String template = null;
        if (param.getStartTime() == null
                && param.getEndTime() == null
                && param.getPageSize() == null
                && DEFAULT_QUERY_STATUS.equals(param.getTradeStatus())) {
            template = KEY_TEMPLATE_2;

        } else if (null != param.getStartTime()
                && param.getStartTime().getTime() >= ES_DATA_START
                && (null == param.getEndTime() || param.getStartTime().before(param.getEndTime()))) {
            template = KEY_TEMPLATE_6;
        } else if (specificMatchAndReWriteParam(param)) {
            template = KEY_TEMPLATE_7;
        } else if (param.getStartTime() != null
                && param.getEndTime() != null && param.getEndTime().getTime() > ES_DATA_START
                && (param.getOrderBy() == null || param.getOrderBy() == AccountBookOrderByType.CTIME_DESC)
                && (Objects.equals(param.getPageSize(), 30) || Objects.equals(param.getPageSize(), 100) || Objects.equals(param.getPageSize(), 6) || Objects.isNull(param.getPageSize()))) {
            Period period = Period.between(param.getStartTime().toInstant().atZone(ZoneId.of("+8")).toLocalDate(),
                    param.getEndTime().toInstant().plusMillis(-1).atZone(ZoneId.of("+8")).toLocalDate());
            int months = period.getYears() * 12 + period.getMonths();
            if (months == 3) {
                template = KEY_TEMPLATE_1;
            }
        }
        if (StringUtils.isNotEmpty(template)) {
            return throwCoin(template, param.getMerchant_id());
        }

        return false;
    }

    public static boolean byStartTimeAndEndTime(String merchantId, Long startTime, Long endTime) {
        if (startTime != null
                && startTime >= ES_DATA_START
                && (endTime == null || endTime > startTime)) {
            return throwCoin(KEY_TEMPLATE_8, merchantId);
        }

        return false;
    }

    public static boolean getAccountSumMatch(AccountRecordParam param) {
        if (param != null
                && param.getUpayQueryType() == UpayQueryType.UPAY.getCode()
                && param.getStartTime() != null
                && param.getStartTime().getTime() >= ES_DATA_START
                && (param.getEndTime() == null || param.getEndTime().after(param.getStartTime()))) {
            return throwCoin(KEY_TEMPLATE_9, param.getMerchant_id());
        }

        return false;
    }

    public static boolean getAccountSumGroupByMatch(AccountRecordParam param) {
        if (param != null
                && param.getUpayQueryType() == UpayQueryType.UPAY.getCode()
                && param.getStartTime() != null
                && param.getStartTime().getTime() >= ES_DATA_START
                && (param.getEndTime() == null || param.getEndTime().after(param.getStartTime()))) {
            return throwCoin(KEY_TEMPLATE_10, param.getMerchant_id());
        }

        return false;
    }

    public static boolean queryChangeShiftsStatisticsByChangeShiftsMatch(String merchantId, Long startTime, Long endTime) {
        if (startTime != null
                && startTime >= ES_DATA_START
                && (endTime == null || endTime > startTime)) {
            return throwCoin(KEY_TEMPLATE_11, merchantId);
        }

        return false;
    }

    /**
     * 会重写参数
     *
     * @param param 参数
     * @return 是否match 并且重写
     */
    private static boolean specificMatchAndReWriteParam(AccountRecordParam param) {
        Date startTime = param.getStartTime();
        Date endTime = param.getEndTime();
        if (startTime == null || endTime == null) {
            return false;
        }
        LocalDate now = LocalDate.now();
        long start = now.atStartOfDay().plusMonths(-3).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
        long end = now.plusDays(1).atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
        if (startTime.getTime() != start || endTime.getTime() != end) {
            return false;
        }

        //app首页查询满足条件

//        这几个字段未包含
        //offsetHour
        //merchant_id
        //storeIds
        //simple
        //token
        //start
        //end
        //upayQueryType
        if (param.getLastRecordTime() == null
                && param.getPageNum() == null
                && (param.getPageSize() != null && param.getPageSize() == 30)
                && param.getOrderBy() == null
                && param.getTradeStatus() == null
                && param.getTradeType() == null
                && param.getTerminals() == null
                && param.getNotContainTerminals() == null
                && param.getVersion() == null
                && param.getTransactionSn() == null
                && param.getMobile() == null
                && param.getQrcode() == null
                && param.getPos() == null
                && param.getMaya() == null
                && param.getPosplus() == null
                && param.getPcplugin() == null
                && param.getFaceScan() == null
                && param.getGroupBys() == null
                && param.getSocode() == null
                && param.getNpos2() == null
                && param.getGroupmeal() == null
                && param.getProductFlag() == null
                && param.getOrderSns() == null
                && param.getIsStoreAccount()
                && param.getValidTimeSpan()
                && param.getNeedTodayDefault()
                && (!param.getIsSummary())) {
            //开始时间重写到-1
            param.setStartTime(new Date(now.atStartOfDay().plusMonths(-1).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000));
            param.setPageSize(4);
            return true;
        }
        return false;
    }


    private static boolean throwCoin(String template, String merchantId) {
        int headPct = MapUtil.getIntValue(proportions, template, 0);
        if (headPct == 0) {
            return false;
        }
        if (headPct == 100) {
            logger.info("template:{} will query es, merchant_id = {}", template, merchantId);
            return true;
        }

        int randInt = Math.abs((template + merchantId).hashCode() % 100);
        if (randInt < headPct) {
            logger.info("template:{} will query es, merchant_id = {}", template, merchantId);
            return true;
        }

        return false;
    }

}
