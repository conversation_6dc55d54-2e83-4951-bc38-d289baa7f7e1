package com.wosai.upay.transaction.service;

import java.util.Map;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.annotation.DBSelectService;

/**
 * 兼容老的backend-upay 接口
 * 
 * @see com.wosai.upay.transaction.service.UpayOrderService
 */
@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "/rpc/terminal")
@Deprecated
public interface BackendUpayTerminalService {
    public Map<String, Object> getTerminalChangeShiftsStatisticsInfo(String terminalSn, String batchSn);

    public ListResult getTerminalChangeShiftsStatisticsList(String terminalSn, Long startDate, Long endDate, PageInfo pageInfo);
}
