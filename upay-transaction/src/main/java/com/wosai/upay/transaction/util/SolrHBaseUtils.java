package com.wosai.upay.transaction.util;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.upay.model.Order;
import com.wosai.upay.model.Transaction;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DatabaseQueryConstant;
import com.wosai.upay.transaction.model.SensitiveProperties;
import com.wosai.upay.transaction.service.dao.base.HBaseDao;
import com.wosai.upay.transaction.trace.TimedCallable;
import com.wosai.upay.transaction.trace.TimedRunnable;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Threads;
import org.apache.hadoop.hbase.zookeeper.ReadOnlyZKClient;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.impl.CloudSolrClient;
import org.apache.solr.client.solrj.impl.HttpClientUtil;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrInputDocument;
import org.apache.solr.common.params.CommonParams;
import org.apache.solr.common.params.ModifiableSolrParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Function;


public class SolrHBaseUtils {

    public static final Logger log = LoggerFactory.getLogger(SolrHBaseUtils.class);

    public static final HttpClient httpClient;

    public static final ThreadPoolExecutor pool;

    private static final String SOLR_PREFIX = "hsolr@";

    private static final String H_BASE_PREFIX = "hbase@";

    public static final ConcurrentHashMap<Integer, Connection> RPC_TIMEOUT_CONNECTION = new ConcurrentHashMap<>();

    public static final ConcurrentHashMap<String, TablePool> TABLE_NAME_TABLE_POOL = new ConcurrentHashMap<>();

    public static final ConcurrentHashMap<Integer, CloudSolrClient> SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT = new ConcurrentHashMap<>();

    public static final ConcurrentHashMap<Integer, Connection> LINDORM_RPC_TIMEOUT_CONNECTION = new ConcurrentHashMap<>();
    public static final ConcurrentHashMap<Integer, CloudSolrClient> LINDORM_SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT = new ConcurrentHashMap<>();
    public static final ConcurrentHashMap<String, TablePool> LINDORM_TABLE_NAME_TABLE_POOL = new ConcurrentHashMap<>();
    public static final ConcurrentHashMap<String, String> COLLECTION_MONTH_YEAR = new ConcurrentHashMap<>();

    public static ExecutorService lindormExecutor = Executors.newFixedThreadPool(10);

    private static final String HBASE_CLIENT_IPC_POOL_SIZE = "20";

    private static final String HBASE_CLIENT_RETRIES_NUMBER = "3";

    private static final String RECOVERY_RETRY = "5";

    private static final Interner<String> STRING_POOL = Interners.newWeakInterner();

    private static final String SOLR_METHOD_QUERY = "com.wosai.upay.transaction.util.SolrHBaseUtils:query(java.util.List,org.apache.solr.client.solr.SolrQuery,java.lang.String,";
    private static final String END_OF_SOLR_METHOD = ")";
    private static final String HBASE_COLD_BATCH_GET = "com.wosai.upay.transaction.util.SolrHBaseUtils:coldBatchGet()";

    public enum SolrPartition {
        hot, recent_6m, cold, all
    }

    static {
        PoolingHttpClientConnectionManager poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager();
        ModifiableSolrParams solrParams = new ModifiableSolrParams();
        solrParams.set(HttpClientUtil.PROP_MAX_CONNECTIONS, 900);
        solrParams.set(HttpClientUtil.PROP_MAX_CONNECTIONS_PER_HOST, 30);
        httpClient = HttpClientUtil.createClient(solrParams, poolingHttpClientConnectionManager);

        pool = new ThreadPoolExecutor(100, 512, 60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                Threads.newDaemonThreadFactory(CommonConstant.HBASE_THREAD_NAME),
                new ThreadPoolExecutor.AbortPolicy());
        pool.allowCoreThreadTimeOut(true);

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            RPC_TIMEOUT_CONNECTION.values().forEach(connection -> {
                try {
                    connection.close();
                    log.info("hBaseConnection 正常关闭 ");
                } catch (Exception ex) {
                    log.error("hBaseConnection 关闭异常 ", ex);
                }
            });
            SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT.values().forEach(cloudSolrClient -> {
                try {
                    cloudSolrClient.close();
                    log.info("solrConnection 正常关闭 ");
                } catch (Exception ex) {
                    log.error("solrConnection 关闭异常 ", ex);
                }
            });
            LINDORM_RPC_TIMEOUT_CONNECTION.values().forEach(connection -> {
                try {
                    connection.close();
                    log.info("lindorm hBaseConnection 正常关闭 ");
                } catch (Exception ex) {
                    log.error("lindorm hBaseConnection 关闭异常 ", ex);
                }
            });
            LINDORM_SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT.values().forEach(cloudSolrClient -> {
                try {
                    cloudSolrClient.close();
                    log.info("lindorm solrConnection 正常关闭 ");
                } catch (Exception ex) {
                    log.error("lindorm solrConnection 关闭异常 ", ex);
                }
            });
            pool.shutdown();

        }));
    }

    private static boolean isUniqKey(List<String> collections, SolrQuery solrQuery) {
        String collection = collections.get(0);
        String queryString = solrQuery.toQueryString();
        if (collection.contains("tx") && (queryString.contains(Transaction.CLIENT_TSN) || queryString.contains(Transaction.TSN))) {
            return true;
        }
        if (collection.contains("order") && (queryString.contains(Order.CLIENT_SN) || queryString.contains(Order.SN))) {
            return true;
        }
        return false;
    }

    @SneakyThrows
    public static long count(String collection, SolrQuery solrQuery, String methodName, int solrTimeout) {
        try{
            SphU.entry(SOLR_METHOD_QUERY + solrTimeout + END_OF_SOLR_METHOD, EntryType.IN, 1);
            return TimedCallable.of(SOLR_PREFIX + traceName(collection) + "_" + methodName, () -> {
                solrQuery.setStart(0);
                solrQuery.setRows(0);

                solrQuery.set(CommonParams.TIME_ALLOWED, solrTimeout - 500);
                log.info("methodName -> {}, collection -> {}, queryString -> {}, solrTimeout ->{}", methodName, collection, solrQuery.toQueryString(), solrTimeout);
                return getCloudSolrClient(solrTimeout)
                        .query(collection, solrQuery, SolrRequest.METHOD.POST)
                        .getResults()
                        .getNumFound();
            }).call();
        } catch (BlockException ex) {
            throw new com.wosai.upay.transaction.exception.BlockException("请求过于频繁,请稍后重试");
        } finally {
            SphU.entryEnd(1);
        }
    }

    @SneakyThrows
    public static List<QueryResponse> query(List<String> collections, SolrQuery solrQuery, String methodName, int solrTimeout) {
        try {
            SphU.entry(SOLR_METHOD_QUERY + solrTimeout + END_OF_SOLR_METHOD, EntryType.IN, 1);
            // 通过订单号查询数据且要查询多个solr collection时，当单个collection查到数据后就返回
            boolean needSkip = false;
            if (HBaseDao.QUERY_LIST.equals(methodName) && collections.size() > 1 && isUniqKey(collections, solrQuery)) {
                needSkip = true;
            }
            List<QueryResponse> responses = new ArrayList<>(collections.size());
            MutablePair<Boolean, Exception> taskResult = MutablePair.of(false, null);
            final boolean finalSkip = needSkip;
            for (String collection : collections) {
                TimedRunnable.of(SOLR_PREFIX + traceName(collection) + "_" + methodName, () ->{
                    try {
                        solrQuery.set(CommonParams.TIME_ALLOWED, solrTimeout - 500);
                        log.info("methodName -> {}, collection -> {}, queryString -> {}, solrTimeout ->{}", methodName, collection, solrQuery.toQueryString(), solrTimeout);
                        QueryResponse response = getCloudSolrClient(solrTimeout).query(collection, solrQuery, SolrRequest.METHOD.POST);
                        responses.add(response);
                        if (finalSkip && response.getResults().getNumFound() > 0) {
                            taskResult.setLeft(true);
                            return;
                        }
                    } catch (Exception ex) {
                        log.error("methodName -> {}, collection -> {}, queryString -> {}, solrTimeout ->{}，exception：{}", methodName, collection, solrQuery.toQueryString(), solrTimeout, ex);
                        ActiveSpan.error("solr查询出错" + ex.getMessage());
                        taskResult.setRight(ex);
                    }
                }).run();
                if(taskResult.getLeft()) {
                    break;
                }else if(taskResult.getRight() != null) {
                    throw taskResult.getRight();
                }
            }
            return responses;
        } catch (BlockException ex) {
            throw new com.wosai.upay.transaction.exception.BlockException("请求过于频繁,请稍后重试");
        } finally {
            SphU.entryEnd(1);
        }
    }

    /**
     * zeus字符串太长影响性能 减少长度
     * 
     * 2022-10-08：因为月份越来越多，导致trace指标太多，展示不出来，改造为按年打印
     *
     * @param collection 索引名称
     * @return upay_tx_201605, upay_tx_201606, upay_tx_201607   -->  tx_2016_to_2016
     */
    private static String traceName(String collection) {
        try {
            if (StringUtils.isEmpty(collection)) {
                return "";
            }
            String[] split = collection.split(",");
            String start = split[0];
            String end = split[split.length - 1];
            if (start.contains("201605")) {
                log.warn("may full query");
            }
            String type = start.substring(start.indexOf("_") + 1, start.lastIndexOf("_"));
            return type + getCollectionYear(start) + "_to_" + getCollectionYear(end);
        } catch (Exception e) {
            log.error("to traceName error", e);
            return "";
        }
    }

    private static String getCollectionYear(String collection) {
        String year = COLLECTION_MONTH_YEAR.get(collection);
        if(year == null) {
            year = collection.substring(collection.lastIndexOf("_") + 1, collection.length() - 2);
            COLLECTION_MONTH_YEAR.put(collection, year);
        }
        return year;
    }

    @SneakyThrows
    public static Result[] batchGet(TableName tableName, List<Get> gets, int hBaseTimeout) {
        log.info("batchGet ->{}, hBaseTimeout -> {}", tableName.getNameAsString(), hBaseTimeout);
        return TimedCallable.of(H_BASE_PREFIX + getCollectionYear(tableName.getNameAsString()) + "_" + CommonConstant.BATCH_GET, () ->{
            TablePool tablePool = null;
            Table table = null;
            try {
                Connection connection = getConnection(hBaseTimeout);
                tablePool = getTablePool(connection, tableName, hBaseTimeout);
                table = tablePool.borrowObject();
                return table.get(gets);
            } catch (Exception ex) {
                log.error("batchGet ->{}, hBaseTimeout -> {}，exception：{}", tableName.getNameAsString(), hBaseTimeout, ex);
                ActiveSpan.error("hBase查询出错" + ex.getMessage());
                throw ex;
            } finally {
                if (tablePool != null && table != null) {
                    tablePool.returnObject(table);
                }
            }
        }).call();
    }

    @SneakyThrows
    public static Result[] coldBatchGet(TableName tableName, List<Get> gets) {
        try {
            SphU.entry(HBASE_COLD_BATCH_GET, EntryType.IN, 1);
            // 冷存储默认5秒超时
            return batchGet(tableName, gets, DatabaseQueryConstant.TIMEOUT_5000);
        } catch (BlockException ex) {
            throw new com.wosai.upay.transaction.exception.BlockException("请求过于频繁,请稍后重试");
        } finally {
            SphU.entryEnd(1);
        }
    }

    @SneakyThrows
    public static void put(TableName tableName, Put put) {
        TimedRunnable.of(H_BASE_PREFIX + getCollectionYear(tableName.getNameAsString()) + "_" + CommonConstant.PUT, () -> {
            TablePool tablePool = null;
            Table table = null;
            try {
                Connection connection = getConnection(CommonConstant.HBASE_PUT_RPC_TIMEOUT);
                tablePool = getTablePool(connection, tableName, CommonConstant.HBASE_PUT_RPC_TIMEOUT);
                table = tablePool.borrowObject();
                table.put(put);
            } catch (Exception ex) {
                log.error("tableName -> {}，hBase写入异常", tableName.getNameAsString(), ex);
                ActiveSpan.error("hBase查询出错" + ex.getMessage());
            } finally {
                if (tablePool != null && table != null) {
                    tablePool.returnObject(table);
                }
            }
        }).run();
//         异步写入lindorm宽表
        lindormExecutor.submit(() -> putLindorm(tableName, put));
    }

    @SneakyThrows
    public static void putLindorm(TableName tableName, Put put) {
        TablePool tablePool = null;
        Table table = null;
        try {
            Connection connection = getLindormConnection(CommonConstant.HBASE_PUT_RPC_TIMEOUT);
            tablePool = getLindormTablePool(connection, tableName, CommonConstant.HBASE_PUT_RPC_TIMEOUT);
            table = tablePool.borrowObject();
            table.put(put);
        } catch (Exception ex) {
            log.error("tableName -> {}，lindorm hBase写入异常", tableName.getNameAsString(), ex);
        } finally {
            if (tablePool != null && table != null) {
                tablePool.returnObject(table);
            }
        }
    }

    @SneakyThrows
    public static void putSolr(String collection, SolrInputDocument solrInputFields) {
        CloudSolrClient cloudSolrClient = SolrHBaseUtils.getCloudSolrClient(4500);
        cloudSolrClient.setDefaultCollection(collection);
        cloudSolrClient.add(solrInputFields, 1000);
//      cloudSolrClient.commit(null,false,true);
        // 异步写入lindorm 搜索
        lindormExecutor.submit(() -> putLindormSolr(collection, solrInputFields));
    }

    @SneakyThrows
    public static void putLindormSolr(String collection, SolrInputDocument solrInputFields) {
        try {
            CloudSolrClient cloudSolrClient = SolrHBaseUtils.getLindormCloudSolrClient(4500);
            cloudSolrClient.setDefaultCollection(collection);
            cloudSolrClient.add(solrInputFields, 1000);
//      cloudSolrClient.commit(null,false,true);
        }catch (Exception e) {
            log.error("tableName -> {}，lindorm solr写入异常", collection, e);
        }
    }

    @SneakyThrows
    public static Connection getConnection(int hBaseRpcTimeout) {
        Connection connection = RPC_TIMEOUT_CONNECTION.get(hBaseRpcTimeout);
        if (connection == null) {
            String rpcTimeoutKey = String.valueOf(hBaseRpcTimeout);
            synchronized (STRING_POOL.intern(rpcTimeoutKey)) {
                SensitiveProperties sensitiveProperties = SpringUtil.getBean(SensitiveProperties.class);
                Environment env = SpringUtil.getBean(Environment.class);
                String zkHost = env.getProperty(CommonConstant.HBASE_ZKHOST);
                Configuration config = HBaseConfiguration.create();
                config.set(ClusterConnection.HBASE_CLIENT_CONNECTION_IMPL, LayeredConnectionImplementation.class.getName());
                config.set(HConstants.ZOOKEEPER_QUORUM, zkHost);
                config.set(HConstants.HBASE_CLIENT_IPC_POOL_SIZE, HBASE_CLIENT_IPC_POOL_SIZE);
                config.set(HConstants.HBASE_RPC_TIMEOUT_KEY, rpcTimeoutKey);
                config.set(HConstants.HBASE_CLIENT_RETRIES_NUMBER, HBASE_CLIENT_RETRIES_NUMBER);
                config.set(HConstants.HBASE_CLIENT_OPERATION_TIMEOUT, String.valueOf(hBaseRpcTimeout * 3 + 1000));
                config.set(ReadOnlyZKClient.RECOVERY_RETRY, RECOVERY_RETRY);
                config.set(HConstants.USERNAME, sensitiveProperties.getLindormUsername());
                config.set(HConstants.PASSWORD, sensitiveProperties.getLindormPassword());
                connection = ConnectionFactory.createConnection(config, pool);
                RPC_TIMEOUT_CONNECTION.put(hBaseRpcTimeout, connection);
            }
        }
        return connection;
    }

    @SneakyThrows
    public static Connection getLindormConnection(int hBaseRpcTimeout) {
        Connection connection = LINDORM_RPC_TIMEOUT_CONNECTION.get(hBaseRpcTimeout);
        if (connection == null) {
            String rpcTimeoutKey = String.valueOf(hBaseRpcTimeout);
            synchronized (STRING_POOL.intern(rpcTimeoutKey)) {
                SensitiveProperties sensitiveProperties = SpringUtil.getBean(SensitiveProperties.class);
                Environment env = SpringUtil.getBean(Environment.class);
                String zkHost = env.getProperty(CommonConstant.LINDORM_HBASE_ZKHOST);

                Configuration config = HBaseConfiguration.create();
                config.set(ClusterConnection.HBASE_CLIENT_CONNECTION_IMPL, LayeredConnectionImplementation.class.getName());
                config.set(HConstants.ZOOKEEPER_QUORUM, zkHost);
                config.set(HConstants.HBASE_CLIENT_IPC_POOL_SIZE, HBASE_CLIENT_IPC_POOL_SIZE);
                config.set(HConstants.HBASE_RPC_TIMEOUT_KEY, rpcTimeoutKey);
                config.set(HConstants.HBASE_CLIENT_RETRIES_NUMBER, HBASE_CLIENT_RETRIES_NUMBER);
                config.set(HConstants.HBASE_CLIENT_OPERATION_TIMEOUT, String.valueOf(hBaseRpcTimeout * 3 + 1000));
                config.set(ReadOnlyZKClient.RECOVERY_RETRY, RECOVERY_RETRY);
                config.set(HConstants.USERNAME, sensitiveProperties.getLindormUsername());
                config.set(HConstants.PASSWORD, sensitiveProperties.getLindormPassword());
                connection = ConnectionFactory.createConnection(config, pool);
                LINDORM_RPC_TIMEOUT_CONNECTION.put(hBaseRpcTimeout, connection);
            }
        }
        return connection;
    }

    public static CloudSolrClient getCloudSolrClient(int solrTimeout) {
        CloudSolrClient cloudSolrClient = SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT.get(solrTimeout);
        if (cloudSolrClient == null) {
            String socketTimeoutMillisKey = String.valueOf(solrTimeout);
            synchronized (STRING_POOL.intern(socketTimeoutMillisKey)) {
                cloudSolrClient = SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT.get(solrTimeout);
                if (cloudSolrClient == null) {
                    Environment env = SpringUtil.getBean(Environment.class);
                    String zkHost = env.getProperty(CommonConstant.SOLR_ZKHOST);
                    cloudSolrClient = new CloudSolrClient.Builder()
                            .withZkHost(zkHost)
                            .withHttpClient(httpClient)
                            .withConnectionTimeout(CommonConstant.CONNECTION_TIMEOUT_MILLIS)
                            .withSocketTimeout(solrTimeout)
                            .build();
                    SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT.put(solrTimeout, cloudSolrClient);
                }
            }
        }
        return cloudSolrClient;
    }

    public static CloudSolrClient getLindormCloudSolrClient(int solrTimeout) {
        CloudSolrClient cloudSolrClient = LINDORM_SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT.get(solrTimeout);
        if (cloudSolrClient == null) {
            String socketTimeoutMillisKey = String.valueOf(solrTimeout);
            synchronized (STRING_POOL.intern(socketTimeoutMillisKey)) {
                cloudSolrClient = LINDORM_SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT.get(solrTimeout);
                if (cloudSolrClient == null) {
                    Environment env = SpringUtil.getBean(Environment.class);
                    String zkHost = env.getProperty(CommonConstant.LINDORM_SOLR_ZKHOST);
                    cloudSolrClient = new CloudSolrClient.Builder()
                            .withZkHost(zkHost)
                            .withHttpClient(httpClient)
                            .withConnectionTimeout(CommonConstant.CONNECTION_TIMEOUT_MILLIS)
                            .withSocketTimeout(solrTimeout)
                            .build();
                    LINDORM_SOCKET_TIMEOUT_CLOUD_SOLR_CLIENT.put(solrTimeout, cloudSolrClient);
                }
            }
        }
        return cloudSolrClient;
    }

    public static TablePool getTablePool(Connection connection, TableName tableName, int hBaseTimeout) {
        String tn = tableName.getNameAsString() + hBaseTimeout;
        TablePool tablePool = TABLE_NAME_TABLE_POOL.get(tn);
        if (tablePool == null) {
            synchronized (STRING_POOL.intern(tn)) {
                tablePool = TABLE_NAME_TABLE_POOL.get(tn);
                if (tablePool == null) {
                    tablePool = new TablePool(connection, tableName, 15, 100);
                    TABLE_NAME_TABLE_POOL.put(tn, tablePool);
                }
            }
        }
        return tablePool;
    }

    public static TablePool getLindormTablePool(Connection connection, TableName tableName, int hBaseTimeout) {
        String tn = tableName.getNameAsString() + hBaseTimeout;
        TablePool tablePool = LINDORM_TABLE_NAME_TABLE_POOL.get(tn);
        if (tablePool == null) {
            synchronized (STRING_POOL.intern(tn)) {
                tablePool = LINDORM_TABLE_NAME_TABLE_POOL.get(tn);
                if (tablePool == null) {
                    tablePool = new TablePool(connection, tableName, 3, 10);
                    LINDORM_TABLE_NAME_TABLE_POOL.put(tn, tablePool);
                }
            }
        }
        return tablePool;
    }

    /**
     * 专门解析 order 和 transaction 表 reflect 列的值，由于历史原因，值可能是
     *
     * @param reflectValue byte 数组类型的值
     * @return 如果是 map 就转 map，否则转字符串
     */
    public static Object parseReflectField(byte[] reflectValue) {
        if (reflectValue == null) {
            return null;
        }

        String reflectText = new String(reflectValue);

        try {
            return JSON.parseObject(reflectText, new TypeReference<HashMap<String, Object>>() {
            });
        } catch (Exception e) {
        }
        // hbase返回的字符串带引号，需要移除
        try {
            if(!reflectText.isEmpty() && reflectText.startsWith("\"")) {
                reflectText = reflectText.substring(1, reflectText.length() -1);
            }
        } catch (Exception e) {
        }
        return reflectText;
    }

    @SneakyThrows
    public static Map<String, Object> getHbaseData(TableName tableName, Scan scan, Function<Result, Map<String, Object>> function){
        Connection connection = SolrHBaseUtils.getConnection(DatabaseQueryConstant.QUERY_THIRTEEN_SECONDS_TIMEOUT_SECONDS);
        TablePool pool = SolrHBaseUtils.getTablePool(connection, tableName, DatabaseQueryConstant.QUERY_THIRTEEN_SECONDS_TIMEOUT_SECONDS);
        Table table = pool.borrowObject();
        try {
            ResultScanner resultScanner = table.getScanner(scan);
            try {
                Stopwatch scannerWatch = Stopwatch.createStarted();
                Result scanResult = resultScanner.next();
                log.info("end of scanner, table: {}, cost: {} ms", tableName.getNameAsString(), scannerWatch.stop().elapsed(TimeUnit.MILLISECONDS));
                if (scanResult != null) {
                    return function.apply(scanResult);
                }
            } finally {
                resultScanner.close();
            }
        } finally {
            try {
                if (pool != null && table != null) {
                    pool.returnObject(table);
                }
            }catch (Exception e) {
            }
        }
        return null;
    }
}
