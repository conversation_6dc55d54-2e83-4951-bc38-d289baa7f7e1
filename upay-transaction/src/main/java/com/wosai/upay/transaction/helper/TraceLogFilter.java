package com.wosai.upay.transaction.helper;

import org.slf4j.MDC;
import org.slf4j.Marker;

import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.upay.transaction.constant.CommonConstant;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;


/**
 * TraceLogFilter
 *
 * <AUTHOR>
 */
public class TraceLogFilter extends TurboFilter {

    @Override
    public FilterReply decide(Marker marker, Logger logger, Level level, String format, Object[] params, Throwable t) {
        String traceId = TraceContext.traceId();
        CarrierItem item = TracingUtil.getTraceCarrierItem();
        if(item != null) {
            traceId = item.getTraceId();
        }
        MDC.put(CommonConstant.TRACE_ID, traceId);
        return FilterReply.NEUTRAL;
    }
}