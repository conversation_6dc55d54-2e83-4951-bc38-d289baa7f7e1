package com.wosai.upay.transaction.service.model.po;

import com.wosai.upay.cashdesk.api.result.SummaryTradeResponse;
import com.wosai.upay.cashdesk.api.result.TotalTradeResponse;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.model.vo.TransactionSumV;
import com.wosai.upay.transaction.util.SolrUtils;
import com.wosai.upay.transaction.util.TransactionUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.solr.common.util.NamedList;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.sum.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.valuecount.ParsedValueCount;

import java.math.BigDecimal;
import java.util.List;


@Accessors(chain = true)
@Data
public class TransactionSummaryPo {


    public static TransactionSummaryPo NULL_VALUE = new TransactionSummaryPo();

    public static boolean isEmpty(TransactionSummaryPo po) {
        return po.equals(NULL_VALUE);
    }

    private Object key;

    private long paid_amount = 0L;
    private long paid_count = 0L;
    private long deposit_amount = 0L;
    private long deposit_count = 0L;
    private long refunded_amount = 0L;
    private long refunded_count = 0L;
    private long deposit_canceled_amount = 0L;
    private long deposit_canceled_count = 0L;
    private long canceled_amount = 0L;
    private long canceled_count = 0L;
    private long charge_amount = 0L;
    private long charge_count = 0L;
    private long charge_refund_amount = 0L;
    private long charge_refund_count = 0L;
    private long order_take_amount = 0L;
    private long order_take_count = 0L;
    private long order_take_refund_amount = 0L;
    private long order_take_refund_count = 0L;
    private long store_pay_amount = 0L;
    private long store_pay_count = 0L;
    private long store_refund_amount = 0L;
    private long store_refund_count = 0L;
    private long store_in_amount = 0L;
    private long store_in_count = 0L;
    private long store_in_refund_amount = 0L;
    private long store_in_refund_count = 0L;
    private long deposit_freeze_amount = 0L;
    private long deposit_freeze_count = 0L;
    private long deposit_freeze_cancel_amount = 0L;
    private long deposit_freeze_cancel_count = 0L;

    

    /**
     * solr type的结果转换对象
     */
    public static TransactionSummaryPo buildFromSolrResult(NamedList<?> entry) {
        return new TransactionSummaryPo()
                .setKey(entry.get("val"))
                .setPaid_amount(SolrUtils.double2LongValue(entry.get(Transaction.PAID_AMOUNT)))
                .setPaid_count(SolrUtils.double2IntValue(entry.get(Transaction.PAID_COUNT)))
                .setDeposit_amount(SolrUtils.double2LongValue(entry.get(Transaction.DEPOSIT_AMOUNT)))
                .setDeposit_count(SolrUtils.double2IntValue(entry.get(Transaction.DEPOSIT_COUNT)))
                .setRefunded_amount(SolrUtils.double2LongValue(entry.get(Transaction.REFUNDED_AMOUNT)))
                .setRefunded_count(SolrUtils.double2IntValue(entry.get(Transaction.REFUNDED_COUNT)))
                .setDeposit_canceled_amount(SolrUtils.double2LongValue(entry.get(Transaction.DEPOSIT_CANCELED_AMOUNT)))
                .setDeposit_canceled_count(SolrUtils.double2IntValue(entry.get(Transaction.DEPOSIT_CANCELED_COUNT)))
                .setCanceled_amount(SolrUtils.double2LongValue(entry.get(Transaction.CANCELED_AMOUNT)))
                .setCanceled_count(SolrUtils.double2IntValue(entry.get(Transaction.CANCELED_COUNT)))
                .setCharge_amount(SolrUtils.double2LongValue(entry.get(Transaction.CHARGE_AMOUNT)))
                .setCharge_count(SolrUtils.double2IntValue(entry.get(Transaction.CHARGE_COUNT)))
                .setCharge_refund_amount(SolrUtils.double2LongValue(entry.get(Transaction.CHARGE_REFUND_AMOUNT)))
                .setCharge_refund_count(SolrUtils.double2IntValue(entry.get(Transaction.CHARGE_REFUND_COUNT)))
                .setOrder_take_amount(SolrUtils.double2LongValue(entry.get(Transaction.ORDER_TAKE_AMOUNT)))
                .setOrder_take_count(SolrUtils.double2IntValue(entry.get(Transaction.ORDER_TAKE_COUNT)))
                .setOrder_take_refund_amount(SolrUtils.double2LongValue(entry.get(Transaction.ORDER_TAKE_REFUND_AMOUNT)))
                .setOrder_take_refund_count(SolrUtils.double2IntValue(entry.get(Transaction.ORDER_TAKE_REFUND_COUNT)))
                .setStore_pay_amount(SolrUtils.double2LongValue(entry.get(Transaction.STORE_PAY_AMOUNT)))
                .setStore_pay_count(SolrUtils.double2IntValue(entry.get(Transaction.STORE_PAY_COUNT)))
                .setStore_refund_amount(SolrUtils.double2LongValue(entry.get(Transaction.STORE_REFUND_AMOUNT)))
                .setStore_refund_count(SolrUtils.double2IntValue(entry.get(Transaction.STORE_REFUND_COUNT)))
                .setStore_in_amount(SolrUtils.double2LongValue(entry.get(Transaction.STORE_IN_AMOUNT)))
                .setStore_in_count(SolrUtils.double2IntValue(entry.get(Transaction.STORE_IN_COUNT)))
                .setStore_in_refund_amount(SolrUtils.double2LongValue(entry.get(Transaction.STORE_IN_REFUND_AMOUNT)))
                .setStore_in_refund_count(SolrUtils.double2IntValue(entry.get(Transaction.STORE_IN_REFUND_COUNT)))
                .setDeposit_freeze_amount(SolrUtils.double2LongValue(entry.get(Transaction.DEPOSIT_FREEZE_AMOUNT)))
                .setDeposit_freeze_count(SolrUtils.double2IntValue(entry.get(Transaction.DEPOSIT_FREEZE_COUNT)))
                .setDeposit_freeze_cancel_amount(SolrUtils.double2LongValue(entry.get(Transaction.DEPOSIT_FREEZE_CANCELED_AMOUNT)))
                .setDeposit_freeze_cancel_count(SolrUtils.double2IntValue(entry.get(Transaction.DEPOSIT_FREEZE_CANCELED_COUNT)));
    }

    private static void categoryForSummary(TransactionSummaryPo po, int type, long count, long sum) {
        if (type == Transaction.TYPE_PAYMENT) {
            po.setPaid_amount(sum);
            po.setPaid_count(count);
        } else if (type == Transaction.TYPE_REFUND_REVOKE) {
//            po.setre(sum);BuyerCommonService.java
//            po.setPaid_count(count);
        } else if (type == Transaction.TYPE_REFUND) {
            po.setRefunded_amount(sum);
            po.setRefunded_count(count);
        } else if (type == Transaction.TYPE_CANCEL) {
            po.setCanceled_amount(sum);
            po.setCanceled_count(count);
        } else if (type == Transaction.TYPE_DEPOSIT_FREEZE) {
            po.setDeposit_freeze_amount(sum);
            po.setDeposit_freeze_count(count);
        } else if (type == Transaction.TYPE_DEPOSIT_CANCEL) {
            po.setDeposit_freeze_cancel_amount(sum);
            po.setDeposit_freeze_cancel_count(count);
        } else if (type == Transaction.TYPE_DEPOSIT_CONSUME) {
            po.setDeposit_amount(sum);
            po.setDeposit_count(count);
        } else if (type == Transaction.TYPE_DEPOSIT_CONSUME_CANCEL) {
            po.setDeposit_canceled_amount(sum);
            po.setDeposit_canceled_count(count);
        } else if (type == Transaction.TYPE_CHARGE) {
            po.setCharge_amount(sum);
            po.setCharge_count(count);
        } else if (type == Transaction.TYPE_CHARGE_REFUND) {
            po.setCharge_refund_amount(sum);
            po.setCharge_refund_count(count);
        } else if (type == Transaction.TYPE_ORDER_TAKE) {
            po.setOrder_take_amount(sum);
            po.setOrder_take_count(count);
        } else if (type == Transaction.TYPE_ORDER_TAKE_REFUND) {
            po.setOrder_take_refund_amount(sum);
            po.setOrder_take_refund_count(count);
        } else if (type == Transaction.TYPE_STORE_PAY) {
            po.setStore_pay_amount(sum);
            po.setStore_pay_count(count);
        } else if (type == Transaction.TYPE_STORE_REFUND) {
            po.setStore_refund_amount(sum);
            po.setStore_refund_count(count);
        } else if (type == Transaction.TYPE_STORE_IN) {
            po.setStore_in_amount(sum);
            po.setStore_in_count(count);
        } else if (type == Transaction.TYPE_STORE_IN_REFUND) {
            po.setStore_in_refund_amount(sum);
            po.setStore_in_refund_count(count);
        }
    }

    /**
     * es type聚合出结果转换对象
     */
    public static TransactionSummaryPo buildFromEsTypeBuckets(List<? extends Terms.Bucket> buckets) {
        TransactionSummaryPo po = new TransactionSummaryPo();
        buckets.forEach(bucket -> {
            ParsedValueCount countTerms = bucket.getAggregations().get(Transaction.TYPE);
            ParsedSum sumTerms = bucket.getAggregations().get(Transaction.ORIGINAL_AMOUNT);
            int type = ((Long) bucket.getKey()).intValue();
            long count = countTerms.getValue();
            long sum = new BigDecimal(sumTerms.getValue() + "").longValue();
            categoryForSummary(po, type, count, sum);
        });
        return po;

    }

    public static TransactionSummaryPo buildFromCashDeskTradeListSummary(List<TotalTradeResponse> totalTradeResponses) {
        if (null == totalTradeResponses) {
            return TransactionSummaryPo.NULL_VALUE;
        }
        TransactionSummaryPo po = new TransactionSummaryPo();
        totalTradeResponses.forEach(response -> {
            int type = response.getType();
            long count = response.getTradeCount();
            long sum = response.getTradeAmount();
            categoryForSummary(po, type, count, sum);
        });
        return po;

    }

    /**
     * 收银台聚合出结果转换对象
     */
    public static TransactionSummaryPo buildFromCashDeskSummary(SummaryTradeResponse summaryTradeResponses) {
        TransactionSummaryPo po = new TransactionSummaryPo();
        if (summaryTradeResponses != null) {
            po.setKey(summaryTradeResponses.getPayway());
            int type = summaryTradeResponses.getType();
            long count = summaryTradeResponses.getSumCount();
            long sum = summaryTradeResponses.getSumAmount();
            categoryForSummary(po, type, count, sum);
        }
        return po;

    }

    /**
     * 两个结果sum
     */
    public static TransactionSummaryPo summarySelf(TransactionSummaryPo x1, TransactionSummaryPo x2) {
        return new TransactionSummaryPo()
                .setKey(x1.getKey())
                .setPaid_amount(x1.getPaid_amount() + x2.getPaid_amount())
                .setPaid_count(x1.getPaid_count() + x2.getPaid_count())
                .setDeposit_amount(x1.getDeposit_amount() + x2.getDeposit_amount())
                .setDeposit_count(x1.getDeposit_count() + x2.getDeposit_count())
                .setRefunded_amount(x1.getRefunded_amount() + x2.getRefunded_amount())
                .setRefunded_count(x1.getRefunded_count() + x2.getRefunded_count())
                .setDeposit_canceled_amount(x1.getDeposit_canceled_amount() + x2.getDeposit_canceled_amount())
                .setDeposit_canceled_count(x1.getDeposit_canceled_count() + x2.getDeposit_canceled_count())
                .setCanceled_amount(x1.getCanceled_amount() + x2.getCanceled_amount())
                .setCanceled_count(x1.getCanceled_count() + x2.getCanceled_count())
                .setCharge_amount(x1.getCharge_amount() + x2.getCharge_amount())
                .setCharge_count(x1.getCharge_count() + x2.getCharge_count())
                .setCharge_refund_amount(x1.getCharge_refund_amount() + x2.getCharge_refund_amount())
                .setCharge_refund_count(x1.getCharge_refund_count() + x2.getCharge_refund_count())
                .setOrder_take_amount(x1.getOrder_take_amount() + x2.getOrder_take_amount())
                .setOrder_take_count(x1.getOrder_take_count() + x2.getOrder_take_count())
                .setOrder_take_refund_amount(x1.getOrder_take_refund_amount() + x2.getOrder_take_refund_amount())
                .setOrder_take_refund_count(x1.getOrder_take_refund_count() + x2.getOrder_take_refund_count())
                .setStore_pay_amount(x1.getStore_pay_amount() + x2.getStore_pay_amount())
                .setStore_pay_count(x1.getStore_pay_count() + x2.getStore_pay_count())
                .setStore_refund_amount(x1.getStore_refund_amount() + x2.getStore_refund_amount())
                .setStore_refund_count(x1.getStore_refund_count() + x2.getStore_refund_count())
                .setStore_in_amount(x1.getStore_in_amount() + x2.getStore_in_amount())
                .setStore_in_count(x1.getStore_in_count() + x2.getStore_in_count())
                .setStore_in_refund_amount(x1.getStore_in_refund_amount() + x2.getStore_in_refund_amount())
                .setStore_in_refund_count(x1.getStore_in_refund_count() + x2.getStore_in_refund_count())
                .setDeposit_freeze_amount(x1.getDeposit_freeze_amount() + x2.getDeposit_freeze_amount())
                .setDeposit_freeze_count(x1.getDeposit_freeze_count() + x2.getDeposit_freeze_count())
                .setDeposit_freeze_cancel_amount(x1.getDeposit_freeze_cancel_amount() + x2.getDeposit_freeze_cancel_amount())
                .setDeposit_freeze_cancel_count(x1.getDeposit_freeze_cancel_count() + x2.getDeposit_freeze_cancel_count());
    }

    /**
     * 收款 = 移动收款 + 现金记账 + 点单外卖 + 储值核销
     * 退款 = 移动收退款款 + 现金记账退款 + 点单外卖退款 + 储值核销退款
     * 交易金额 = 收款金额 - 退款金额
     *
     * @return sumV
     */
    public TransactionSumV buildSumV() {
        return this.buildSumV(null);
    }

    /**
     * 收款 = 移动收款 + 现金记账 + 点单外卖 + 储值核销
     * 退款 = 移动收退款款 + 现金记账退款 + 点单外卖退款 + 储值核销退款
     * 交易金额 = 收款金额 - 退款金额
     *
     * @return sumV
     */
    public TransactionSumV buildSumV(List<String> summaryExts) {
        TransactionSumV sumV = new TransactionSumV();
        if (key != null) {
            sumV.setPayWay(Integer.parseInt(key + ""));
        }
        //收款
        sumV.setPaidAmount(paid_amount + charge_amount + order_take_amount + store_pay_amount);
        sumV.setPaidCount(Long.valueOf(paid_count + charge_count + order_take_count + store_pay_count).intValue());

        //退款
        sumV.setRefundedAmount(refunded_amount + charge_refund_amount + order_take_refund_amount + store_refund_amount);
        sumV.setRefundedCount(Long.valueOf(refunded_count + charge_refund_count + order_take_refund_count + store_refund_count).intValue());

        //撤销
        sumV.setCanceledAmount(canceled_amount);
        sumV.setCanceledCount(Long.valueOf(canceled_count).intValue());

        //预授权完成相关
        sumV.setDepositAmount(deposit_amount);
        sumV.setDepositCount(Long.valueOf(deposit_count).intValue());
        sumV.setDepositCanceledAmount(deposit_canceled_amount);
        sumV.setDepositCanceledCount(Long.valueOf(deposit_canceled_count).intValue());

        //交易笔数 =  收款笔数 + 退款笔数 + 预授权成功笔数 + 预授权取消笔数  (金额同理)
        sumV.setSalesAmount(sumV.getDepositAmount() + sumV.getPaidAmount() - sumV.getRefundedAmount() - sumV.getCanceledAmount() - sumV.getDepositCanceledAmount());
        sumV.setSalesCount(sumV.getDepositCount() + sumV.getPaidCount() + sumV.getRefundedCount() + sumV.getCanceledCount() + sumV.getDepositCanceledCount());

        //储值 充值，退款，总 (笔数金额)
        sumV.setStoreInAmount(store_in_amount);
        sumV.setStoreInCount(store_in_count);
        sumV.setStoreInRefundAmount(store_in_refund_amount);
        sumV.setStoreInRefundCount(store_in_refund_count);
        sumV.setStoreInTotalAmount(store_in_amount - store_in_refund_amount);
        sumV.setStoreInTotalCount(store_in_count + store_in_refund_count);

        // 预授权扩展参数特殊处理，笔数计入总交易笔数，金额不计入
        if (TransactionUtil.summaryDeposit(summaryExts)) {
            sumV.setSalesCount(sumV.getSalesCount() + sumV.getDepositFreezeCount() + sumV.getDepositFreezeCanceledCount());
        }
        // 预授权
        sumV.setDepositFreezeAmount(deposit_freeze_amount);
        sumV.setDepositFreezeCount(Long.valueOf(deposit_freeze_count).intValue());
        sumV.setDepositFreezeCanceledAmount(deposit_freeze_cancel_amount);
        sumV.setDepositFreezeCount(Long.valueOf(deposit_freeze_cancel_count).intValue());
        return sumV;
    }
}
