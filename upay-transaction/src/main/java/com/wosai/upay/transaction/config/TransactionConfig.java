package com.wosai.upay.transaction.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.googlecode.jsonrpc4j.DefaultErrorResolver;
import com.googlecode.jsonrpc4j.MultipleErrorResolver;
import com.wosai.upay.common.helper.CommonServicePostProcessor;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.common.helper.UpayMethodValidationPostProcessor;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.helper.DBSelectPostProcessor;
import com.wosai.upay.transaction.helper.DBSelectServiceMethodInterceptor;
import com.wosai.upay.transaction.helper.ExceptionBaseErrorResolver;
import com.wosai.upay.transaction.helper.UpayServiceMethodInterceptor;
import com.wosai.upay.transaction.util.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.beanvalidation.MethodValidationPostProcessor;
import org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping;

import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class TransactionConfig {
    @Bean
    public MethodValidationPostProcessor methodValidationPostProcessor() {
        UpayMethodValidationPostProcessor processor = new UpayMethodValidationPostProcessor();
        processor.setValidatedAnnotationType(CommonTransactionValidated.class);
        return processor;
    }

    @Bean
    public UpayServiceMethodInterceptor serviceMethodInterceptor() {
        return new UpayServiceMethodInterceptor();
    }

    @Bean
    public CommonServicePostProcessor commonServicePostProcessor() {
        CommonServicePostProcessor processor = new CommonServicePostProcessor();
        processor.setAdvice(serviceMethodInterceptor());
        processor.setAnnotationTypeClass(CommonTransactionService.class.getName());
        return processor;
    }

    @Bean
    public BeanNameUrlHandlerMapping beanNameUrlHandlerMapping() {
        return new BeanNameUrlHandlerMapping();
    }

    @Bean
    public MultipleErrorResolver rpcErrorResolver() {
        return new MultipleErrorResolver(
                ExceptionBaseErrorResolver.INSTANCE,
                DefaultErrorResolver.INSTANCE
        );
    }

    @Bean
    public SpringContextHolder springContextHolder() {
        return new SpringContextHolder();
    }

    @Bean
    public MyObjectMapper myObjectMapper() {
        return new MyObjectMapper();
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        objectMapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
        // map类型为null时也要进行返回
        objectMapper.configOverride(Map.class).setInclude(JsonInclude.Value.construct(JsonInclude.Include.NON_NULL, JsonInclude.Include.ALWAYS));
        return objectMapper;
    }

    @Bean
    public DBSelectServiceMethodInterceptor dbSelectServiceMethodInterceptor() {
        return new DBSelectServiceMethodInterceptor();
    }

    @Bean
    public DBSelectPostProcessor dbSelectPostProcessor() {
        DBSelectPostProcessor processor = new DBSelectPostProcessor();
        processor.setAdvice(dbSelectServiceMethodInterceptor());
        return processor;
    }

    @Bean
    public SpringUtil springUtil() {
        return new SpringUtil();
    }

    @Bean
    public OdpsUtil odpsUtil() {
        return new OdpsUtil();
    }

    @Bean
    public MetaCacheUtil metaCacheUtil() {
        return new MetaCacheUtil();
    }

    @Bean
    public OrderUtil orderUtil() {
        return new OrderUtil();
    }

    @Bean
    public OssFileUploader ossFileUploader() {
        return new OssFileUploader();
    }

    @Bean
    public SmtpMailSender smtpMailSender() {
        return new SmtpMailSender();
    }

    @Bean
    public StatementTransactionUtil statementTransactionUtil() {
        return new StatementTransactionUtil();
    }

    @Bean
    public StatementWalletUtils statementWalletUtils() {
        return new StatementWalletUtils();
    }

    @Bean
    public WithdrawExportUtils withdrawExportUtils() {
        return new WithdrawExportUtils();
    }

    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(50);
        taskExecutor.setMaxPoolSize(100);
        taskExecutor.setQueueCapacity(100);
        taskExecutor.setKeepAliveSeconds(120);
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskExecutor.setThreadNamePrefix("Async-");
        taskExecutor.initialize();
        return taskExecutor;
    }

}
