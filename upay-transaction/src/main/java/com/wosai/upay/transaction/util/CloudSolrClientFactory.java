package com.wosai.upay.transaction.util;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.solr.client.solrj.impl.CloudSolrClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class CloudSolrClientFactory extends BasePooledObjectFactory<CloudSolrClient> {

    private String zkHost;

    public CloudSolrClientFactory(String zkHost) {
        this.zkHost = zkHost;
    }

    @Override
    public CloudSolrClient create() {
        CloudSolrClient cloudSolrClient = new CloudSolrClient.Builder().withZkHost(zkHost)
                .withConnectionTimeout(3000).withSocketTimeout(5000).build();
        return cloudSolrClient;
    }

    @Override
    public PooledObject<CloudSolrClient> wrap(CloudSolrClient obj) {
        return new DefaultPooledObject<>(obj);
    }

    @Override
    @SneakyThrows
    public void destroyObject(PooledObject<CloudSolrClient> p) {
        CloudSolrClient socket = p.getObject();
        socket.close();
        super.destroyObject(p);
    }

}
