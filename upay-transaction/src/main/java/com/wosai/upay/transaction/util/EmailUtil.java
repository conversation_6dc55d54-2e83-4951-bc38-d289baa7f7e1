package com.wosai.upay.transaction.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/11/8 5:48 下午
 */
public class EmailUtil {


    public static final String EMAIL_PATTERN = "^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$";


    /**
     * 邮箱格式 校验
     *
     * @param email
     * @return
     */
    public static boolean check(String email) {
        Pattern pattern = Pattern.compile(EMAIL_PATTERN);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }
}
