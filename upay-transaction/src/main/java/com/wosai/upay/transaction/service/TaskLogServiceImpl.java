package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * 对账单导出日志相关接口
 *
 * <AUTHOR>
 */


@AutoJsonRpcServiceImpl
@Service
public class TaskLogServiceImpl implements TaskLogService {
    public static final Logger logger = LoggerFactory.getLogger(TaskLogServiceImpl.class);

    @Autowired
    com.wosai.upay.task.center.service.TaskLogService taskTaskLogService;


    @Override
    public Map createTaskApplyLog(Map taskApplyLog) {
        return taskTaskLogService.createTaskApplyLog(taskApplyLog);
    }

    @Override
    public void deleteTaskApplyLog(String taskApplyLogId) {
        taskTaskLogService.deleteTaskApplyLog(taskApplyLogId);
    }

    @Override
    public Map updateTaskApplyLog(Map taskApplyLog) {
        return taskTaskLogService.updateTaskApplyLog(taskApplyLog);
    }

    @Override
    public void updateTaskApplyLogStatus(String id, int status) {
        taskTaskLogService.updateTaskApplyLogStatus(id, status);
    }

    @Override
    public Map getTaskApplyLog(String taskApplyLogId) {
        return taskTaskLogService.getTaskApplyLog(taskApplyLogId);
    }

    @Override
    public ListResult findTaskApplyLogs(PageInfo pageInfo, Map queryFilter) {
        return taskTaskLogService.findTaskApplyLogs(pageInfo, queryFilter);
    }

    @Override
    public Boolean getAndSetResultIfSameSuccSignature(String taskLogId) {
        return taskTaskLogService.getAndSetResultIfSameSuccSignature(taskLogId);
    }

    @Override
    public String getSameTaskFileUrl(String taskLogId) {
        return taskTaskLogService.getSameTaskFileUrl(taskLogId);
    }

    public void updateTasksMtime(List<String> taskLogIds, Long mtime) {
        taskTaskLogService.updateTasksMtime(taskLogIds, mtime);
    }

    public List getHeartbeatStopTaskList(long start) {
        return taskTaskLogService.getHeartbeatStopTaskList(start);
    }



}
