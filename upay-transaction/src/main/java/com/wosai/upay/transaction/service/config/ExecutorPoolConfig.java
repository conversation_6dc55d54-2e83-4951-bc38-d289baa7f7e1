package com.wosai.upay.transaction.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ExecutorPoolConfig {
    /**
     * wallet_change_log 查 HBase 时的线程池
     */
    @Bean
    public Executor walletChangeLogHBaseExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(4);
        taskExecutor.setMaxPoolSize(4);
        taskExecutor.setAllowCoreThreadTimeOut(true);
        taskExecutor.setQueueCapacity(2000);
        taskExecutor.setKeepAliveSeconds(3600);
        taskExecutor.setThreadNamePrefix("wallet-hbase-");
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(30);
        taskExecutor.setRejectedExecutionHandler(((r, executor) -> {
            String message = String.format("线程池已满, 线程池名称=statsExecutor, 核心线程池大小=%s, 最大线程池大小=%s, 当前活跃线程数=%s, 当前任务数=%s",
                    executor.getCorePoolSize(), executor.getMaximumPoolSize(), executor.getActiveCount(), executor.getTaskCount());
            log.error(message);
        }));

        return taskExecutor;
    }
}
