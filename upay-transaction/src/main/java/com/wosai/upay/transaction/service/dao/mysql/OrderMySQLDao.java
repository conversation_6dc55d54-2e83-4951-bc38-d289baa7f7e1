package com.wosai.upay.transaction.service.dao.mysql;

import com.wosai.upay.transaction.service.model.query.OrderHBaseQuery;

import java.util.List;
import java.util.Map;

/**
 * MySQL-based order data access interface
 * Replaces HBase/Solr queries for order data
 */
public interface OrderMySQLDao {

    /**
     * Query order list based on criteria
     */
    List<Map<String, Object>> queryList(OrderHBaseQuery query);

    /**
     * Count orders based on criteria
     */
    Long count(OrderHBaseQuery query);

    /**
     * Query single order by SN
     */
    Map<String, Object> queryBySn(String merchantId, String sn, long start, long end);
}