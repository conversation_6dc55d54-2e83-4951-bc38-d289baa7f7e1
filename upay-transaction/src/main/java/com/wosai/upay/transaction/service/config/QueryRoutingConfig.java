package com.wosai.upay.transaction.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Configuration for query routing between HBase/Solr and MySQL
 */
@Component
public class QueryRoutingConfig {

    @Value("${query.routing.mysql.enabled:false}")
    private boolean mysqlEnabled;

    @Value("${query.routing.mysql.percentage:0}")
    private int mysqlPercentage;

    @Value("${query.routing.mysql.merchant.whitelist:}")
    private String mysqlMerchantWhitelist;

    @Value("${query.routing.mysql.merchant.blacklist:}")
    private String mysqlMerchantBlacklist;

    public boolean isMysqlEnabled() {
        return mysqlEnabled;
    }

    public void setMysqlEnabled(boolean mysqlEnabled) {
        this.mysqlEnabled = mysqlEnabled;
    }

    public int getMysqlPercentage() {
        return mysqlPercentage;
    }

    public void setMysqlPercentage(int mysqlPercentage) {
        this.mysqlPercentage = mysqlPercentage;
    }

    public String getMysqlMerchantWhitelist() {
        return mysqlMerchantWhitelist;
    }

    public void setMysqlMerchantWhitelist(String mysqlMerchantWhitelist) {
        this.mysqlMerchantWhitelist = mysqlMerchantWhitelist;
    }

    public String getMysqlMerchantBlacklist() {
        return mysqlMerchantBlacklist;
    }

    public void setMysqlMerchantBlacklist(String mysqlMerchantBlacklist) {
        this.mysqlMerchantBlacklist = mysqlMerchantBlacklist;
    }
}