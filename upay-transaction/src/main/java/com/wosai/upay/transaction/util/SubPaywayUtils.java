package com.wosai.upay.transaction.util;

        import java.util.HashMap;
        import java.util.Map;

/**
 * see 支付源列表以及通道: https://confluence.wosai-inc.com/pages/viewpage.action?pageId=1182225
 *
 * <AUTHOR>
 * @date 2020/05/12
 */
public final class SubPaywayUtils {
    private static final Map<String, String> SUB_PAYWAY_MAP = new HashMap<String, String>(){{
        put("1", "B扫C");
        put("2", "C扫B");
        put("3", "WAP支付");
        put("4", "小程序支付");
        put("5", "APP支付");
        put("6","H5支付");
    }};

    public static String getSubPaywayDesc(String code) {
        String value = SUB_PAYWAY_MAP.get(code);

        return value != null ? value : "";
    }
}
