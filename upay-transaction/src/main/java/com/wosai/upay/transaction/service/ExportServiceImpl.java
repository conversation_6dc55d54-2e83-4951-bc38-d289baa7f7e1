package com.wosai.upay.transaction.service;


import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.transaction.model.TTaskInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.Future;

/**
 * 导出任务调度相关接口
 *
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
public class ExportServiceImpl implements ExportService {
    public static final Logger logger = LoggerFactory.getLogger(ExportServiceImpl.class);


    @Autowired
    com.wosai.upay.task.center.service.ExportService taskExportService;




    /**
     * 生成对账单导出任务
     *
     * @param taskInfo
     * @param queryFilter
     * @return
     */
    @Override
    public Map createExportStatementTask(Map<String, Object> taskInfo, Map<String, Object> queryFilter) {
        return taskExportService.createExportStatementTask(taskInfo, queryFilter);
    }

    @Override
    public void submitToExecutor(String taskLogId) {
        taskExportService.submitToExecutor(taskLogId);

    }


    @Override
    public Map<String, Future> getTaskFutureMap() {
        return taskExportService.getTaskFutureMap();
    }

    @Override
    public Collection<TTaskInfo> getTaskInfo(boolean isSimple) {

        Collection<com.wosai.upay.task.center.model.TTaskInfo> taskInfos = taskExportService.getTaskInfo(isSimple);
        Collection<TTaskInfo> res = new ArrayList<>();
        taskInfos.forEach(item -> {
            TTaskInfo taskInfo = new TTaskInfo();
            taskInfo.setTaskNum(item.getTaskNum());
            taskInfo.setType(item.getType());
            taskInfo.setBlockNum(item.getBlockNum());
            res.add(taskInfo);
        });
        return res;

    }

    @Override
    public void deleteTaskInTaskFutureMap(String taskLogId) {
        taskExportService.deleteTaskInTaskFutureMap(taskLogId);
    }

    @Override
    public void clearTaskFutureMap() {
        taskExportService.clearTaskFutureMap();
    }

    @Override
    public void doExportOrder(String taskLogId) throws IOException {
        taskExportService.doExportOrder(taskLogId);

    }

    @Override
    public void doExportStatement(String taskLogId) throws Exception {
        taskExportService.doExportStatement(taskLogId);
    }

    @Override
    public void doGroupStatementExportStatement(String taskLogId) throws IOException {
        taskExportService.doGroupStatementExportStatement(taskLogId);

    }



    @Override
    public boolean isKAStatement(String merchantId) {
        return taskExportService.isKAStatement(merchantId);
    }

    @Override
    public boolean isGroupKAStatement(String groupSn, List<String> merchantIds) {
        return taskExportService.isGroupKAStatement(groupSn, merchantIds);
    }


}
