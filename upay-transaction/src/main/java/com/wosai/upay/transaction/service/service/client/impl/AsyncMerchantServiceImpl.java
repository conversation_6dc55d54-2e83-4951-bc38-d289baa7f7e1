package com.wosai.upay.transaction.service.service.client.impl;

import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.transaction.service.service.client.IAsyncMerchantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@Service
public class AsyncMerchantServiceImpl implements IAsyncMerchantService {

    @Autowired
    public MerchantService merchantService;

    @Async
    @Override
    public Future<Map> getMerchantBySn(String merchantSn, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<Map>(merchantService.getMerchantBySn(merchantSn));
    }

    @Async
    @Override
    public Future<Map> getMerchant(String merchantId, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<Map>(merchantService.getMerchant(merchantId));
    }
}
