package com.wosai.upay.transaction.mq.handler;

import com.wosai.common.retry.RxRetryUtil;
import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.upay.transaction.constant.EsConstant;
import com.wosai.upay.transaction.util.DateTimeUtil;
import com.wosai.upay.transaction.util.WarnUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class TranIndexHandler {

    @Resource
    private RestHighLevelClient tradeMemoRestHighLevelClient;

    private final String TRACE_NAME = "es-bulk";

    @Timed(value = TRACE_NAME)
    public void handle(List<Map> tran) {
        BulkRequest bulkRequest = new BulkRequest();
        log.info("tran size:{}", tran.size());
        tran.forEach(t-> bulkRequest.add(new IndexRequest(String.format(EsConstant.INDEX_QUARTER
                , DateTimeUtil.yearQuarter(MapUtils.getLongValue(t, DaoConstants.CTIME)))
                , EsConstant.ES_TYPE
                , MapUtils.getString(t, EsConstant.TSN)).source(t)));
        try {
            RxRetryUtil.just().count(4).span(500, 1000, 2000).addLogMsg("bulk建索引").execute(() -> {
                BulkResponse bulkResponse = tradeMemoRestHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                if(bulkResponse.hasFailures()){
                    throw new RuntimeException(bulkResponse.buildFailureMessage());
                }
                return null;
            });
        }catch (Exception ex){
            log.error("bulk建索引失败,bulkRequest => {}", WosaiJsonUtils.toJSONString(tran), ex);
            StringBuilder stringBuilder = new StringBuilder();
            tran.forEach(t ->
                    stringBuilder.append(" ").append(MapUtils.getString(t, EsConstant.TSN)).append(" ")
            );
            WarnUtil.warn("es同步,bulk建索引失败,单号 => " + stringBuilder.toString() + "," + ex.getMessage());
            ActiveSpan.error(ex);
        }
    }
}