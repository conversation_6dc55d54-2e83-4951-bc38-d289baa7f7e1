package com.wosai.upay.transaction.service.api;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DateTimeConst;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.service.OrderService;
import com.wosai.upay.transaction.service.OrderServiceV2;
import com.wosai.upay.transaction.util.OldOrderUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.TreeMap;

/**
 * OrderServiceOpenImpl
 *
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
public class OrderServiceV2Impl implements OrderServiceV2 {

    @Autowired
    private OrderService orderService;
    @Autowired
    private MerchantService merchantService;

    @Override
    public Map<String, Object> getOrder(Map<String, Object> params) {
        String merchantId = MapUtils.getString(params, "merchant_id");
        String orderSn = MapUtils.getString(params, CommonConstant.ORDER_SN);
        if (StringUtils.isEmpty(merchantId)) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "merchant_id  不能为空");
        }
        if (StringUtils.isEmpty(orderSn)) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "orderSn  不能为空");
        }
        Map<String, Object> result = orderService.getOrderDetailsByMerchantIdAndOrderSn(merchantId, orderSn);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        result = convert(result);
        // 设置商户名
        Map<String, Object> merchantInfo = merchantService.getMerchant(merchantId);
        if (merchantInfo != null) {
            result.put("merchant_name", MapUtils.getString(merchantInfo, Merchant.NAME));
        }
        return result;
    }

    @NotNull
    private Map<String, Object> convert(@NotNull Map<String, Object> source) {
        Assert.notNull(source, "source cannot be null");

        Map<String, Object> target = new TreeMap<>();

        target.put("order_sn", MapUtils.getString(source, Order.SN));
        String oldStatus = "" + OldOrderUtils.toOldOrderStatus(MapUtils.getIntValue(source, Order.STATUS));
        target.put("status", oldStatus);
        String oldPayWay = OldOrderUtils.toOldPayWay(MapUtils.getIntValue(source, Order.PAYWAY));
        long totalFee = MapUtils.getLongValue(source, Order.NET_ORIGINAL);
        target.put("total_fee", "" + (totalFee / 100.0)); // 单位：元
        long originFee = MapUtils.getLongValue(source, Order.ORIGINAL_TOTAL);
        target.put("origin_fee", "" + (originFee / 100.0)); // 单位：元
        target.put("product_remark", MapUtils.getString(source, Order.REFLECT));
        String ctime = new DateTime(MapUtils.getLong(source, DaoConstants.CTIME)).toString(DateTimeConst.PATTERN_YYYY_MM_DD_HH_MM_SS);
        target.put("order_pay_time", ctime);
        target.put("buyer_account", MapUtils.getString(source, Order.BUYER_LOGIN));

        // 商户优惠
        long mchFavorableAmount = MapUtils.getLongValue(source, Order.MCH_FAVORABLE_AMOUNT);
        // 收款通道商户免充值优惠
        long channelMchTopUpFavorableAmount = MapUtils.getLongValue(source, Order.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT);

        // 喔噻补贴的优惠
        long sqbDiscount = mchFavorableAmount - channelMchTopUpFavorableAmount;
        if (sqbDiscount > 0) {
            target.put("sqb_discount", "" + sqbDiscount);
        }

        // 商户补贴的优惠
        long outerDiscount = mchFavorableAmount + channelMchTopUpFavorableAmount;
        if (outerDiscount > 0) {
            target.put("outer_discount", String.valueOf(outerDiscount));
        }

        target.put("refund_fee", OldOrderUtils.getRefundFee(oldStatus, originFee, totalFee));
        target.put("payway_text", OldOrderUtils.getPayWayText(oldPayWay));

        return target;
    }
}
