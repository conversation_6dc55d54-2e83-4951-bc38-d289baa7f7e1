package com.wosai.upay.transaction.util;

import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.transaction.constant.DatabaseQueryConstant;
import com.wosai.upay.transaction.model.Transaction;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**集团对账单 按商户维度拆分处理
 * <AUTHOR>
 */
public class MultipleMerchantTransactionHeap {
    public static final Logger logger = LoggerFactory.getLogger(MultipleMerchantTransactionHeap.class);
    private Map<List<String>, TransactionCondition> merchantCounter = Maps.newConcurrentMap();
    private Integer DEFAULT_BATCH_SIZE = 5000;
    private Map commonQueryFilter;
    private StatementTransactionUtil statementTransactionUtil;
    private List<OrderBy> orderBy;
    private PriorityQueue transactionQueue;

    public MultipleMerchantTransactionHeap(List<String> merchantIds, Map queryFilter, StatementTransactionUtil statementTransactionUtil) {
        this.orderBy = CommonUtil.getTimeOrderBy();
        this.transactionQueue = new PriorityQueue((o1, o2) -> NumberUtils.compare(BeanUtil.getPropLong((o2), orderBy.get(0).getField()), BeanUtil.getPropLong(o1, orderBy.get(0).getField())));
        addMerchant(merchantIds, BeanUtil.getPropLong(queryFilter,"date_end"));
        this.commonQueryFilter = queryFilter;
        this.statementTransactionUtil = statementTransactionUtil;
        merchantCounter.forEach((k, v) -> {
            supplementData(k);
        });
    }

    private void addMerchant(List<String> merchantIds, Long dateEnd){
        Lists.partition(merchantIds, ConfigService.getAppConfig().getIntProperty("group_divide_threshold", 400)).forEach(subMerchantIds -> merchantCounter.put(subMerchantIds, new TransactionCondition(subMerchantIds, dateEnd)));
    }

    public Object getTransaction() {
        Map top = ((Map) transactionQueue.poll());
        String merchant_id = BeanUtil.getPropString(top, Transaction.MERCHANT_ID);
        List<String> subMerchantIds = merchantCounter.keySet().stream().filter(l -> l.contains(merchant_id)).findFirst().orElse(null);
        if (Objects.nonNull(subMerchantIds)){
            TransactionCondition transactionCondition = merchantCounter.get(subMerchantIds);
            Long endTime = CommonUtil.getTime(top);
            String id = MapUtil.getString(top, DaoConstants.ID);
            //记录边界数据
            if (endTime == transactionCondition.nextEnd){
                transactionCondition.addEdgeRecord(id);
            }
            if (transactionCondition.getCount().decrementAndGet() == 0){
                supplementData(subMerchantIds);
            }
        }
        return top;
    }

    private void supplementData(List<String> merchantIds){
        TransactionCondition transactionCondition = merchantCounter.get(merchantIds);
        if (Objects.isNull(transactionCondition)){
            return;
        }
        List<Map<String, Object>> transactionList = transactionCondition.supplementData();
        if (CollectionUtils.isNotEmpty(transactionList)) {
            transactionQueue.addAll(transactionList);
        } else{
            merchantCounter.remove(merchantIds);
        }
    }

    public List getTransactionList(){
        if (transactionQueue.isEmpty()){
            return Lists.newArrayList();
        }
        Integer batchSize = DEFAULT_BATCH_SIZE;
        List res = Lists.newLinkedList();
        int size = transactionQueue.size();
        int min = Math.min(size, batchSize);
        for (int i = 0; i < min; i++) {
            res.add(getTransaction());
        }
        return res;
    }

    @Data
    private class TransactionCondition{
        private AtomicInteger count = new AtomicInteger(0);
        private long nextEnd = Long.MAX_VALUE;
        private List<String> merchantIds;
        private List<String> edgeRecordList = Lists.newLinkedList();

        public TransactionCondition(List<String> merchantIds, long nextEnd) {
            this.merchantIds = merchantIds;
            this.nextEnd = nextEnd;
        }

        public List<Map<String, Object>> supplementData(){
            if (CollectionUtils.isEmpty(merchantIds)){
                return null;
            }
            Map mapParam = buildQueryParams();
            List<Map<String, Object>> transactionList = retryGetMerchantTransactionList(mapParam);
            if (CollectionUtils.isNotEmpty(transactionList)) {
                if (transactionList.size() == DEFAULT_BATCH_SIZE) {
                    long firstRecordTime = CommonUtil.getTime(transactionList.get(0));
                    long lastRecordTime = CommonUtil.getTime(transactionList.get(transactionList.size() - 1));
                    if (firstRecordTime == lastRecordTime){
                        logger.error("(单毫秒内) 指定商户集合的流水超过批次上限， merchantIds:{}, batch:{}, queryFilter:{}", merchantIds, transactionList.size(), mapParam);
                        throw new RuntimeException("单毫秒内的流水超过上限，程序执行异常。");
                    }
                }
                //去重边界数据,维护边界map
                CommonUtil.removeDuplicatesRecord(transactionList, edgeRecordList, nextEnd);
                edgeRecordList.clear();
                if (CollectionUtils.isNotEmpty(transactionList)){
                    count.addAndGet(transactionList.size());
                    nextEnd = CommonUtil.getTime(transactionList.get(transactionList.size() - 1));
                }
            }
            return transactionList;
        }

        private Map buildQueryParams(){
            Map<String, Object> map = Maps.newHashMap();
            Map<Object, Object> innerQueryFilter = Maps.newHashMap();
            innerQueryFilter.putAll(commonQueryFilter);
            map.put("queryFilter", innerQueryFilter);
            innerQueryFilter.put("merchant_ids", merchantIds);
            map.put("nextEndTime", nextEnd + 1);
            return map;
        }

        public void addEdgeRecord(String id){
            edgeRecordList.add(id);
        }

        private List<Map<String, Object>> retryGetMerchantTransactionList(Map mapParam){
            Long paramNextEndTime = BeanUtil.getPropLong(mapParam, "nextEndTime");
            Long paramStart = ((Long) BeanUtil.getNestedProperty(mapParam, "queryFilter.date_start"));
            PageInfo pageInfo = new PageInfo(1, DatabaseQueryConstant.MAX_PAGE_SIZE_LIMIT, paramStart, paramNextEndTime, orderBy);
            try {
                return statementTransactionUtil.retryGetMerchantTransactionList(null, null, null, null, null, pageInfo, (Map) BeanUtil.getProperty(mapParam, "queryFilter"));
            } catch (Exception e) {
                logger.error("get transaction error. params ={}", mapParam);
                return null;
            }
        }
    }

}
