package com.wosai.upay.transaction.service.dao.mysql;

import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;

import java.util.List;
import java.util.Map;

/**
 * MySQL-based transaction data access interface
 * Replaces HBase/Solr queries for transaction data
 */
public interface TransactionMySQLDao {

    /**
     * Query transaction list based on criteria
     */
    List<Map<String, Object>> queryList(TransactionHBaseQuery query);

    /**
     * Count transactions based on criteria
     */
    Long count(TransactionHBaseQuery query);

    /**
     * Get transaction summary statistics
     */
    TransactionSummaryPo summaryTx(TransactionHBaseQuery query);

    /**
     * Get transaction summary grouped by key
     */
    List<TransactionSummaryPo> summaryTxGroupByKey(TransactionHBaseQuery query);

    /**
     * Query transaction list by memo/trade memo
     */
    List<String> queryListByMemo(TransactionHBaseQuery query);

    /**
     * Query single transaction by SN
     */
    Map<String, Object> queryBySn(String merchantId, String sn, long start, long end);
}