package com.wosai.upay.transaction.service.dao.mysql;

import com.wosai.upay.transaction.service.model.po.WalletChangeLogPo;
import com.wosai.upay.transaction.service.model.query.WalletChangeLogQuery;

import java.util.List;

/**
 * MySQL-based wallet change log data access interface
 * Replaces HBase/Solr queries for wallet change log data
 */
public interface WalletChangeLogMySQLDao {

    /**
     * Query wallet change log list based on criteria
     */
    List<WalletChangeLogPo> queryForList(WalletChangeLogQuery query);

    /**
     * Count wallet change logs based on criteria
     */
    long count(WalletChangeLogQuery query);
}