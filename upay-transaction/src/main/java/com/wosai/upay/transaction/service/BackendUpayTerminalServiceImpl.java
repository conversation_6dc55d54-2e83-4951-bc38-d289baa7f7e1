package com.wosai.upay.transaction.service;

import java.util.Map;
import java.util.stream.Collectors;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;

@AutoJsonRpcServiceImpl
@Component
@Deprecated
public class BackendUpayTerminalServiceImpl implements BackendUpayTerminalService{
    @Autowired
    UpayOrderService upayOrderService;

    public Map<String, Object> getTerminalChangeShiftsStatisticsInfo(String terminalSn, String batchSn) {
        Map<String, Object> result = upayOrderService.getTerminalChangeShiftsStatisticsInfo(terminalSn, batchSn);
        return filterResult(result);
    }

    public ListResult getTerminalChangeShiftsStatisticsList(String terminalSn, Long startDate, Long endDate, PageInfo pageInfo){
        ListResult result = upayOrderService.getTerminalChangeShiftsStatisticsList(terminalSn, startDate, endDate, pageInfo);
        if (result != null && CollectionUtil.isNotEmpty(result.getRecords())) {
            result.setRecords(result.getRecords().stream().map(this::filterResult).collect(Collectors.toList()));
        }
        return result;
    }

    public Map filterResult(Map result){
        if (MapUtil.isNotEmpty(result)) {
            Map newValue = MapUtil.copyInclusive(result, "start_date", "end_date", "batch_sn", "pay_amount", "pay_count", "refund_amount", "refund_count", "pay_detail", "refund_detail");
            Map<String, Map<String, Object>> refundDetailMap = MapUtil.getMap(newValue, "refund_detail");
            if (MapUtil.isNotEmpty(refundDetailMap)) {
                for (Map<String, Object> refundDtail : refundDetailMap.values()) {
                    if (MapUtil.isNotEmpty(refundDtail)) {
                        refundDtail.remove("act_merchant");
                        refundDtail.remove("act_cus");
                    }
                }
            }
            return newValue;
        }
        return result;
    }
}