package com.wosai.upay.transaction.util;

import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultException;
import com.wosai.middleware.vault.elasticsearch.DynamicCredentialsProvider;
import com.wosai.upay.transaction.service.config.ElasticsearchProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/***
 * @ClassName: ESUtil
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/6/4 1:57 PM
 */
@Slf4j
public class ESUtil {
    private static Vault vault;

    private static final Map<String, RestHighLevelClient> restHighLevelClients = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        loadVault();
    }

    public static RestHighLevelClient getPayGroupEsClient(ElasticsearchProperties elasticsearchProperties) {
        String instance = elasticsearchProperties.getInstance();
        if (restHighLevelClients.get(instance) == null) {
            synchronized (instance.intern()) {
                if(restHighLevelClients.get(instance) == null) {
                    if (vault == null) {
                        loadVault();
                    }
                    // 阿里云ES集群需要basic auth验证。
                    // 用户名和密码由Vault提供
                    CredentialsProvider credentialsProvider = new DynamicCredentialsProvider(vault, instance);
                    // 通过builder创建rest client，配置http client的HttpClientConfigCallback。
                    // 单击所创建的Elasticsearch实例ID，在基本信息页面获取公网地址，即为ES集群地址。
                    RestClientBuilder builder = RestClient.builder(new HttpHost(elasticsearchProperties.getHostname(), elasticsearchProperties.getPort()))
                            .setRequestConfigCallback(requestConfigBuilder -> {
                                requestConfigBuilder.setConnectTimeout(elasticsearchProperties.getConnectTimeout());
                                requestConfigBuilder.setSocketTimeout(elasticsearchProperties.getSocketTimeout());
                                requestConfigBuilder.setConnectionRequestTimeout(elasticsearchProperties
                                        .getConnectionRequestTimeout());
                                return requestConfigBuilder;})
                            .setHttpClientConfigCallback(httpClientBuilder -> {
                                httpClientBuilder.setMaxConnTotal(elasticsearchProperties.getMaxConnectTotal());
                                httpClientBuilder.setMaxConnPerRoute(elasticsearchProperties.getMaxConnectPerRoute());
                                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                                httpClientBuilder.setDefaultIOReactorConfig(IOReactorConfig.custom()
                                        .setIoThreadCount(Runtime.getRuntime().availableProcessors()).build());
                                return httpClientBuilder;});
                    // RestHighLevelClient实例通过REST high-level client builder进行构造。
                    RestHighLevelClient restHighLevelClient = new RestHighLevelClient(builder);
                    restHighLevelClients.put(instance, restHighLevelClient);
                }
            }
        }
        return restHighLevelClients.get(instance);
    }

    private static void loadVault(){
        try {
            vault = Vault.autoload();
        } catch (VaultException e) {
            log.error("load vault error!");
        }
    }
}