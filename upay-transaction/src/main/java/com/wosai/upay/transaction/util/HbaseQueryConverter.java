package com.wosai.upay.transaction.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import org.apache.commons.collections.CollectionUtils;

import java.util.Objects;
import java.util.Set;

import static com.wosai.upay.transaction.model.Transaction.*;

/**
 * <AUTHOR> Date: 2019-09-05 Time: 10:19
 */
public class HbaseQueryConverter {
    private HbaseQueryConverter(){}

    public static final Set<String> SIMPLE_FIELDS = Sets.newHashSet(ID
            , TSN
            , ORDER_SN
            , TYPE
            , TRADE_NO
            , STATUS
            , EFFECTIVE_AMOUNT
            , ORIGINAL_AMOUNT
            , DaoConstants.CTIME
            , CONFIG_SNAPSHOT
            , EXTENDED_PARAMS
            , EXTRA_OUT_FIELDS
            , EXTRA_PARAMS);
    public static final Set<String> BASIC_FIELDS = Sets.newHashSet(ID
            , TSN
            , STORE_ID
            , ORDER_SN
            , TRADE_NO
            , ITEMS
            , EXTRA_OUT_FIELDS
            , CONFIG_SNAPSHOT
            , REFLECT
            , PROVIDER
            , BIZ_ERROR_CODE
            , MERCHANT_ID
            , TERMINAL_ID
            , EFFECTIVE_AMOUNT
            , ORIGINAL_AMOUNT
            , PAID_AMOUNT
            , RECEIVED_AMOUNT
            , STATUS
            , BUYER_UID
            , PRODUCT_FLAG
            , BUYER_LOGIN
            , TYPE
            , OPERATOR
            , PAYWAY
            , SUB_PAYWAY
            , FINISH_TIME
            , CHANNEL_FINISH_TIME
            , DaoConstants.CTIME
            , EXTENDED_PARAMS
            , EXTRA_PARAMS);


    public static final Set<String> ORDER_SIMPLE_FIELDS = Sets.newHashSet(ID,
            Order.SN,
            Order.CLIENT_SN,
            Order.TRADE_NO,
            Order.NET_ORIGINAL,
            Order.EFFECTIVE_TOTAL,
            Order.NET_EFFECTIVE,
            Order.TOTAL_DISCOUNT,
            Order.NET_DISCOUNT,
            Order.MERCHANT_ID);

    public static final Set<String> ORDER_BASIC_FIELDS = Sets.newHashSet(ID,
            Order.SN,
            Order.CLIENT_SN,
            Order.SUBJECT,
            Order.BODY,
            Order.ITEMS,
            Order.NET_ITEMS,
            Order.STATUS,
            Order.TCP_MODIFIED,
            Order.ORIGINAL_TOTAL,
            Order.NET_ORIGINAL,
            Order.EFFECTIVE_TOTAL,
            Order.NET_EFFECTIVE,
            Order.TOTAL_DISCOUNT,
            Order.NET_DISCOUNT,
            Order.BUYER_UID,
            Order.BUYER_LOGIN,
            Order.MERCHANT_ID,
            Order.STORE_ID,
            Order.TERMINAL_ID,
            Order.OPERATOR,
            Order.PROVIDER,
            Order.PAYWAY,
            Order.SUB_PAYWAY,
            Order.TRADE_NO,
            Order.REFLECT,
            "ctime",
            "mtime",
            "deleted",
            "version"
    );

    public static TransactionHBaseQuery convert(TransactionQuery transactionQuery) {
        if (Objects.isNull(transactionQuery)) {
            return null;
        }

        TransactionHBaseQuery hbaseQuery = new TransactionHBaseQuery();
        hbaseQuery.setQueryEs(transactionQuery.isQueryEs());
        hbaseQuery.setQueryCashDesk(transactionQuery.isQueryCashDesk());
        hbaseQuery.setCashDeskIds(transactionQuery.getCashDeskList());
        hbaseQuery.setStartTime(transactionQuery.getStartTime());
        hbaseQuery.setEndTime(transactionQuery.getEndTime());
        Integer limit = transactionQuery.getLimit();
        if (Objects.isNull(limit)) {
            limit = 15;
        }
        hbaseQuery.setLimit(limit);
        hbaseQuery.setOffset(transactionQuery.getOffset());

        Boolean isSimple = transactionQuery.getSimple();
        if (isSimple != null && isSimple) {
            hbaseQuery.setFilterColumns(SIMPLE_FIELDS);
        } else {
            hbaseQuery.setFilterColumns(BASIC_FIELDS);
        }

        hbaseQuery.setTerminals(transactionQuery.getTerminalList());
        hbaseQuery.getOrderBys().add(transactionQuery.getOrderBy());

        hbaseQuery.setStoreIds(transactionQuery.getStoreIdList());
        hbaseQuery.setMerchantIds(CommonUtil.newArrayList(transactionQuery.getMerchantId()));
        if (CollectionUtils.isNotEmpty(transactionQuery.getMerchantIdList())){
            hbaseQuery.setMerchantIds(transactionQuery.getMerchantIdList());
        }
        hbaseQuery.setNotTerminals(transactionQuery.getNotContainTerminalList());;
        hbaseQuery.setPayWays(transactionQuery.getPayways());
        hbaseQuery.setOrderSns(transactionQuery.getOrderSns());
        hbaseQuery.setTransactionSn(transactionQuery.getTransactionSn());
        hbaseQuery.setTransactionSns(transactionQuery.getTransactionSns());
        hbaseQuery.setProductFlags(transactionQuery.getProductFlags());
        hbaseQuery.getStatusTypeSubPayWayQueries().putAll(transactionQuery.getStatusTypeSubPayWayQueries());

        Set<String> groupByKeySet = transactionQuery.getGroupByKeys();
        if (CollectionUtils.isNotEmpty(groupByKeySet)) {
            hbaseQuery.setGroupByKey(Lists.newArrayList(groupByKeySet).get(0));
        }

        hbaseQuery.setTradeNo(transactionQuery.getTradeNo());

        if (CollectionUtils.isNotEmpty(transactionQuery.getBuyerUids())){
            hbaseQuery.setBuyerUids(transactionQuery.getBuyerUids());
        }
        if (Objects.nonNull(transactionQuery.getMinOriginalAmount())){
            hbaseQuery.setMinOriginalAmount(transactionQuery.getMinOriginalAmount());
        }
        if (Objects.nonNull(transactionQuery.getMaxOriginalAmount())){
            hbaseQuery.setMaxOriginalAmount(transactionQuery.getMaxOriginalAmount());
        }
        return hbaseQuery;
    }

    public static TransactionQuery convert(TransactionHBaseQuery transactionHBaseQuery) {
        if (Objects.isNull(transactionHBaseQuery)) {
            return null;
        }

        TransactionQuery transactionQuery = new TransactionQuery();
        transactionQuery.setQueryEs(transactionHBaseQuery.isQueryEs());
        transactionQuery.setStartTime(transactionHBaseQuery.getStartTime());
        transactionQuery.setEndTime(transactionHBaseQuery.getEndTime());
        Integer limit = transactionHBaseQuery.getLimit();
        if (Objects.isNull(limit)) {
            limit = 15;
        }
        transactionQuery.setLimit(limit);
        transactionQuery.setOffset(transactionHBaseQuery.getOffset());

        transactionQuery.setTerminalList(transactionHBaseQuery.getTerminals());
        transactionQuery.getOrderBys().add(transactionHBaseQuery.getOrderBy());

        transactionQuery.setStoreIdList(transactionHBaseQuery.getStoreIds());
        transactionQuery.setMerchantId(transactionHBaseQuery.getMerchantIds().get(0));
        transactionQuery.setNotContainTerminalList(transactionHBaseQuery.getNotTerminals());;
        transactionQuery.setPayways(transactionHBaseQuery.getPayWays());
        transactionQuery.setOrderSns(transactionHBaseQuery.getOrderSns());
        transactionQuery.setTransactionSn(transactionHBaseQuery.getTransactionSn());
        transactionQuery.setProductFlags(transactionHBaseQuery.getProductFlags());
        transactionQuery.getStatusTypeSubPayWayQueries().putAll(transactionHBaseQuery.getStatusTypeSubPayWayQueries());
        transactionQuery.setLoadTypes(transactionHBaseQuery.getLoadTypes());

        return transactionQuery;
    }
}
