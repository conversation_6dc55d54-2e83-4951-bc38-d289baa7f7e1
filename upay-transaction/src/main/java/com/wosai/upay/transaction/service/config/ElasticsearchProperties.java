package com.wosai.upay.transaction.service.config;

import lombok.Data;

/***
 * @ClassName: ElasticsearchProperties
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/6/4 11:59 AM
 */
@Data
public class ElasticsearchProperties {
    private String instance;
    private String hostname;
    private int port;
    private int connectTimeout;
    private int socketTimeout;
    private int connectionRequestTimeout;
    private int maxConnectTotal;
    private int maxConnectPerRoute;
}
