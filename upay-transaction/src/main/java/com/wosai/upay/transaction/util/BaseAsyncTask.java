package com.wosai.upay.transaction.util;

import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.helper.DBSelectContext;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @param <T>
 */
public abstract class BaseAsyncTask<T> implements Callable<T> {

    private final DBSelectContext dBselectContext = DBSelectContext.getContext();

    private final long enqueueTimeMillis = System.currentTimeMillis();

    public abstract T run() throws Exception;

    @Override
    public final T call() throws Exception {
        long blockTime = System.currentTimeMillis() - enqueueTimeMillis;
        //任务被阻塞1s,取消任务,抛出任务执行超时异常
        if(blockTime > CommonConstant.MILLISECOND_OF_SECOND){
            throw new RuntimeException("任务执行超时");
        }
        cloneDbContext();

        T result = run();
        return result;
    }

    private void cloneDbContext() {
        DBSelectContext context = DBSelectContext.getContext();
        context.setSelectDb(dBselectContext.getSelectDb());
    }

}
