package com.wosai.upay.transaction.util;

import lombok.SneakyThrows;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Table;

public class TableFactory extends BasePooledObjectFactory<Table> {

    private Connection connection;

    private TableName tableName;

    public TableFactory(Connection connection, TableName tableName) {
        this.connection = connection;
        this.tableName = tableName;
    }

    @Override
    @SneakyThrows
    public Table create() {
        return connection.getTable(tableName);
    }

    @Override
    public PooledObject<Table> wrap(Table obj) {
        return new DefaultPooledObject<>(obj);
    }

    @Override
    @SneakyThrows
    public void destroyObject(PooledObject<Table> p) {
        Table table = p.getObject();
        table.close();
        super.destroyObject(p);
    }
}
