package com.wosai.upay.transaction.service.service.client.impl;

import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.transaction.service.service.client.IAsyncTerminalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@Service
public class AsyncTerminalServiceImpl implements IAsyncTerminalService {

    @Autowired
    public TerminalService terminalService;

    @Override
    @Async
    public Future<Map> getTerminalBySn(String terminalSn, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<Map>(terminalService.getTerminalBySn(terminalSn));
    }

    @Override
    @Async
    public Future<Map> getTerminal(String terminalId, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<Map>(terminalService.getTerminal(terminalId));
    }
}
