package com.wosai.upay.transaction.service.routing;

import com.wosai.upay.transaction.service.dao.mysql.TransactionMySQLDao;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Service that routes transaction queries between MySQL and HBase/Solr
 * based on feature toggle configuration
 */
@Service
public class RoutingTransactionService {

    private static final Logger logger = LoggerFactory.getLogger(RoutingTransactionService.class);

    @Autowired
    private TransactionMySQLDao transactionMySQLDao;

    @Autowired
    private TransactionHBaseDao transactionHBaseDao;

    @Autowired
    private QueryRoutingService queryRoutingService;

    public List<Map<String, Object>> queryList(TransactionHBaseQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for transaction queryList, merchant: {}", merchantId);
            return transactionMySQLDao.queryList(query);
        } else {
            logger.info("Using HBase/Solr for transaction queryList, merchant: {}", merchantId);
            return transactionHBaseDao.queryList(query);
        }
    }

    public Long count(TransactionHBaseQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for transaction count, merchant: {}", merchantId);
            return transactionMySQLDao.count(query);
        } else {
            logger.info("Using HBase/Solr for transaction count, merchant: {}", merchantId);
            return transactionHBaseDao.count(query);
        }
    }

    public TransactionSummaryPo summaryTx(TransactionHBaseQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for transaction summaryTx, merchant: {}", merchantId);
            return transactionMySQLDao.summaryTx(query);
        } else {
            logger.info("Using HBase/Solr for transaction summaryTx, merchant: {}", merchantId);
            return transactionHBaseDao.summaryTx(query);
        }
    }

    public List<TransactionSummaryPo> summaryTxGroupByKey(TransactionHBaseQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for transaction summaryTxGroupByKey, merchant: {}", merchantId);
            return transactionMySQLDao.summaryTxGroupByKey(query);
        } else {
            logger.info("Using HBase/Solr for transaction summaryTxGroupByKey, merchant: {}", merchantId);
            return transactionHBaseDao.summaryTxGroupByKey(query);
        }
    }

    public List<String> queryListByMemo(TransactionHBaseQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for transaction queryListByMemo, merchant: {}", merchantId);
            return transactionMySQLDao.queryListByMemo(query);
        } else {
            logger.info("Using HBase/Solr for transaction queryListByMemo, merchant: {}", merchantId);
            return transactionHBaseDao.queryListByMemo(query);
        }
    }

    public Map<String, Object> queryBySn(String merchantId, String sn, long start, long end) {
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for transaction queryBySn, merchant: {}", merchantId);
            return transactionMySQLDao.queryBySn(merchantId, sn, start, end);
        } else {
            logger.info("Using HBase/Solr for transaction queryBySn, merchant: {}", merchantId);
            return transactionHBaseDao.queryBySn(merchantId, sn, start, end);
        }
    }

    private String getFirstMerchantId(TransactionHBaseQuery query) {
        if (query.getMerchantIds() != null && !query.getMerchantIds().isEmpty()) {
            return query.getMerchantIds().get(0);
        }
        return null;
    }
}