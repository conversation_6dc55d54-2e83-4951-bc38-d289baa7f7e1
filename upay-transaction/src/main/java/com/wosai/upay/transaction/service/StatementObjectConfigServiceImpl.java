package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 */

@AutoJsonRpcServiceImpl
@Service
public class StatementObjectConfigServiceImpl implements StatementObjectConfigService {

    @Autowired
    private com.wosai.upay.task.center.service.StatementObjectConfigService taskStatementObjectConfigService;


    @Override
    public void insertOrUpdate(Map config) {
        taskStatementObjectConfigService.insertOrUpdate(config);
    }


    @Override
    public Map getLanguageByGroupSn(String groupSn) {
        return taskStatementObjectConfigService.getLanguageByGroupSn(groupSn);
    }

    @Override
    public Map getConfigByObjectId(String merchantId) {
        return taskStatementObjectConfigService.getConfigByObjectId(merchantId);
    }

}
