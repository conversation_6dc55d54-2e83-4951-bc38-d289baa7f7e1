package com.wosai.upay.transaction.config;

import com.wosai.upay.transaction.helper.UpayServiceHandlerInterceptor;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.HiddenHttpMethodFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC configuration
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Bean
    public FilterRegistrationBean hiddenHttpMethodFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new HiddenHttpMethodFilter());
        registration.setName("httpMethodFilter");
        registration.addServletNames("dispatcher");
        return registration;
    }

    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new UpayServiceHandlerInterceptor()).addPathPatterns("/**");
    }
}
