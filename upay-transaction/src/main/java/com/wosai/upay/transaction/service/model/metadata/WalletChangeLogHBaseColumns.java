package com.wosai.upay.transaction.service.model.metadata;

/**
 * WalletChangeLogHBaseColumns
 *
 * <AUTHOR>
 */
public interface WalletChangeLogHBaseColumns {

    byte[] ID = "id".getBytes();

    byte[] TYPE = "type".getBytes();

    byte[] AMOUNT = "amount".getBytes();

    byte[] SIGN = "sign".getBytes();

    byte[] BALANCE = "balance".getBytes();

    byte[] REMARK = "remark".getBytes();

    byte[] DETAIL = "detail".getBytes();

    byte[] MERCHANT_ID = "merchant_id".getBytes();

    byte[] ACTION_ID = "action_id".getBytes();

    byte[] CTIME = "ctime".getBytes();

    byte[] MTIME = "mtime".getBytes();

//    byte[] VERSION = "version".getBytes();

}
