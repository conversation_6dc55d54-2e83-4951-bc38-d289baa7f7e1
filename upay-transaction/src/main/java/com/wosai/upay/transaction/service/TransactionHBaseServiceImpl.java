package com.wosai.upay.transaction.service;

import com.google.common.base.Stopwatch;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.transaction.constant.DatabaseQueryConstant;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.model.ExportTransactionVO;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.model.param.HbaseQueryParam;
import com.wosai.upay.transaction.model.param.TransactionParam;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.util.CommonUtil;
import com.wosai.upay.transaction.util.DateTimeUtil;
import com.wosai.upay.transaction.util.SolrHBaseUtils;
import com.wosai.upay.transaction.util.TablePool;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.transaction.service.dao.base.HBaseDao.FAMILY;


@AutoJsonRpcServiceImpl
@Service
public class TransactionHBaseServiceImpl implements TransactionHBaseService {
    public static final Logger logger = LoggerFactory.getLogger(TransactionHBaseServiceImpl.class);

    private static final SimpleDateFormat DAY_FORMAT = new SimpleDateFormat("yyyyMM");
    @Autowired
    TransactionHBaseDao transactionHBaseDao;

    private static final String NAMESPACE = "upay";
    private static final String TABLE_PREFIX = "tx_";

    private static final TableName SWIPE_TABLE_NAME = TableName.valueOf(NAMESPACE, "swipe_transaction");

    private static final long SWIPE_OFFSET = 10 * 60 * 1000L;


    @Override
    public ExportTransactionVO query(TransactionParam transactionParam) {
        logger.info("查询upay-transaction开始,taskLogId:{},参数是:{}", transactionParam.getTaskLogId(), transactionParam);
        long start = transactionParam.getStart();
        long end = transactionParam.getEnd();
        long nextStart = transactionParam.getNextStart();
        Date queryStartDate = new Date(nextStart);
        long monthEnd = DateTimeUtil.getMonthEnd(queryStartDate).getTime();
        long partEnd = Math.min(monthEnd, end);
        String merchantId = transactionParam.getMerchantId();
        String queryMonth = DAY_FORMAT.format(queryStartDate);
        boolean querySwipe = transactionParam.getUpayQueryType() == UpayQueryType.UPAY_SWIPE.getCode();
        TableName tableName = querySwipe ? SWIPE_TABLE_NAME : TableName.valueOf(NAMESPACE, TABLE_PREFIX + queryMonth);
        Connection connection = SolrHBaseUtils.getConnection(DatabaseQueryConstant.QUERY_THIRTEEN_SECONDS_TIMEOUT_SECONDS);
        TablePool pool = SolrHBaseUtils.getTablePool(connection, tableName, DatabaseQueryConstant.QUERY_THIRTEEN_SECONDS_TIMEOUT_SECONDS);
        Table table = pool.borrowObject();
        int limit = transactionParam.getLimit();
        int scanSize = limit;

        List<Integer> statementTypes = transactionParam.getStatementTypes();
        List<String> storeIds = transactionParam.getStoreIds();
        String terminalId = transactionParam.getTerminalId();
        boolean filterStore = CollectionUtil.isNotEmpty(storeIds);
        boolean filterTerminal = !StringUtil.empty(terminalId);

        List<String> edgeRecordIds = transactionParam.getEdgeRecordIds();
        List<Map<String, Object>> transactionList = new ArrayList<Map<String, Object>>();
        boolean flagContinue = false;
        boolean flagBreak = false;

        ResultScanner resultScanner = null;
        try {
            Scan scan = new Scan();
            scan.setLimit(limit);
            // 开始时间 - 1（lindorm的withStopRow的inclusive参数无效）
            scan.withStartRow(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(nextStart - 1)), false);
            // 结束时间 + 1（lindorm的withStartRow的inclusive参数无效）
            scan.withStopRow(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(partEnd + 1)), false);
            scan.addFamily(FAMILY);
            scan.setCacheBlocks(false);
            resultScanner = table.getScanner(scan);
            Stopwatch scannerWatch = Stopwatch.createStarted();
            Result[] batchGet = resultScanner.next(limit);
            scanSize = batchGet.length;
            logger.info("end of scanner, taskLogId: {}, cost: {} ms", transactionParam.getTaskLogId(), scannerWatch.stop().elapsed(TimeUnit.MILLISECONDS));
            for (Result rs : batchGet) {
                Map<String, Object> transaction = transactionHBaseDao.buildEntryMap(rs, Collections.emptyMap());
                // 只导出成功交易
                if (MapUtil.getIntValue(transaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS) {
                    continue;
                }
                // 只导出部分流水类型
                int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
                if (!statementTypes.contains(type)) {
                    continue;
                }
                if (filterStore && !storeIds.contains(MapUtil.getString(transaction, Transaction.STORE_ID))) {
                    continue;
                }
                if (filterTerminal && !terminalId.equals(MapUtil.getString(transaction, Transaction.TERMINAL_ID))) {
                    continue;
                }
                if (!querySwipe) {
                    // 订单创建时间大于结束时间，说明超过结束时间
                    long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
                    if (ctime > partEnd || ctime < nextStart) {
                        continue;
                    }
                } else {
                    // 刷卡交易是按照渠道时间来导出的，当不存在值或支付时间小于导出开始时间时，不导出；但支付时间大于结束时间时，不导出
                    Long channelFinishTime = MapUtil.getLongValue(transaction, Transaction.CHANNEL_FINISH_TIME);
                    if (channelFinishTime == null
                            || channelFinishTime < (start + SWIPE_OFFSET)
                            || channelFinishTime > end) {
                        continue;
                    }
                }
                transactionList.add(transaction);
            }
            CommonUtil.removeDuplicatesRecord(transactionList, edgeRecordIds, nextStart);
            if (CollectionUtils.isEmpty(transactionList)) {
                // 当一个商户交易量特别大，单次查询出所有数据都被过滤时，不应该跳出
                if (scanSize == limit) {
                    Map<String, Object> lastRecord = transactionHBaseDao.buildEntryMap(batchGet[limit - 1], Collections.emptyMap());
                    long nextStartTmp = CommonUtil.getTime(lastRecord);
                    if (nextStartTmp != start) {
                        nextStart = nextStartTmp;
                        edgeRecordIds.clear();
                        flagContinue = true;
                    }
                }
                flagBreak = true;
            }

        } catch (Exception e) {
            logger.error("查询对账单信息出错", e);
            throw new RuntimeException("查询对账单信息出错", e);
        } finally {
            resultScanner.close();
            pool.returnObject(table);
        }
        logger.info("查询upay-transaction结束,taskLogId:{},结果信息大小为:{} edgeRecordIds:{}, scanSize:{},flagBreak:{},flagContinue:{},nextStart:{}", transactionParam.getTaskLogId(), transactionList.size(), edgeRecordIds, scanSize, flagBreak, flagContinue, nextStart);
        return ExportTransactionVO.builder().transactionList(transactionList).edgeRecordIds(edgeRecordIds).flagBreak(flagBreak).flagContinue(flagContinue).scanSize(scanSize).nextStart(nextStart).build();
    }


    @Override
    public List<Map<String, Object>> queryList(HbaseQueryParam hbaseQueryParam) {
        TransactionHBaseQuery hBaseQuery = new TransactionHBaseQuery();
        hBaseQuery.setCashDeskIds(hbaseQueryParam.getCashDeskIds());
        hBaseQuery.setQueryCashDesk(hbaseQueryParam.isQueryCashDesk());
        hBaseQuery.setStartTime(hbaseQueryParam.getStartTime());
        hBaseQuery.setEndTime(hbaseQueryParam.getEndTime());
        hBaseQuery.setLimit(hbaseQueryParam.getLimit());
        hBaseQuery.getOrderBys().add(new OrderBy("ctime", OrderBy.OrderType.ASC));
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
        statusTypeSubPayWayQuery.setStatusList(hbaseQueryParam.getNotStatusList());
        hBaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
        for (Integer order : hbaseQueryParam.getOrders()) {
            if (order != null && 1 == order) {
                hBaseQuery.getOrderBys().add(new OrderBy("ctime", OrderBy.OrderType.ASC));
            } else {
                hBaseQuery.getOrderBys().add(new OrderBy("ctime", OrderBy.OrderType.DESC));
            }
        }
        return transactionHBaseDao.queryList(hBaseQuery);
    }
}
