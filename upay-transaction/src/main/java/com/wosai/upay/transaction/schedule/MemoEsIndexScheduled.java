package com.wosai.upay.transaction.schedule;

import com.wosai.upay.transaction.constant.EsConstant;
import com.wosai.upay.transaction.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.forcemerge.ForceMergeRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.rest.RestStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class MemoEsIndexScheduled {

    @Resource
    private RestHighLevelClient tradeMemoRestHighLevelClient;

    /**
     * 定时删除es binlog同步数据的索引
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void deleteBinlogIndexTask() {

        //删除三年前的index
        String quarter = DateTimeUtil.yearQuarter(System.currentTimeMillis() - (365 * 3 + 31 * 3) * 24 * 60 * 60 * 1000L );
        String index = String.format("%s%s", EsConstant.INDEX_QUARTER, quarter);
        try {
            for(int i = 0; i < 3; i++) {

                try {
                    DeleteIndexRequest request = new DeleteIndexRequest(index);
                    tradeMemoRestHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
                    log.info("删除索引 ==>{}成功", index);
                    break;
                } catch (ElasticsearchStatusException exception) {
                    if (exception.status() == RestStatus.NOT_FOUND) {
                        //Do something if the index to be deleted was not found
                        log.info("索引 ==>{}不存在", index);
                    } else {
                        throw exception;
                    }
                }
            }
        } catch (Throwable ex) {
            log.error("索引{} ==>删除失败 ", index, ex);
        }
    }

    /**
     * 定时合并 ES binlog_index segments
     */
    @Scheduled(cron = "0 30 3 * * ?")
    public void forceMergeBinlogIndexSegments() {
        String yesterdayQuarter = DateTimeUtil.yearQuarter(System.currentTimeMillis() - 24 * 60 * 60 * 1000L);
        String index = String.format("%s%s", EsConstant.INDEX_QUARTER, yesterdayQuarter);
        forceMergeExIndexSegments(index);
    }

    private void forceMergeExIndexSegments(String index) {
        try {
            ForceMergeRequest request = new ForceMergeRequest(index);
            request.maxNumSegments(1);
            tradeMemoRestHighLevelClient.indices().forcemerge(request, RequestOptions.DEFAULT);
            log.info("合并索引:{} 的段 ==> 成功", index);
        } catch (ElasticsearchException exception) {
            if (exception.status() == RestStatus.NOT_FOUND) {
                log.info("索引:{} ==>不存在", index);
            } else {
                throw exception;
            }
        } catch (Throwable e) {
            log.info("合并索引：{} ==> 的段失败 ", index, e);
        }
    }

}
