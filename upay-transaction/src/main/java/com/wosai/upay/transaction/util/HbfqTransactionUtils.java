package com.wosai.upay.transaction.util;

import static com.wosai.upay.transaction.util.LanguageUtil.*;
import static com.wosai.upay.transaction.util.SheetHelper.appendLine;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.transaction.constant.CommonConstant;

import avro.shaded.com.google.common.collect.Lists;

public class HbfqTransactionUtils {

    private static final Map<Integer, String> statusMap = CollectionUtil.hashMap(
            0, "待处理", 1, "处理中", 2, "处理失败", 3, "处理成功", 4, "分账未知", 5, "处理失败已关闭"
    );
    public static String getStatusDesc(Integer status){
        return statusMap.getOrDefault(status, status + "");
    }

    private static final Map<Integer,String> organizationsMap = CollectionUtil.hashMap(
      1002, "拉卡拉", 1, "支付宝", 3, "微信"

    );

    public static final String HBFQ_PRODUCT_FLAG = "a3";
    public static final String CREDIT_PRODUCT_FLAG = "ai";
    public static final String FQ_AMOUNT = "fq_amount";
    public static final String COMBINATION_PAY = "combination_pay";

    public static String getOrganizationDesc(Integer organization){
        return organizationsMap.getOrDefault(organization, organization + "");
    }

    public static SXSSFWorkbook newStatementDetailSheet(Map context, String sheetName) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(CommonConstant.ROW_ACCESS_WINDOW_SIZE);
        SXSSFSheet detailSheet = (SXSSFSheet) workbook.createSheet(sheetName);
        appendHeaderForStatementDetailSheet(context, detailSheet);
        return workbook;
    }

    /**
     * add header for statement detail sheet
     */
    private static void appendHeaderForStatementDetailSheet(Map context, SXSSFSheet detailSheet) {
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        String merchantSn = BeanUtil.getPropString(context, "merchant_sn");
        String merchantName = BeanUtil.getPropString(context, "merchant_name");
        appendLine(detailSheet, Arrays.asList(getValue(WOSAI_TRANSACTION_DETAILS)));
        appendLine(detailSheet, Arrays.asList(getValue(MERCHANT_NO) + ":" + merchantSn));
        appendLine(detailSheet, Arrays.asList(getValue(MERCHANT_NAME) + ":" + merchantName));
        appendLine(detailSheet, Arrays.asList(String.format(
                getValue(PERIOD) + ":[%s]—:[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()
        )));
        appendLine(detailSheet, Arrays.asList("#-----------------------------------------" + getValue(FQ_TRANSACTION_DETAILS_LIST) + "----------------------------------------#"));
        List<String> valueList = Lists.newArrayList(TRANSACTION_DATE, TIME, STORE_NAME, MERCHANT_SERIAL_NO, MERCHANT_ORDER_NO, WOSAI_ORDER_NO, PAYMENT_TYPE_ORDER_NO,
                TRANSACTION_TYPE, TRANSACTION_STATUS, TRANSACTION_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE,
                MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, DETAIL_SETTLEMENT_AMOUNT, FQ_TYPE, FQ_NUM, FQ_AMOUNT, HBFQ_MCH_DISCOUNT_AMOUNT, FQ_PAY_TYPE);

        appendLine(detailSheet, getValueList(valueList));
    }

    public static void addStatementSummaryTitle(Map context, SXSSFSheet summarySheet) {
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        String merchantSn = BeanUtil.getPropString(context, "merchant_sn");
        String merchantName = BeanUtil.getPropString(context, "merchant_name");
        String sid = String.format("SQB%s-%s-%s",
                merchantSn,
                CommonConstant.DAY_SDF_YYYYMMDD.get().format(start),
                CommonConstant.DAY_SDF_YYYYMMDD.get().format(end)
        );

        appendLine(summarySheet, Arrays.asList(String.format(
                getValue(PERIOD) + ":[%s]:[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()
                ))
        );
        appendLine(summarySheet, Arrays.asList(getValue(STATEMENT_NO), sid));
        appendLine(summarySheet, Arrays.asList(getValues(MERCHANT_BASIC_INFORMATION)));
        appendLine(summarySheet, getValues(MERCHANT_NO, MERCHANT_NAME, STATEMENT_STYLE));
        appendLine(summarySheet, Arrays.asList(merchantSn, merchantName, "分期收款对账单"));
        appendLine(summarySheet, Arrays.asList());
        appendLine(summarySheet, Arrays.asList("如果您是支付宝签约的正式商户，手续费和结算金额仅供参考，如有不一致请以支付通道官方后台数据为准"));
        appendLine(summarySheet, Arrays.asList("实收金额 = 收款金额 - 退款金额 - 收钱吧商户优惠 - 收款通道商户免充值优惠；收钱吧补贴优惠和收款通道补贴优惠不需要商户承担，收款通道商户预充值优惠已预存金额，不做扣减。"));
        appendLine(summarySheet, Arrays.asList("结算金额 = 实收金额 - 手续费 - 分账金额（含花呗分期商家贴息金额）"));
        appendLine(summarySheet, Arrays.asList());
    }

}
