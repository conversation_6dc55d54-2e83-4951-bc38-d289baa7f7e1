package com.wosai.upay.transaction.service.dao.redis;

import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.service.exception.LoadCacheException;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RFuture;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 */
@Component
public class RedisMapCache {

    private static final int TTL_3 = 3;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * putAll
     *
     * @param key      the key
     * @param ttl      存活时间
     * @param timeUnit 存活时间单位
     * @param map      field-value 键值对集合
     */
    public void putAll(String key, long ttl, TimeUnit timeUnit, Map<String, Object> map) {
        Assert.hasText(key, "key 不能为空");
        Assert.state(ttl > 0, "ttl 必须大于0");
        Assert.notNull(timeUnit, "timeUnit 不能为空");
        Assert.notEmpty(map, "map 不能为空");

        RMap<String, Object> rMap = this.redissonClient.getMap(key);
        rMap.putAll(map);
        rMap.expire(ttl, timeUnit);
    }

    /**
     * 异步获取并刷新缓存
     *
     * @param key      the key
     * @param fields   一个或多个字段集合
     * @param ttl      存活时间
     * @param timeUnit 存活时间单位
     * @return 类型 map 缓存键值对数据
     */
    public Map<String, Object> getAllAsyncAndRefreshTtl(String key, Set<String> fields, int ttl, TimeUnit timeUnit) {
        Assert.hasText(key, "key 不能为空");
        Assert.notEmpty(fields, "fields 不能为空");
        Assert.state(ttl > 0, "ttl 必须大于0");
        Assert.notNull(timeUnit, "timeUnit 不能为空");

        RMap<String, Object> rMap = this.redissonClient.getMap(key);
        RFuture<Map<String, Object>> rFuture = rMap.getAllAsync(fields);

        try {
            Map<String, Object> result = rFuture.get(TTL_3, TimeUnit.SECONDS);
            if (result != null && result.size() > 0) {
                // 订单不存在缓存不需要刷新过期时间
                if (!result.containsKey(CommonConstant.FIELD_EXIST)) {
                    rMap.expire(ttl, timeUnit);
                }
            }
            return result;
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new LoadCacheException(key, fields, e);
        }
    }

}
