package com.wosai.upay.transaction.config;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.trade.service.TradeAppService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.upay.picture.service.ImageBuildService;
import com.wosai.upay.prepaid.api.PrepaidStatementService;
import com.wosai.upay.prepaid.api.PrepaidIssuerService;
import com.wosai.upay.prepaid.api.PrepaidMerchantService;
import com.wosai.app.service.MerchantUserService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.MetaService;
import com.wosai.upay.core.service.ChangeShiftsService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.app.service.GroupService;
import com.wosai.app.service.DepartmentService;
import com.wosai.upay.task.center.service.ExportService;
import com.wosai.upay.task.center.service.TaskLogService;
import com.wosai.upay.task.center.service.StatementObjectConfigService;
import com.wosai.upay.trade.api.FormService;
import com.wosai.upay.trade.api.FormpayService;
import com.wosai.upay.core.service.CashDeskService;
import com.wosai.upay.cashdesk.api.service.CashDeskTradeService;
import com.wosai.app.backend.api.service.IMerchantService;
import com.wosai.app.backend.api.service.IStaffService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.MerchantUserDataPermissionsService;
import com.wosai.ads.coeus.api.service.NexusAccountService;
import com.wosai.upay.transaction.service.remote.RemoteReceiverService;
import com.wosai.shouqianba.withdrawservice.service.WithdrawService;
import com.wosai.upay.clearance.service.ClearanceService;
import com.wosai.service.IMerchantGrayService;
import com.wosai.market.saas.merchant.api.service.CustomerService;
import com.wosai.service.IAccountReportServiceProxyV2;
import com.wosai.upay.job.service.T9Service;
import facade.ICustomerRelationFacade;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class RpcConfig {

    @Bean
    public JsonProxyFactoryBean qrcodeImagedown(
            @Value("${upay-picture.server}") String upayPictureServer,
            @Value("${upay-picture.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-picture.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(upayPictureServer + "rpc/image");
        factory.setServiceInterface(ImageBuildService.class);
        factory.setServerName("upay-picture-service");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean prepaidStatementService(
            @Value("${upay-prepaid-card.server}") String upayPrepaidServer,
            @Value("${upay-picture.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-picture.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(upayPrepaidServer + "rpc/statement");
        factory.setServiceInterface(PrepaidStatementService.class);
        factory.setServerName("upay-prepaid-card");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean prepaidIssuerService(
            @Value("${upay-prepaid-card.server}") String upayPrepaidServer,
            @Value("${upay-picture.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-picture.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(upayPrepaidServer + "rpc/issuer");
        factory.setServiceInterface(PrepaidIssuerService.class);
        factory.setServerName("upay-prepaid-card");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean prepaidMerchantService(
            @Value("${upay-prepaid-card.server}") String upayPrepaidServer,
            @Value("${upay-picture.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-picture.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(upayPrepaidServer + "rpc/merchant");
        factory.setServiceInterface(PrepaidMerchantService.class);
        factory.setServerName("upay-prepaid-card");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    // Merchant User Services
    @Bean
    public JsonProxyFactoryBean merchantUserService(
            @Value("${merchant-user-service.server}") String merchantUserServer,
            @Value("${merchant-user-service.connection_timeout:500}") int connectionTimeout,
            @Value("${merchant-user-service.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantUserServer + "/rpc/merchantuser");
        factory.setServiceInterface(MerchantUserService.class);
        factory.setServerName("merchant-user-service");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    // Core Business Services
    @Bean
    public JsonProxyFactoryBean externalMerchantService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/merchant");
        factory.setServiceInterface(MerchantService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean externalTradeConfigService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/tradeConfig");
        factory.setServiceInterface(TradeConfigService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean externalBusinssCommonService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/common");
        factory.setServiceInterface(BusinssCommonService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean externalStoreService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/store");
        factory.setServiceInterface(StoreService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean merchantCenterStoreService(
            @Value("${merchant-center.server}") String merchantCenterServer,
            @Value("${merchant-center.connection_timeout:500}") int connectionTimeout,
            @Value("${merchant-center.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantCenterServer + "rpc/store");
        factory.setServiceInterface(com.wosai.mc.service.StoreService.class);
        factory.setServerName("merchant-center");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean externalTerminalService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/terminal");
        factory.setServiceInterface(TerminalService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean externalMetaService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/meta");
        factory.setServiceInterface(MetaService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean externalChangeShiftsService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/changeShifts");
        factory.setServiceInterface(ChangeShiftsService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean supportService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/support");
        factory.setServiceInterface(SupportService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean merchantUserGroupService(
            @Value("${merchant-user-service.server}") String merchantUserServer,
            @Value("${merchant-user-service.connection_timeout:500}") int connectionTimeout,
            @Value("${merchant-user-service.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantUserServer + "rpc/group");
        factory.setServiceInterface(GroupService.class);
        factory.setServerName("merchant-user-service");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean merchantUserDepartmentService(
            @Value("${merchant-user-service.server}") String merchantUserServer,
            @Value("${merchant-user-service.connection_timeout:500}") int connectionTimeout,
            @Value("${merchant-user-service.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantUserServer + "rpc/department");
        factory.setServiceInterface(DepartmentService.class);
        factory.setServerName("merchant-user-service");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    // Task Center Services
    @Bean
    public JsonProxyFactoryBean taskExportService(
            @Value("${upay-task-center.server}") String taskCenterServer,
            @Value("${upay-trade.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-trade.read_timeout:10000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(taskCenterServer + "rpc/export");
        factory.setServiceInterface(ExportService.class);
        factory.setServerName("upay-task-center");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean taskTaskLogService(
            @Value("${upay-task-center.server}") String taskCenterServer,
            @Value("${upay-trade.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-trade.read_timeout:10000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(taskCenterServer + "rpc/task");
        factory.setServiceInterface(TaskLogService.class);
        factory.setServerName("upay-task-center");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean taskStatementObjectConfigService(
            @Value("${upay-task-center.server}") String taskCenterServer,
            @Value("${upay-trade.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-trade.read_timeout:10000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(taskCenterServer + "rpc/statementObjectConfig");
        factory.setServiceInterface(StatementObjectConfigService.class);
        factory.setServerName("upay-task-center");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean formService(
            @Value("${upay-trade.server}") String tradeServer,
            @Value("${upay-trade.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-trade.read_timeout:10000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(tradeServer + "rpc/form");
        factory.setServiceInterface(FormService.class);
        factory.setServerName("upay-trade");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean formpayService(
            @Value("${upay-trade.server}") String tradeServer,
            @Value("${upay-trade.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-trade.read_timeout:10000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(tradeServer + "rpc/formpay");
        factory.setServiceInterface(FormpayService.class);
        factory.setServerName("upay-trade");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean cashDeskService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/cash_desk");
        factory.setServiceInterface(CashDeskService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean cashDeskTradeService(
            @Value("${upay-cashdesk.server}") String cashdeskServer) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(cashdeskServer + "rpc/cashdesk");
        factory.setServiceInterface(CashDeskTradeService.class);
        factory.setServerName("upay-cashdesk");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(5000);
        return factory;
    }

    // App Backend Services
    @Bean
    public JsonProxyFactoryBean appMerchantService(
            @Value("${app-backend.server}") String appBackendServer,
            @Value("${app-backend.connection_timeout:500}") int connectionTimeout,
            @Value("${app-backend.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(appBackendServer + "rpc/merchantconfig");
        factory.setServiceInterface(IMerchantService.class);
        factory.setServerName("app-backend");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean appStaffService(
            @Value("${app-backend.server}") String appBackendServer,
            @Value("${app-backend.connection_timeout:500}") int connectionTimeout,
            @Value("${app-backend.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(appBackendServer + "rpc/staff");
        factory.setServiceInterface(IStaffService.class);
        factory.setServerName("app-backend");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean merchantUserServiceV2(
            @Value("${merchant-user-service.server}") String merchantUserServer,
            @Value("${merchant-user-service.connection_timeout:500}") int connectionTimeout,
            @Value("${merchant-user-service.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantUserServer + "/rpc/merchantuserV2");
        factory.setServiceInterface(MerchantUserServiceV2.class);
        factory.setServerName("merchant-user-service");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean merchantUserDataPermissionsService(
            @Value("${merchant-user-service.server}") String merchantUserServer,
            @Value("${merchant-user-service.connection_timeout:500}") int connectionTimeout,
            @Value("${merchant-user-service.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantUserServer + "rpc/merchantUser/dataPermissions");
        factory.setServiceInterface(MerchantUserDataPermissionsService.class);
        factory.setServerName("merchant-user-service");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean nexusAccountService(
            @Value("${rpc.service.url.ads.coeus.service}") String adsService,
            @Value("${rpc.service.url.ads.coeus.service.connection_timeout:500}") int connectionTimeout,
            @Value("${rpc.service.url.ads.coeus.service.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(adsService + "rpc/nexus/account");
        factory.setServiceInterface(NexusAccountService.class);
        factory.setServerName("ads-coeus");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean receiverService(
            @Value("${profit-sharing.server}") String profitSharingServer,
            @Value("${profit-sharing.connection_timeout:500}") int connectionTimeout,
            @Value("${profit-sharing.read_timeout:60000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(profitSharingServer + "rpc/receiver");
        factory.setServiceInterface(RemoteReceiverService.class);
        factory.setServerName("profit-sharing");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean withdrawService(
            @Value("${shouqianba-withdraw-service.server}") String withdrawServer,
            @Value("${shouqianba-withdraw-service.connection_timeout:500}") int connectionTimeout,
            @Value("${shouqianba-withdraw-service.read_timeout:60000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(withdrawServer + "/rpc/withdraw");
        factory.setServiceInterface(WithdrawService.class);
        factory.setServerName("shouqianba-withdraw-service");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean clearanceService(
            @Value("${clearance-service.server}") String clearanceServer,
            @Value("${clearance-service.connection_timeout:500}") int connectionTimeout,
            @Value("${clearance-service.read_timeout:60000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(clearanceServer + "/rpc/clearance");
        factory.setServiceInterface(ClearanceService.class);
        factory.setServerName("clearance-service");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean merchantGrayService(
            @Value("${upay-grayscale.server}") String grayscaleServer,
            @Value("${upay-grayscale.connection_timeout:500}") int connectionTimeout,
            @Value("${upay-grayscale.read_timeout:3000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(grayscaleServer + "rpc/merchantGray");
        factory.setServiceInterface(IMerchantGrayService.class);
        factory.setServerName("upay-grayscale");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean customerService(
            @Value("${marketing-saas-merchant-facade.server}") String marketingServer,
            @Value("${marketing-saas-merchant-facade.connection_timeout:500}") int connectionTimeout,
            @Value("${marketing-saas-merchant-facade.read_timeout:500}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(marketingServer + "rpc/customer");
        factory.setServiceInterface(CustomerService.class);
        factory.setServerName("marketing-saas-merchant-facade");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean accountReportService(
            @Value("${transaction-report.server}") String reportServer,
            @Value("${transaction-report.connection_timeout:500}") int connectionTimeout,
            @Value("${transaction-report.read_timeout:6000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(reportServer + "rpc/account_report_proxy_v2");
        factory.setServiceInterface(IAccountReportServiceProxyV2.class);
        factory.setServerName("transaction-report");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    // Trade Management Services
    @Bean
    public JsonProxyFactoryBean tradeComboService(
            @Value("${trade_manage.server}") String tradeManageServer,
            @Value("${trade_manage.server.connection_timeout:500}") int connectionTimeout,
            @Value("${trade_manage.server.read_timeout:6000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(tradeManageServer + "rpc/trade_combo_detail");
        factory.setServiceInterface(TradeComboDetailService.class);
        factory.setServerName("trade-manage");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean tradeAppService(
            @Value("${trade_manage.server}") String tradeManageServer,
            @Value("${trade_manage.server.connection_timeout:500}") int connectionTimeout,
            @Value("${trade_manage.server.read_timeout:6000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(tradeManageServer + "rpc/trade_app");
        factory.setServiceInterface(TradeAppService.class);
        factory.setServerName("trade-manage");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean metaService(
            @Value("${core-business.server}") String coreBusinessServer,
            @Value("${core-business.connection_timeout:500}") int connectionTimeout,
            @Value("${core-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreBusinessServer + "rpc/meta");
        factory.setServiceInterface(MetaService.class);
        factory.setServerName("core-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean t9Service(
            @Value("${merchant-contract-job.server}") String merchantContractJobServer,
            @Value("${merchant-contract-job.connection_timeout:500}") int connectionTimeout,
            @Value("${merchant-contract-job.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantContractJobServer + "/rpc/t9");
        factory.setServiceInterface(T9Service.class);
        factory.setServerName("merchant-contract-job");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean iCustomerRelationFacade(
            @Value("${crm-customer-relation.server}") String crmCustomerRelationServer,
            @Value("${crm-customer-relation.connection_timeout:500}") int connectionTimeout,
            @Value("${crm-customer-relation.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(crmCustomerRelationServer + "rpc/relation");
        factory.setServiceInterface(ICustomerRelationFacade.class);
        factory.setServerName("crm-customer-relation");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean brandFacade(
            @Value("${jsonrpc.brand-business.server}") String brandBusinessServer,
            @Value("${brand-business.connection_timeout:500}") int connectionTimeout,
            @Value("${brand-business.read_timeout:5000}") int readTimeout) {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(brandBusinessServer + "/rpc/brand");
        factory.setServiceInterface(BrandFacade.class);
        factory.setServerName("brand-business");
        factory.setConnectionTimeoutMillis(connectionTimeout);
        factory.setReadTimeoutMillis(readTimeout);
        return factory;
    }

}
