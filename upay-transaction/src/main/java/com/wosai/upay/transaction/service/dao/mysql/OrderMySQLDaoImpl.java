package com.wosai.upay.transaction.service.dao.mysql;

import com.wosai.upay.transaction.service.model.query.OrderHBaseQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

@Repository
public class OrderMySQLDaoImpl implements OrderMySQLDao {

    private static final Logger logger = LoggerFactory.getLogger(OrderMySQLDaoImpl.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final String BASE_ORDER_FIELDS = 
        "id, sn, client_sn, subject, body, items, net_items, status, tcp_modified, " +
        "original_total, net_original, effective_total, net_effective, " +
        "total_discount, net_discount, buyer_uid, buyer_login, merchant_id, " +
        "store_id, terminal_id, operator, provider, payway, sub_payway, " +
        "trade_no, reflect, ctime, mtime";

    private final RowMapper<Map<String, Object>> orderRowMapper = new OrderRowMapper();

    @Override
    public List<Map<String, Object>> queryList(OrderHBaseQuery query) {
        StringBuilder sql = new StringBuilder("SELECT ").append(BASE_ORDER_FIELDS)
                .append(" FROM `order` WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        buildWhereClause(sql, params, query);
        
        if (query.getOffset() != null && query.getLimit() != null) {
            sql.append(" LIMIT ?, ?");
            params.add(query.getOffset());
            params.add(query.getLimit());
        }
        
        sql.append(" ORDER BY ctime DESC");
        
        logger.debug("Querying orders with SQL: {}", sql);
        return jdbcTemplate.query(sql.toString(), params.toArray(), orderRowMapper);
    }

    @Override
    public Long count(OrderHBaseQuery query) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(1) FROM `order` WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        buildWhereClause(sql, params, query);
        
        logger.debug("Counting orders with SQL: {}", sql);
        return jdbcTemplate.queryForObject(sql.toString(), params.toArray(), Long.class);
    }

    @Override
    public Map<String, Object> queryBySn(String merchantId, String sn, long start, long end) {
        String sql = "SELECT " + BASE_ORDER_FIELDS + 
                    " FROM `order` WHERE merchant_id = ? AND sn = ? AND ctime BETWEEN ? AND ?";
        
        try {
            return jdbcTemplate.queryForObject(sql, new Object[]{merchantId, sn, start, end}, orderRowMapper);
        } catch (Exception e) {
            logger.warn("Order not found: merchantId={}, sn={}, start={}, end={}", merchantId, sn, start, end);
            return null;
        }
    }

    private void buildWhereClause(StringBuilder sql, List<Object> params, OrderHBaseQuery query) {
        if (!CollectionUtils.isEmpty(query.getMerchantIds())) {
            sql.append(" AND merchant_id IN (");
            sql.append(String.join(",", Collections.nCopies(query.getMerchantIds().size(), "?")));
            sql.append(")");
            params.addAll(query.getMerchantIds());
        }

        if (!CollectionUtils.isEmpty(query.getStoreIds())) {
            sql.append(" AND store_id IN (");
            sql.append(String.join(",", Collections.nCopies(query.getStoreIds().size(), "?")));
            sql.append(")");
            params.addAll(query.getStoreIds());
        }

        if (!CollectionUtils.isEmpty(query.getTerminalIds())) {
            sql.append(" AND terminal_id IN (");
            sql.append(String.join(",", Collections.nCopies(query.getTerminalIds().size(), "?")));
            sql.append(")");
            params.addAll(query.getTerminalIds());
        }

        if (query.getStartTime() != null) {
            sql.append(" AND ctime >= ?");
            params.add(query.getStartTime());
        }

        if (query.getEndTime() != null) {
            sql.append(" AND ctime <= ?");
            params.add(query.getEndTime());
        }

        if (!CollectionUtils.isEmpty(query.getPayWays())) {
            sql.append(" AND payway IN (");
            sql.append(String.join(",", Collections.nCopies(query.getPayWays().size(), "?")));
            sql.append(")");
            params.addAll(query.getPayWays());
        }

        if (!CollectionUtils.isEmpty(query.getSubPayWays())) {
            sql.append(" AND sub_payway IN (");
            sql.append(String.join(",", Collections.nCopies(query.getSubPayWays().size(), "?")));
            sql.append(")");
            params.addAll(query.getSubPayWays());
        }

        if (!CollectionUtils.isEmpty(query.getProviders())) {
            sql.append(" AND provider IN (");
            sql.append(String.join(",", Collections.nCopies(query.getProviders().size(), "?")));
            sql.append(")");
            params.addAll(query.getProviders());
        }

        if (!CollectionUtils.isEmpty(query.getStatusList())) {
            sql.append(" AND status IN (");
            sql.append(String.join(",", Collections.nCopies(query.getStatusList().size(), "?")));
            sql.append(")");
            params.addAll(query.getStatusList());
        }

        if (query.getMinTotalAmount() != null) {
            sql.append(" AND original_total >= ?");
            params.add(query.getMinTotalAmount());
        }

        if (query.getMaxTotalAmount() != null) {
            sql.append(" AND original_total <= ?");
            params.add(query.getMaxTotalAmount());
        }

        if (!CollectionUtils.isEmpty(query.getBuyerUids())) {
            sql.append(" AND buyer_uid IN (");
            sql.append(String.join(",", Collections.nCopies(query.getBuyerUids().size(), "?")));
            sql.append(")");
            params.addAll(query.getBuyerUids());
        }

        if (!CollectionUtils.isEmpty(query.getBuyerLogins())) {
            sql.append(" AND buyer_login IN (");
            sql.append(String.join(",", Collections.nCopies(query.getBuyerLogins().size(), "?")));
            sql.append(")");
            params.addAll(query.getBuyerLogins());
        }

        if (query.getProviderIsNull()) {
            sql.append(" AND provider IS NULL");
        }

        if (StringUtils.hasLength(query.getOrderSn())) {

            sql.append(" AND sn = ?");
            params.add(query.getOrderSn());
        }

        if (!CollectionUtils.isEmpty(query.getOrderSns())) {
            sql.append(" AND sn IN (");
            sql.append(String.join(",", Collections.nCopies(query.getOrderSns().size(), "?")));
            sql.append(")");
            params.addAll(query.getOrderSns());
        }

        if (StringUtils.hasLength(query.getClientSn())) {
            sql.append(" AND client_sn = ?");
            params.add(query.getClientSn());
        }

        if (StringUtils.hasLength(query.getTradeNo())) {
            sql.append(" AND trade_no = ?");
            params.add(query.getTradeNo());
        }

        sql.append(" AND deleted = 0");
    }

    private static class OrderRowMapper implements RowMapper<Map<String, Object>> {
        @Override
        public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
            Map<String, Object> result = new HashMap<>();
            
            result.put("id", rs.getString("id"));
            result.put("sn", rs.getString("sn"));
            result.put("client_sn", rs.getString("client_sn"));
            result.put("subject", rs.getString("subject"));
            result.put("body", rs.getString("body"));
            result.put("items", rs.getBytes("items"));
            result.put("net_items", rs.getBytes("net_items"));
            result.put("status", rs.getInt("status"));
            result.put("tcp_modified", rs.getBoolean("tcp_modified"));
            result.put("original_total", rs.getLong("original_total"));
            result.put("net_original", rs.getLong("net_original"));
            result.put("effective_total", rs.getLong("effective_total"));
            result.put("net_effective", rs.getLong("net_effective"));
            result.put("total_discount", rs.getLong("total_discount"));
            result.put("net_discount", rs.getLong("net_discount"));
            result.put("buyer_uid", rs.getString("buyer_uid"));
            result.put("buyer_login", rs.getString("buyer_login"));
            result.put("merchant_id", rs.getString("merchant_id"));
            result.put("store_id", rs.getString("store_id"));
            result.put("terminal_id", rs.getString("terminal_id"));
            result.put("operator", rs.getString("operator"));
            result.put("provider", rs.getInt("provider"));
            result.put("payway", rs.getInt("payway"));
            result.put("sub_payway", rs.getInt("sub_payway"));
            result.put("trade_no", rs.getString("trade_no"));
            result.put("reflect", rs.getBytes("reflect"));
            result.put("ctime", rs.getLong("ctime"));
            result.put("mtime", rs.getLong("mtime"));
            
            return result;
        }
    }
}