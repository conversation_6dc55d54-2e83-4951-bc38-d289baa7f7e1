package com.wosai.upay.transaction.service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.util.ApolloUtil;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.config.HbaseDispatcherTimeConfig;
import com.wosai.upay.transaction.service.dao.base.HBaseDao;
import com.wosai.upay.transaction.service.dao.redis.RedisMapCache;
import com.wosai.upay.transaction.service.model.query.OrderHBaseQuery;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.util.DateTimeUtil;
import com.wosai.upay.transaction.util.Pair;
import com.wosai.upay.transaction.util.SolrHBaseUtils.SolrPartition;
import com.wosai.upay.transaction.util.TransactionTypeRelatedUtil;

import lombok.SneakyThrows;

/**
 * GatewaySupportService
 *
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
public class GatewaySupportServiceImpl implements GatewaySupportService {


    /**
     * 缓存过期时间：10个时间单位
     */
    private static final int TTL_10 = 10;


    /**
     * 缓存过期时间：3个时间单位
     */
    private static final int TTL_3 = 3;

    /**
     * hbase rpc/operation 的超时时间：3000个时间单位
     */
    private static final int HBASE_TIMEOUT_3000 = 3000;

    /**
     * 缓存 HBase 查询时的 id-ctime 对
     */
    private static final Set<String> HBASE_ROWKEYS_CACHE_FIELDS = Sets.newTreeSet();


    static {
        HBASE_ROWKEYS_CACHE_FIELDS.add(DaoConstants.ID);
        HBASE_ROWKEYS_CACHE_FIELDS.add(DaoConstants.CTIME);
        HBASE_ROWKEYS_CACHE_FIELDS.add(CommonConstant.FIELD_EXIST);
    }

    @Autowired
    private RedisMapCache redisMapCache;

    @Autowired
    public HBaseDao<OrderHBaseQuery> orderHbaseDao;

    @Autowired
    public HBaseDao<TransactionHBaseQuery> transactionHBaseDao;
    @Autowired
    HbaseDispatcherTimeConfig hbaseDispatcherTimeConfig;

    @Override
    public Map<String, Object> getOrderBySn(String merchantId, String orderSn, String clientSn, String partition) {
        if (StringUtils.isEmpty(orderSn) && StringUtils.isEmpty(clientSn)) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "订单号和商户订单号不能同时都为空");
        }

        String key = CommonConstant.HBASE_ROWKEYS_PREFIX + "getOrderBySn:"
                + "merchantId" + Optional.ofNullable(merchantId).orElse("")
                + "orderSn" + Optional.ofNullable(orderSn).orElse("")
                + "clientSn" + Optional.ofNullable(clientSn).orElse("")
                + "partition" + partition;

        return this.getOrderOrTxFromIndexCache(key, this.orderHbaseDao,
                () -> {
                    try {
                        SphU.entry("GatewaySupportService.getOrderBySn()-Hbase", EntryType.IN, 1, merchantId);
                        OrderHBaseQuery hBaseQuery = new OrderHBaseQuery();
                        hBaseQuery.setSolrPartition(getSolrPartition(partition));
                        if (StringUtils.isNotEmpty(merchantId)) {
                            hBaseQuery.setMerchantIds(Lists.newArrayList(merchantId));
                        }
                        if (StringUtils.isNotEmpty(orderSn)) {
                            hBaseQuery.setOrderSn(orderSn);
                        } else if (StringUtils.isNotEmpty(clientSn)) {
                            hBaseQuery.setClientSn(clientSn);
                        } else {
                            throw new BizException(BizException.CODE_INVALID_PARAMETER, "订单号和商户订单号不能同时都为空");
                        }

                        List<Map<String, Object>> orders = this.orderHbaseDao.queryList(hBaseQuery);
                        // sn传的值有可能是拉卡拉或者兴业的订单号
                        if (CollectionUtil.isEmpty(orders) && StringUtils.isNotEmpty(orderSn)) {
                            hBaseQuery.setOrderSn(null);
                            hBaseQuery.setClientSn(null);
                            hBaseQuery.setTradeNo(orderSn);
                            orders = this.orderHbaseDao.queryList(hBaseQuery);
                        }

                        return (null != orders && orders.size() == 1) ? orders.get(0) : null;
                    } catch (BlockException e) {
                        throw new com.wosai.upay.transaction.exception.BlockException("请求过于频繁,请稍后重试");
                    } finally {
                        SphU.entryEnd(1);
                    }
                });
    }

    @Override
    public Map<String, Object> getTransactionByClientTsn(String merchantId, String orderSn, String clientTsn, Long ctime) {

        String key = CommonConstant.HBASE_ROWKEYS_PREFIX + "getTransactionByClientTsn:"
                + "merchantId" + merchantId
                + "orderSn" + orderSn
                + "clientTsn" + clientTsn
                + "ctime" + ctime;

        return gatewayTransactionFilter(
                this.getOrderOrTxFromIndexCache(key, this.transactionHBaseDao,
                        () -> {
                            TransactionHBaseQuery hBaseQuery = new TransactionHBaseQuery();
                            hBaseQuery.setMerchantIds(Lists.newArrayList(merchantId));
                            hBaseQuery.setOrderSns(Lists.newArrayList(orderSn));
                            hBaseQuery.setClientTsns(Lists.newArrayList(clientTsn));
                            hBaseQuery.setStartTime(hbaseDispatcherTimeConfig.getSolrLimitStartTime(ctime));
                            List<Map<String, Object>> transactions = gatewayTransactionFilter(this.transactionHBaseDao.queryList(hBaseQuery));
                            if (transactions == null || transactions.size() == 0) {
                                return null;
                            }
                            return transactions.parallelStream()
                                    .max(Comparator.comparingLong(transaction -> MapUtils.getLongValue(transaction, DaoConstants.CTIME)))
                                    .get();
                        }, TTL_10, TimeUnit.MINUTES)
        );
    }

    @Override
    public Map<String, Object> getLatestTransactionByOrderSn(String merchantId, String orderSn, Long ctime) {
        TransactionHBaseQuery hBaseQuery = new TransactionHBaseQuery();
        hBaseQuery.setStartTime(ctime);
        hBaseQuery.setMerchantIds(Lists.newArrayList(merchantId));
        hBaseQuery.setOrderSns(Lists.newArrayList(orderSn));

        List<Map<String, Object>> transactions = gatewayTransactionFilter(this.transactionHBaseDao.queryList(hBaseQuery));
        if (CollectionUtils.isEmpty(transactions)) {
            return null;
        }
        return gatewayTransactionFilter(
                transactions.stream()
                        .max(Comparator.comparing(transaction -> MapUtils.getLongValue(transaction, DaoConstants.CTIME)))
                        .get()
        );
    }

    @Override
    public Map<String, Object> getPayOrConsumerTransaction(String merchantId, String orderSn, Long ctime) {
        String key = CommonConstant.HBASE_ROWKEYS_PREFIX + "getPayOrConsumerTransaction:"
                + "merchantId" + merchantId + "orderSn" + orderSn + "ctime" + ctime;

        return this.getOrderOrTxFromIndexCache(key, this.transactionHBaseDao,
                () -> {
                    if (ctime >= hbaseDispatcherTimeConfig.getHotIndexStartTime()) {
                        TransactionHBaseQuery hBaseQuery = new TransactionHBaseQuery();
                        hBaseQuery.setMerchantIds(Lists.newArrayList(merchantId));
                        hBaseQuery.setOrderSns(Lists.newArrayList(orderSn));
                        hBaseQuery.setStartTime(ctime);
                        StatusTypeSubPayWayQuery remappingFunction = new StatusTypeSubPayWayQuery();
                        remappingFunction.setTypeList(Lists.newArrayList(TransactionTypeRelatedUtil.PAY_TYPE_FOR_GATEWAY));
                        remappingFunction.setStatusList(Lists.newArrayList(Transaction.STATUS_SUCCESS));
                        hBaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, remappingFunction);

                        List<Map<String, Object>> transaction = this.transactionHBaseDao.queryList(hBaseQuery);
                        return gatewayTransactionFilter((null != transaction && transaction.size() > 0) ? transaction.get(0) : null);
                    } else {
                        return gatewayTransactionFilter(transactionHBaseDao.queryBySn(merchantId, orderSn, ctime - 60 * 1000, ctime + 60 * 1000));
                    }
                },
                // 当交易状态为预授权完成或支付时，才缓存
                (transaction) -> {
                    if (transaction == null) {
                        return false;
                    }
                    Integer type = MapUtils.getInteger(transaction, Transaction.TYPE);
                    return TransactionTypeRelatedUtil.isPayTypeForGateWay(type);
                });
    }

    @Override
    public List<Map<String, Object>> getSuccessTransactionList(String merchantId, String orderSn, Long ctime) {
        TransactionHBaseQuery hBaseQuery = new TransactionHBaseQuery();
        hBaseQuery.setMerchantIds(Lists.newArrayList(merchantId));
        hBaseQuery.setOrderSns(Lists.newArrayList(orderSn));
        hBaseQuery.setStartTime(hbaseDispatcherTimeConfig.getSolrLimitStartTime(ctime));
        StatusTypeSubPayWayQuery remappingFunction = new StatusTypeSubPayWayQuery();
        remappingFunction.setStatusList(Lists.newArrayList(Transaction.STATUS_SUCCESS));
        hBaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, remappingFunction);

        return gatewayTransactionFilter(transactionHBaseDao.queryList(hBaseQuery));
    }

    @Override
    public List<Map<String, Object>> getRefundSuccessTransactionList(String merchantId, String orderSn, Long ctime) {
        TransactionHBaseQuery hBaseQuery = new TransactionHBaseQuery();
        hBaseQuery.setMerchantIds(Lists.newArrayList(merchantId));
        hBaseQuery.setOrderSns(Lists.newArrayList(orderSn));
        hBaseQuery.setStartTime(hbaseDispatcherTimeConfig.getSolrLimitStartTime(ctime));
        StatusTypeSubPayWayQuery remappingFunction = new StatusTypeSubPayWayQuery();
        remappingFunction.setTypeList(TransactionTypeRelatedUtil.REFUND_FOR_GATEWAY);
        remappingFunction.setStatusList(Lists.newArrayList(Transaction.STATUS_SUCCESS));
        hBaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, remappingFunction);

        return gatewayTransactionFilter(transactionHBaseDao.queryList(hBaseQuery));
    }


    /**
     * 从缓存中间接获取订单/交易数据（先缓存获取订单/交易id、ctime，再从 HBase 获取数据）
     *
     * @param cacheKey    缓存key
     * @param hBaseDao    订单或交易 Dao，{@link HBaseDao} 子类
     * @param valueLoader 没命中缓存时，数据加载器
     * @param ttl         过期时间数量
     * @param unit        过期时间单位
     * @return 订单/交易数据
     */
    private Map<String, Object> getOrderOrTxFromIndexCache(String cacheKey, HBaseDao<?> hBaseDao,
                                                           Callable<Map<String, Object>> valueLoader, int ttl, TimeUnit unit) {
        return getOrderOrTxFromIndexCache(cacheKey, hBaseDao, valueLoader, null, ttl, unit);
    }

    /**
     * 从缓存中间接获取订单/交易数据（先缓存获取订单/交易id、ctime，再从 HBase 获取数据）
     *
     * @param cacheKey    缓存key
     * @param hBaseDao    订单或交易 Dao，{@link HBaseDao} 子类
     * @param valueLoader 没命中缓存时，数据加载器
     * @return 订单/交易数据
     */
    private Map<String, Object> getOrderOrTxFromIndexCache(String cacheKey, HBaseDao<?> hBaseDao,
                                                           Callable<Map<String, Object>> valueLoader) {
        return getOrderOrTxFromIndexCache(cacheKey, hBaseDao, valueLoader, null);
    }


    /**
     * 从缓存中间接获取订单/交易数据（先缓存获取订单/交易id、ctime，再从 HBase 获取数据）
     *
     * @param cacheKey    缓存key
     * @param hBaseDao    订单或交易 Dao，{@link HBaseDao} 子类
     * @param valueLoader 没命中缓存时，数据加载器
     * @param predicate   是否缓存，null 和 true 时缓存
     * @return 订单/交易数据
     */
    @SneakyThrows
    private Map<String, Object> getOrderOrTxFromIndexCache(String cacheKey, HBaseDao<?> hBaseDao,
                                                           Callable<Map<String, Object>> valueLoader,
                                                           Predicate<Map<String, Object>> predicate) {
        return getOrderOrTxFromIndexCache(cacheKey, hBaseDao, valueLoader, predicate, TTL_3, TimeUnit.HOURS);
    }

    /**
     * 从缓存中间接获取订单/交易数据（先缓存获取订单/交易id、ctime，再从 HBase 获取数据）
     *
     * @param cacheKey    缓存key
     * @param hBaseDao    订单或交易 Dao，{@link HBaseDao} 子类
     * @param valueLoader 没命中缓存时，数据加载器
     * @param predicate   是否缓存，null 和 true 时缓存
     * @param ttl         过期时间数量
     * @param unit        过期时间单位
     * @return 订单/交易数据
     */
    @SneakyThrows
    private Map<String, Object> getOrderOrTxFromIndexCache(String cacheKey, HBaseDao<?> hBaseDao,
                                                           Callable<Map<String, Object>> valueLoader,
                                                           Predicate<Map<String, Object>> predicate, int ttl, TimeUnit unit) {
        Assert.hasText(cacheKey, "cacheKey cannot be empty");
        Map<String, Object> orderOrTx = null;

        // 查缓存
        Map<String, Object> idCtimeMap = this.redisMapCache.getAllAsyncAndRefreshTtl(cacheKey, HBASE_ROWKEYS_CACHE_FIELDS, ttl, unit);

        // 命中缓存
        if (MapUtils.isNotEmpty(idCtimeMap)) {
            // 如果指示数据不存在，则直接返回
            if (Objects.equals(CommonConstant.NOT_EXIST, MapUtils.getInteger(idCtimeMap, CommonConstant.FIELD_EXIST))) {
                return null;
            }

            // 如果数据存在，则直接查 HBase
            Pair<String, Long> idCtimePair = Pair.of(MapUtils.getString(idCtimeMap, DaoConstants.ID), MapUtils.getLong(idCtimeMap, DaoConstants.CTIME));
            List<Pair<String, Long>> idCtimeList = Collections.singletonList(idCtimePair);
            List<Map<String, Object>> orderList = hBaseDao.rowFilterByIds(idCtimeList, null, HBASE_TIMEOUT_3000);
            orderOrTx = (null != orderList && orderList.size() == 1) ? orderList.get(0) : null;

        } else if (valueLoader != null) {

            orderOrTx = valueLoader.call();
            // 指示是否要缓存（默认是要缓存）
            if (predicate == null || predicate.test(orderOrTx)) {
                idCtimeMap = Maps.newTreeMap();
                // 查询的数据不为空时，缓存 id 和 ctime
                if (MapUtils.isNotEmpty(orderOrTx)) {
                    String merchantId = MapUtils.getString(orderOrTx, Transaction.MERCHANT_ID);
                    Long ctime = MapUtils.getLong(orderOrTx, DaoConstants.CTIME);
                    String id = MapUtils.getString(orderOrTx, DaoConstants.ID);

                    byte[] rowKey = Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(ctime), Bytes.toBytes(id));
                    idCtimeMap.put(DaoConstants.ID, Hex.encodeHexString(rowKey));
                    idCtimeMap.put(DaoConstants.CTIME, orderOrTx.get(DaoConstants.CTIME));
                    this.redisMapCache.putAll(cacheKey, TTL_3, TimeUnit.HOURS, idCtimeMap);

                } else { // 如果查询到数据不存在时 也需要缓存
                    idCtimeMap.put(CommonConstant.FIELD_EXIST, CommonConstant.NOT_EXIST); // 这里的标记位用 exist
                    this.redisMapCache.putAll(cacheKey, ApolloUtil.getGatewayNotExistCacheSeconds(), TimeUnit.SECONDS, idCtimeMap);
                }
            }
        }

        return orderOrTx;
    }

    /**
     * 通过字符串描述获取对应的 Solr 冷热数据分区
     *
     * @see DataPartitionConst
     * @see SolrPartition
     */
    private SolrPartition getSolrPartition(String value) {
        if (DataPartitionConst.HOT.equals(value)) {
            return SolrPartition.hot;
        }

        if (DataPartitionConst.RECENT_6M.equals(value)) {
            return SolrPartition.recent_6m;
        }

        if (DataPartitionConst.COLD.equals(value)) {
            return SolrPartition.cold;
        }

        throw new BizException(BizException.CODE_INVALID_PARAMETER, "指明数据分区的值无效");
    }

    /**
     * 过滤查询出的流水
     * <p>
     * 原因：查询出来的交易可能不是扫码交易，网关只需要扫码交易，需要做特殊处理
     *
     * @param transaction
     * @return
     */
    private Map<String, Object> gatewayTransactionFilter(Map<String, Object> transaction) {
        Map<String, Object> newValue = transaction;
        Integer type = MapUtil.getInteger(transaction, Transaction.TYPE);
        if (type != null) {
            // 部分流水在写入hbse时会做转换
            if (TransactionTypeRelatedUtil.GATEWAY_TYPE_CONVERT.containsKey(type)) {
                transaction.put(Transaction.TYPE, TransactionTypeRelatedUtil.GATEWAY_TYPE_CONVERT.get(type));
            } else if (!TransactionTypeRelatedUtil.GATEWAY_ALL_TYPE.contains(type)) {
                newValue = null;
            }
        }
        return newValue;
    }

    /**
     * 过滤查询出的流水
     * <p>
     * 原因：查询出来的交易可能不是扫码交易，网关只需要扫码交易，需要做特殊处理
     */
    private List<Map<String, Object>> gatewayTransactionFilter(List<Map<String, Object>> transactions) {
        List<Map<String, Object>> newValue = transactions;
        if (CollectionUtil.isNotEmpty(transactions)) {
            newValue = transactions.stream()
                    .map(this::gatewayTransactionFilter)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        return newValue;
    }

    @Override
    public List<Map<String, Object>> getAlipayDepositConsumeTransactionList(String merchantId, String orderSn,Long ctime) {
        TransactionHBaseQuery hbaseQuery = new TransactionHBaseQuery();
        hbaseQuery.setOrderSns(Arrays.asList(orderSn));
        hbaseQuery.setMerchantIds(Arrays.asList(merchantId));
        hbaseQuery.setPayWays(Arrays.asList(Order.PAYWAY_ALIPAY2));
        hbaseQuery.setStartTime(ctime);
        // solr默认返回10条，设置返回全部(setFacetLimit(-1))时也返回10条，故只能加个条数限制
        hbaseQuery.setLimit(100);
        StatusTypeSubPayWayQuery remappingFunction = new StatusTypeSubPayWayQuery();
        remappingFunction.setTypeList(Arrays.asList(Transaction.TYPE_DEPOSIT_CONSUME));
        hbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.ERROR, remappingFunction);
        List<Map<String, Object>> result = transactionHBaseDao.queryList(hbaseQuery);
        // 原始数据没有排序，按照创建时间由小到大排序
        if(!com.wosai.pantheon.util.CollectionUtil.isEmpty(result)) {
            result.sort((r1, r2) -> MapUtil.getLong(r1, DaoConstants.CTIME) < MapUtil.getLong(r2, DaoConstants.CTIME) ? -1 : 1);
        }
        return result;
    }

    @Override
    public Map<String, Object> getOrderBySnAndDate(String merchantId, String orderSn, String date) {
        Date dateTime = DateTimeUtil.parse(DateTimeUtil.DAY_FORMAT, date);
        if (dateTime == null) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "交易日期错误");
        }
        long start = dateTime.getTime();
        long end = start + DateTimeUtil.ONE_DAY_MILLIS - 1;

        String key = CommonConstant.HBASE_ROWKEYS_PREFIX + "getOrderBySn:"
                + "merchantId" + Optional.ofNullable(merchantId).orElse("")
                + "orderSn" + Optional.ofNullable(orderSn).orElse("")
                + "date" + Optional.ofNullable(date).orElse("");

        AtomicBoolean needCache = new AtomicBoolean(false);
        Map<String, Object> order = this.getOrderOrTxFromIndexCache(key, this.orderHbaseDao,
                () -> {
                    needCache.set(true);
                    return orderHbaseDao.queryBySn(merchantId, orderSn, start, end);
                });
        // 添加缓存，用于查询接口返回
        if (order != null && needCache.get()) {
            key = CommonConstant.HBASE_ROWKEYS_PREFIX + "getOrderBySn:"
                    + "merchantId" + Optional.ofNullable(merchantId).orElse("")
                    + "orderSn" + Optional.ofNullable(orderSn).orElse("")
                    + "clientSn" + Optional.ofNullable(null).orElse("")
                    + "partition" + DataPartitionConst.COLD;
            
            Map<String, Object> idCtimeMap = Maps.newTreeMap();
            // 查询的数据不为空时，缓存 id 和 ctime
            Long ctime = MapUtils.getLong(order, DaoConstants.CTIME);
            String id = MapUtils.getString(order, DaoConstants.ID);

            byte[] rowKey = Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(ctime), Bytes.toBytes(id));
            idCtimeMap.put(DaoConstants.ID, Hex.encodeHexString(rowKey));
            idCtimeMap.put(DaoConstants.CTIME, order.get(DaoConstants.CTIME));
            redisMapCache.putAll(key, TTL_3, TimeUnit.HOURS, idCtimeMap);
        }
        return order;
    }
}
