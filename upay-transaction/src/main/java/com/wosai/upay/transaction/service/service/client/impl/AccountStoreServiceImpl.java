package com.wosai.upay.transaction.service.service.client.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.wosai.app.backend.api.consts.BasicConsts;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.market.saas.merchant.api.service.CustomerService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.VendorApp;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.OperatorType;
import com.wosai.upay.transaction.enums.TerminalType;
import com.wosai.upay.transaction.service.CacheService;
import com.wosai.upay.transaction.service.model.TerminalCode;
import com.wosai.upay.transaction.service.service.client.IAccountStoreService;
import com.wosai.upay.transaction.util.ConfigServiceUtils;
import com.wosai.upay.transaction.util.EnumUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AccountStoreServiceImpl implements IAccountStoreService {

    public static final Log logger = LogFactory.getLog(AccountStoreServiceImpl.class);

    @Autowired
    private StoreService storeService;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Resource
    private CustomerService customerService;

    public static final String MERCHANT_STAFF_CACHE_PREFIX = "U::";

    public static final String STORE_CACHE_PREFIX = "S::";

    public static final String TERMINAL_CACHE_PREFIX = "T::";

    public static final String TERMINAL_SN_CACHE_PREFIX = "TSN::";

    public static final String PAYER_ACCOUNT_CACHE_PREFIX = "A::";

    public final LoadingCache<String,Map<String,String>> MERCHANT_STAFF_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, Map<String, String>>() {
                @Override
                public Map<String, String> load(String merchantId) {
                    return loadStoreStaffByMerchant(merchantId);
                }
            });


    public final LoadingCache<String,Map<String,String>> STORE_TERMINAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, Map<String, String>>() {
                @Override
                public Map<String, String> load(String storeId) {
                    return loadStoreById(storeId);
                }
            });


    public final LoadingCache<String,Map<String,String>> TERMINAL_SN_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .maximumSize(10000).concurrencyLevel(256).build(new CacheLoader<String, Map<String, String>>() {
                @Override
                public Map<String, String> load(String terminalSn) {
                    return loadTerminalBySn(terminalSn);
                }
            });


    public final LoadingCache<String, TerminalCode> TERMINAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, TerminalCode>() {
                @Override
                public TerminalCode load(String terminalId){
                    TerminalCode terminalCode = (TerminalCode)cacheService.getObjectRedisCache(TERMINAL_CACHE_PREFIX + terminalId);
                    if(terminalCode == null){
                        Map terminalMap = terminalService.getTerminalByTerminalId(terminalId);
                        if(!CollectionUtils.isEmpty(terminalMap)){
                            terminalCode = buildTerminalCode(terminalMap);
                        }else{
                            terminalCode = new TerminalCode();
                        }
                        cacheService.setObjectRedisCache(TERMINAL_CACHE_PREFIX + terminalId, terminalCode, 15, TimeUnit.MINUTES);
                    }
                    return terminalCode;
                }
            });


    public Map<String, Integer> VENDOR_APP_CATEGORY_CACHE = Collections.emptyMap();

    public final Cache<String,String> PAYER_ACCOUNT_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build();

    @PostConstruct
    public void init(){
        VENDOR_APP_CATEGORY_CACHE = loadVendorAppCategory();
        //每隔10分钟更新一次
        Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> VENDOR_APP_CATEGORY_CACHE = loadVendorAppCategory(), 1, 10, TimeUnit.MINUTES);
    }

    @Override
    public Map<String, String> findStoreStaffByMerchant(String merchantId) {
        try {
          Map<String, String> storeStaffMap = MERCHANT_STAFF_CACHE.get(merchantId);
          return storeStaffMap;
        }catch (Exception e){
            logger.error("findStoreStaffByMerchant error: "+e.getMessage());
        }
        return new HashMap<>(16);
    }

    @Override
    public Map<String, String> findStoreStaffByMerchantIds(Set<String> merchantIds) {
        Map<String, String> map = Maps.newHashMap();
        try {
            for (String merchantId : merchantIds) {
                map.putAll(MERCHANT_STAFF_CACHE.get(merchantId));
            }
            return map;
        }catch (Exception e){
            logger.error("findStoreStaffByMerchant error: "+e.getMessage());
        }
        return new HashMap<>(16);
    }


    @Override
    public TerminalCode findTerminalByTerminalId(String terminalId){
        try {
           return TERMINAL_CACHE.get(terminalId);
        } catch (ExecutionException e) {
            logger.error("findTerminalByTerminalId error: "+e.getMessage());
        }
        return null;
    }


    public Map<String, String> loadStoreById(String storeId){
        Map store = cacheService.getHashRedisCache(STORE_CACHE_PREFIX + storeId);
        if(!CollectionUtils.isEmpty(store)){
            return store;
        }else{
            Map<String, String> nwMap = new HashMap(16);
            try {
                //从2.0 获取store
                store = storeService.getStoreByStoreId(storeId);
                if (WosaiMapUtils.isNotEmpty(store)) {
                    nwMap.put("sn", WosaiMapUtils.getString(store, "sn"));
                    nwMap.put("name", WosaiMapUtils.getString(store, "name"));
                    nwMap.put("merchant_id",WosaiMapUtils.getString(store,"merchant_id"));
                }
            } catch (Exception e) {
                logger.error("find store error." + e.getMessage());
            }
            cacheService.setHashRedisCache(STORE_CACHE_PREFIX + storeId, nwMap, 15 ,TimeUnit.MINUTES);
            return nwMap;
        }
    }

    public String loadPayerAccount(String merchantId, String storeId, String buyerUid, int payWay, String appid){
        String redisKey = String.format("%s:%s:%s", storeId, buyerUid, payWay);
        Object customerName = cacheService.getObjectRedisCache(PAYER_ACCOUNT_CACHE_PREFIX + redisKey);
        if (StringUtils.isEmpty(customerName)) {
            customerName = CommonConstant.DEFAULT_CUSTOMER_NAME;
            try {
                //从营销获取顾客/会员昵称
                Map request = CollectionUtil.hashMap(CommonConstant.MERCHANT_ID, merchantId,
                        CommonConstant.STORE_ID, storeId,
                        CommonConstant.BUYER_ID, buyerUid,
                        CommonConstant.PAYWAY, payWay,
                        CommonConstant.APPID, appid);

                Map result = customerService.getCustomerInfoForAppAccount(request);
                customerName = MapUtils.getString(result, CommonConstant.CUSTOMER_NAME, CommonConstant.DEFAULT_CUSTOMER_NAME);
            } catch (Exception e) {
                logger.error("get customer name error." + e.getMessage());
            }
            cacheService.setObjectRedisCache(PAYER_ACCOUNT_CACHE_PREFIX + redisKey, customerName, 15, TimeUnit.MINUTES);
        }
        return String.valueOf(customerName);
    }

    private Map<String, String> loadTerminalBySn(String terminalSn){
        Map terminalMap = cacheService.getHashRedisCache(TERMINAL_SN_CACHE_PREFIX + terminalSn);
        if(!CollectionUtils.isEmpty(terminalMap)){
            return terminalMap;
        }else{
            Map<String, String> nwMap = new HashMap(16);
            try {
                terminalMap = terminalService.getTerminalBySn(terminalSn);
                if (WosaiMapUtils.isNotEmpty(terminalMap)) {
                    nwMap.put("storeId", WosaiMapUtils.getString(terminalMap, "store_id"));
                    nwMap.put("storeName", WosaiMapUtils.getString(terminalMap, "store_name"));
                    nwMap.put("merchantName",WosaiMapUtils.getString(terminalMap,"merchant_name"));
                    nwMap.put("merchantId",WosaiMapUtils.getString(terminalMap,"merchant_id"));
                    nwMap.put("terminalId",WosaiMapUtils.getString(terminalMap,"id"));
                }
            } catch (Exception e) {
                logger.error("find terminal error." + e.getMessage());
            }
            cacheService.setHashRedisCache(TERMINAL_SN_CACHE_PREFIX + terminalSn, nwMap, 15 ,TimeUnit.MINUTES);
            return nwMap;
        }
    }

    private Map<String, Integer> loadVendorAppCategory(){
        List<Map> categories = terminalService.getVendorAppsByCategory(null);
        if(categories == null){
            return Collections.emptyMap();
        }
        return categories.stream().collect(Collectors.toMap(vendorApp -> MapUtil.getString(vendorApp, VendorApp.APPID), vendorApp -> MapUtil.getInteger(vendorApp, VendorApp.CATEGORY)));
    }


    @Override
    public Map<String, String> findTerminalByTerminalSn(String terminalSn) {
        try {
            return TERMINAL_SN_CACHE.get(terminalSn);
        }catch (Exception e){
            logger.error("findTerminal error: "+ e.getMessage());
        }
        return Maps.newHashMap();
    }

    @Override
    public Map findStore(String storeId) {
        try {
            return STORE_TERMINAL_CACHE.get(storeId);
        }catch (Exception e){
            logger.error("findStore error: "+ e.getMessage());
        }
        return Maps.newHashMap();
    }

    @Override
    public String findPayerAccount(String merchantId, String storeId, String buyerUid, int payWay, String appid) {
        try {
            String key = String.format("%s:%s:%s:%s:%s", merchantId, storeId, buyerUid, payWay, appid);
            return PAYER_ACCOUNT_CACHE.get(key, () -> {
                // 如果缓存中不存在该键，则从redis中获取
                return loadPayerAccount(merchantId, storeId, buyerUid, payWay, appid);
            });
        }catch (Exception e){
            logger.error("findPayerAccount error: "+ e.getMessage());
        }
        return CommonConstant.DEFAULT_CUSTOMER_NAME;
    }

    private Map<String, String> loadStoreStaffByMerchant(String merchantId){
        Map<String,String> result = cacheService.getCacheMap(MERCHANT_STAFF_CACHE_PREFIX, merchantId);
        if(!CollectionUtils.isEmpty(result)){
            return result;
        }
        QueryMerchantUserReq req = new QueryMerchantUserReq()
                .setMerchant_id(merchantId)
                .setRoles(Arrays.asList(BasicConsts.SUPER_ADMIN, BasicConsts.ADMIN, BasicConsts.CASHIER));
        List<UcMerchantUserInfo> staffs = merchantUserServiceV2.getMerchantUser(req);
        result = convertStaffInfo(staffs);
        cacheService.setCache(MERCHANT_STAFF_CACHE_PREFIX, merchantId, result, TimeUnit.HOURS, 1);
        return result;
    }

    private Map<String, String> convertStaffInfo(List<UcMerchantUserInfo> staffList) {
        try {
            Map<String, String> resultMap = new HashMap(16);
            if (WosaiCollectionUtils.isNotEmpty(staffList)) {
                staffList.forEach(o->{
                    String accountId = o.getUcUserInfo().getUc_user_id();
                    String operatorId = o.getOperator_id();
                    String username = o.getName();
                    if(WosaiStringUtils.isNotBlank(accountId)){
                        if(WosaiStringUtils.isNotBlank(username)){
                            resultMap.put(accountId, username);
                            resultMap.put(operatorId, username);
                        }else{
                            resultMap.put(accountId, OperatorType.CASHIER.getDesc());
                        }
                    }
                });
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("convertStaffInfo error : " + e.getMessage());
        }
        return new HashMap<>();
    }

    private TerminalCode buildTerminalCode(Map terminalMap) {
        String name = WosaiMapUtils.getString(terminalMap, "name");
        String sn = WosaiMapUtils.getString(terminalMap, "sn");
        String deviceFingerprint = WosaiMapUtils.getString(terminalMap, "device_fingerprint");
        Integer type = WosaiMapUtils.getIntValue(terminalMap, "type");
        String vendorAppAppid = WosaiMapUtils.getString(terminalMap, "vendor_app_appid");
        TerminalCode terminalCode = new TerminalCode(deviceFingerprint, name);
        TerminalType terminalType = EnumUtils.getByCode(TerminalType.class, type);
        terminalCode.setVendorAppAppid(vendorAppAppid);
        if(StringUtils.hasLength(vendorAppAppid)){
            terminalCode.setVendorAppName(ConfigServiceUtils.APP_ID_TERMINAL_TYPE_MAP.get(terminalCode.getVendorAppAppid()));
        }
        terminalCode.setSn(sn);
        terminalCode.setType(type);
        terminalCode.setTypeName(Objects.isNull(terminalType) ? null : terminalType.getDesc());
        Integer category = VENDOR_APP_CATEGORY_CACHE.get(vendorAppAppid);
        terminalCode.setCategory(category);
        terminalCode.setCategoryName(ConfigServiceUtils.getVendorAppCategoryDesc(category));
        return terminalCode;

    }
}
