package com.wosai.upay.transaction.service.model.vo;


import lombok.Data;

@Data
public class StoreSummary extends BaseSummary {


    private String storeId;

    private String storeName;
    private String time;

    public StoreSummary() {
    }


    public StoreSummary(Long statementId, String paymentTime, String storeId) {
        this.statementId = statementId;
        this.time = paymentTime.substring(0, 10).replace("-", "/");
        this.storeId = storeId;
    }


}
