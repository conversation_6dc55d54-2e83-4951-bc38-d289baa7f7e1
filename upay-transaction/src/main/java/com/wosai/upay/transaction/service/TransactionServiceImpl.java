package com.wosai.upay.transaction.service;


import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.app.dto.StoreAuthsReq;
import com.wosai.app.service.DepartmentService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DatabaseQueryConstant;
import com.wosai.upay.transaction.constant.QueryFlagConstant;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.repository.DataRepository;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.util.CommonUtil;
import com.wosai.upay.transaction.util.PageInfoChecker;
import com.wosai.upay.transaction.util.TransactionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */

@AutoJsonRpcServiceImpl
@Service
public class TransactionServiceImpl implements TransactionService {
    public static final Logger logger = LoggerFactory.getLogger(TransactionServiceImpl.class);

    @Autowired
    public DataRepository dataRepository;
    @Autowired
    public BusinessService businessService;
    @Autowired
    public TerminalService terminalService;
    @Autowired
    public StoreService storeService;
    @Autowired
    private TransactionHBaseDao transactionHbaseDao;
    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;
    @Autowired
    @Qualifier("merchantUserDepartmentService")
    private DepartmentService departmentService;

    //-------------------------订单的流水列表-------------------------

    /**
     * 根据ordersn获取交易
     * @param orderSn
     * @return
     */
    @Override
    public List getTransactionListByOrderSn(String orderSn) {
        if (StringUtil.empty(orderSn)) {
            return new ArrayList<>();
        }
        TransactionHBaseQuery transactionHbaseQuery = new TransactionHBaseQuery();
        transactionHbaseQuery.setOrderSns(Lists.newArrayList(orderSn));
        transactionHbaseQuery.getOrderBys().add(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC));

        List list = transactionHbaseDao.queryList(transactionHbaseQuery);
        formatTransactions(list, Arrays.asList(
                QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO
        ));
        return list;
    }

    //-------------------------流水列表-------------------------
    @Override
    public List getMerchantTransactionList(String merchantId, String merchantUserId, List<String> departmentIds, String storeId, String terminalId, PageInfo pageInfo, Map<String, Object> queryFilter) {
        PageInfoChecker.check(pageInfo);

        if (queryFilter == null) {
            queryFilter = new HashMap<>(16);
        }
        if (!StringUtil.empty(merchantId)) {
            queryFilter.put("merchant_id", merchantId);
        }
        if (!StringUtil.empty(storeId)) {
            queryFilter.put("store_id", storeId);
        }
        if (!StringUtil.empty(terminalId)) {
            queryFilter.put("terminal_id", terminalId);
        }
        ListResult listResult = getTransactions(pageInfo, queryFilter, false);
        if (listResult == null) {
            return Collections.emptyList();
        }
        return listResult.getRecords();
    }

    @Override
    public long getMerchantTransactionCount(String merchantId, String accountId, List<String> departmentIds, String storeId, String terminalId, PageInfo pageInfo, Map<String, Object> queryFilter) {
        PageInfoChecker.check(pageInfo);

        if (queryFilter == null) {
            queryFilter = new HashMap<>(16);
        }
        if (!StringUtil.empty(merchantId)) {
            queryFilter.put("merchant_id", merchantId);
        }
        if (!StringUtil.empty(storeId)) {
            queryFilter.put("store_id", storeId);
        }
        if (!StringUtil.empty(terminalId)) {
            queryFilter.put("terminal_id", terminalId);
        }

        TransactionHBaseQuery transactionHbaseQuery = getTransactionHbaseQuery(pageInfo, queryFilter);

        if (Objects.isNull(transactionHbaseQuery)) {
            return 0;
        }

        return transactionHbaseDao.count(transactionHbaseQuery);
    }


    private TransactionHBaseQuery getTransactionHbaseQuery(PageInfo pageInfo, Map<String, Object> queryFilter) {
        List<String> departmentIds = (List) BeanUtil.getProperty(queryFilter, "department_ids");
        String merchantUserId = BeanUtil.getPropString(queryFilter, "merchant_user_id");
        String terminalId = BeanUtil.getPropString(queryFilter, "terminal_id");
        String storeId = BeanUtil.getPropString(queryFilter, "store_id");
        List<String> storeIds = (List) BeanUtil.getProperty(queryFilter, "store_ids");

        String merchantId = BeanUtil.getPropString(queryFilter, "merchant_id");
        List<String> merchantIds = (List) BeanUtil.getProperty(queryFilter, "merchant_ids");

        Long status = CommonUtil.parseLong(BeanUtil.getPropString(queryFilter, "status"));

        List<Integer> types = (List) BeanUtil.getProperty(queryFilter, "types");

        pageInfo = CommonUtil.setPageInfoDefaultValueIfNull(pageInfo);
        //最大条数限制
        if (pageInfo.getPageSize() > DatabaseQueryConstant.MAX_PAGE_SIZE_LIMIT) {
            throw new BizException(BizException.CODE_EXCEED_MAX_PAGE_SIZE, "每页数据过多，禁止查询");
        }
        if (pageInfo.getPage() > 1) {
            throw new BizException(BizException.CODE_FORBIDDEN_OVER_PAGE, "流水不允许跨页查询");
        }

        TransactionHBaseQuery transactionHbaseQuery = new TransactionHBaseQuery();

        List<List<String>> allMerchantIds = new ArrayList<>();
        List<List<String>> allStoreIds = new ArrayList<>();
        List<List<String>> allTerminalIds = new ArrayList<>();


        if (!StringUtil.empty(terminalId)) {
            Map<String, Object> terminal = terminalService.getTerminal(terminalId);
            if (terminal == null) {
                return null;
            }
            String localMerchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
            allTerminalIds.add(Arrays.asList(terminalId));
            allMerchantIds.add(Arrays.asList(localMerchantId));
        }
        List localMerchantIds = new ArrayList();
        List localStoreIds = new ArrayList();
        if (departmentIds != null && departmentIds instanceof List) {
            Set<String> departmentStores = departmentService.listStoreIdsByDepartmentId(merchantId, departmentIds);
            for (String localstoreId : departmentStores) {
                Map<String, Object> store = storeService.getStore(localstoreId);
                if (store == null) {
                    continue;
                }
                String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
                localStoreIds.add(localstoreId);
                localMerchantIds.add(localMerchantId);
            }
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        } else if (!StringUtil.empty(merchantUserId)) {
            StoreAuthsReq storeAuthsReq = new StoreAuthsReq();
            storeAuthsReq.setPage(1).setPageSize(100).setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
            storeAuthsReq.setMerchant_user_id(merchantUserId);
            com.wosai.web.api.ListResult<Map> localStores = merchantUserServiceV2.getAuthStores(storeAuthsReq);
            List<Map> records = localStores.getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                for (Map localStore : records) {
                    Map<String, Object> store = storeService.getStore(BeanUtil.getPropString(localStore, DaoConstants.ID));
                    if (store == null) {
                        continue;
                    }
                    String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
                    localStoreIds.add(BeanUtil.getPropString(localStore, DaoConstants.ID));
                    localMerchantIds.add(localMerchantId);
                }
                allStoreIds.add(localStoreIds);
                allMerchantIds.add(localMerchantIds);
            }
        } else if (storeIds != null && storeIds instanceof List) {
            for (String localstoreId : storeIds) {
                Map<String, Object> store = storeService.getStore(localstoreId);
                if (store == null) {
                    continue;
                }
                String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
                localStoreIds.add(localstoreId);
                localMerchantIds.add(localMerchantId);
            }
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        }
        if (!StringUtil.empty(storeId)) {
            Map<String, Object> store = storeService.getStore(storeId);
            if (store == null) {
                return null;
            }
            String localStoreId = BeanUtil.getPropString(store, DaoConstants.ID);
            String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
            allStoreIds.add(Arrays.asList(localStoreId));
            allMerchantIds.add(Arrays.asList(localMerchantId));
        }

        if (merchantIds != null && merchantIds instanceof List) {
            allMerchantIds.add(merchantIds);
        }
        if (!StringUtil.empty(merchantId)) {
            allMerchantIds.add(Arrays.asList(merchantId));
        }

        if (allTerminalIds.size() > 0) {
            Set<String> allSameTerminalIds = CommonUtil.getAllSameElementInLists(allTerminalIds);
            if (allSameTerminalIds.size() == 0) {
                return null;
            }
            transactionHbaseQuery.setTerminals(new ArrayList<>(allSameTerminalIds));
        }
        if (allStoreIds.size() > 0) {
            Set<String> allSameStoreIds = CommonUtil.getAllSameElementInLists(allStoreIds);
            if (allSameStoreIds.size() == 0) {
                return null;
            }
            transactionHbaseQuery.setStoreIds(new ArrayList<>(allSameStoreIds));
        }
        if (allMerchantIds.size() > 0) {
            Set<String> allSameMerchantIds = CommonUtil.getAllSameElementInLists(allMerchantIds);
            if (allSameMerchantIds.size() == 0) {
                return null;
            }
            transactionHbaseQuery.setMerchantIds(new ArrayList<>(allSameMerchantIds));
        }
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();

        if (status != null) {
            statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(status.intValue()));
        }

        if(!CollectionUtils.isEmpty(types)){
            statusTypeSubPayWayQuery.setTypeList(types);
        }

        transactionHbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);

        transactionHbaseQuery.setStartTime(pageInfo.getDateStart());
        transactionHbaseQuery.setEndTime(pageInfo.getDateEnd());

        return transactionHbaseQuery;
    }

    private ListResult getTransactions(PageInfo pageInfo, Map<String, Object> queryFilter, Boolean seleceCount) {
        List<String> departmentIds = (List) BeanUtil.getProperty(queryFilter, "department_ids");
        String merchantUserId = BeanUtil.getPropString(queryFilter, "merchant_user_id");
        String terminalId = BeanUtil.getPropString(queryFilter, "terminal_id");
        String storeId = BeanUtil.getPropString(queryFilter, "store_id");
        List<String> storeIds = (List) BeanUtil.getProperty(queryFilter, "store_ids");

        List<Integer> types = (List) BeanUtil.getProperty(queryFilter, "types");

        String merchantId = BeanUtil.getPropString(queryFilter, "merchant_id");
        List<String> merchantIds = (List) BeanUtil.getProperty(queryFilter, "merchant_ids");

        Long status = CommonUtil.parseLong(BeanUtil.getPropString(queryFilter, "status"));
        String includes = BeanUtil.getPropString(queryFilter, StatementTaskLog.INCLUDES);
        pageInfo = CommonUtil.setPageInfoDefaultValueIfNull(pageInfo);
        //最大条数限制
        if (pageInfo.getPageSize() > DatabaseQueryConstant.MAX_PAGE_SIZE_LIMIT) {
            throw new BizException(BizException.CODE_EXCEED_MAX_PAGE_SIZE, "每页数据过多，禁止查询");
        }
        if (pageInfo.getPage() > 1) {
            throw new BizException(BizException.CODE_FORBIDDEN_OVER_PAGE, "流水不允许跨页查询");
        }

        int hbaseTimeout = MapUtil.getIntValue(queryFilter, CommonConstant.HBASE_TIMEOUT);
        int solrTimeout = MapUtil.getIntValue(queryFilter, CommonConstant.SOLR_TIMEOUT);

        boolean includeBrand = false;
        if (queryFilter != null && queryFilter.containsKey("include_brand")) {
            includeBrand = BeanUtil.getPropBoolean(queryFilter, "include_brand");
        }

        List<List<String>> allMerchantIds = new ArrayList<>();
        List<List<String>> allStoreIds = new ArrayList<>();
        List<List<String>> allTerminalIds = new ArrayList<>();

        if (!StringUtil.empty(terminalId)) {
            Map<String, Object> terminal = terminalService.getTerminal(terminalId);
            if (terminal == null) {
                return ListResult.emptyListResult();
            }
            String localMerchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
            allTerminalIds.add(Arrays.asList(terminalId));
            allMerchantIds.add(Arrays.asList(localMerchantId));
        }
        List localMerchantIds = new ArrayList();
        List localStoreIds = new ArrayList();
        if (storeIds != null && storeIds instanceof List) {
            for (String localstoreId : storeIds) {
                Map<String, Object> store = storeService.getStore(localstoreId);
                if (store == null) {
                    continue;
                }
                String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
                localStoreIds.add(localstoreId);
                localMerchantIds.add(localMerchantId);
            }
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        } else if (departmentIds != null && departmentIds instanceof List) {
            Set<String> departmentStores = departmentService.listStoreIdsByDepartmentId(merchantId, departmentIds);
            for (String localstoreId : departmentStores) {
                Map<String, Object> store = storeService.getStore(localstoreId);
                if (store == null) {
                    continue;
                }
                String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
                localStoreIds.add(localstoreId);
                localMerchantIds.add(localMerchantId);
            }
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        } else if (!StringUtil.empty(merchantUserId)) {
            //原先接口最多要999个 替换接口
            int startPage = 1;
            int maxSize = 100;
            int totalLimit = 999;
            List<Map> records = null;
            do {
                StoreAuthsReq storeAuthsReq = new StoreAuthsReq();
                storeAuthsReq.setPage(startPage).setPageSize(maxSize).setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
                storeAuthsReq.setMerchant_user_id(merchantUserId);
                com.wosai.web.api.ListResult<Map> localStores = merchantUserServiceV2.getAuthStores(storeAuthsReq);
                records = localStores.getRecords();
                if (!CollectionUtils.isEmpty(records)) {
                    for (Map localStore : records) {
                        Map<String, Object> store = storeService.getStore(BeanUtil.getPropString(localStore, DaoConstants.ID));
                        if (store == null) {
                            continue;
                        }
                        String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
                        localStoreIds.add(BeanUtil.getPropString(localStore, DaoConstants.ID));
                        localMerchantIds.add(localMerchantId);
                    }
                }
                startPage++;
            } while (records != null && records.size() == maxSize && allStoreIds.size() < totalLimit);
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        } else if (!StringUtil.empty(storeId)) {
            Map<String, Object> store = storeService.getStore(storeId);
            if (store == null) {
                return ListResult.emptyListResult();
            }
            String localStoreId = BeanUtil.getPropString(store, DaoConstants.ID);
            String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
            allStoreIds.add(Arrays.asList(localStoreId));
            allMerchantIds.add(Arrays.asList(localMerchantId));
        }

        if (merchantIds != null && merchantIds instanceof List) {
            allMerchantIds.add(merchantIds);
        }
        if (!StringUtil.empty(merchantId)) {
            allMerchantIds.add(Arrays.asList(merchantId));
        }

        TransactionHBaseQuery transactionHbaseQuery = new TransactionHBaseQuery();

        transactionHbaseQuery.setSolrTimeout(solrTimeout);
        transactionHbaseQuery.sethBaseTimeout(hbaseTimeout);
        if (allTerminalIds.size() > 0) {
            Set<String> allSameTerminalIds = CommonUtil.getAllSameElementInLists(allTerminalIds);
            if (allSameTerminalIds.size() == 0) {
                return ListResult.emptyListResult();
            }

            transactionHbaseQuery.setTerminals(new ArrayList<>(allSameTerminalIds));
        }
        Set<String> allSameStoreIds;
        if (allStoreIds.size() > 0) {
            allSameStoreIds = CommonUtil.getAllSameElementInLists(allStoreIds);
            if (allSameStoreIds.size() == 0) {
                return ListResult.emptyListResult();
            }

            transactionHbaseQuery.setStoreIds(new ArrayList<>(allSameStoreIds));
        }
        if (allMerchantIds.size() > 0) {
            Set<String> allSameMerchantIds = CommonUtil.getAllSameElementInLists(allMerchantIds);
            if (allSameMerchantIds.size() == 0) {
                return ListResult.emptyListResult();
            }

            transactionHbaseQuery.setMerchantIds(new ArrayList<>(allSameMerchantIds));
        }

        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();

        if(!CollectionUtils.isEmpty(types)){
            statusTypeSubPayWayQuery.setTypeList(types);
        }

        if (status != null) {
            statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(status.intValue()));
        }

        transactionHbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS
                , statusTypeSubPayWayQuery);

        if(DBSelectContext.getContext().getFinishTimeQuery()){
            transactionHbaseQuery.setChannelFinishStartTime(pageInfo.getDateStart());
            transactionHbaseQuery.setChannelFinishEndTime(pageInfo.getDateEnd());
        }else{
            transactionHbaseQuery.setStartTime(pageInfo.getDateStart());
            transactionHbaseQuery.setEndTime(pageInfo.getDateEnd());
        }

        long totalCount = 0;
        if (seleceCount) {
            //统计总条数
            totalCount = transactionHbaseDao.count(transactionHbaseQuery);
        }

        transactionHbaseQuery.getOrderBys().addAll(pageInfo.getOrderBy());

        int limit = pageInfo.getPageSize();
        int offset = (pageInfo.getPage() - 1) * limit;
        transactionHbaseQuery.setLimit(limit);
        transactionHbaseQuery.setOffset(offset);

        List list = transactionHbaseDao.queryList(transactionHbaseQuery);

        List<String> addInfo = Lists.newArrayList(
                QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
        );

        if (!StringUtil.empty(includes) && includes.contains(StatementTaskLog.INCLUDE_CASH_DESK + "")) {
            addInfo.add(QueryFlagConstant.ORDER_DETAILS_ADD_CASH_DESK_INFO);
        }
        if (includeBrand) {
            addInfo.add(QueryFlagConstant.ORDER_DETAILS_ADD_BRAND_INFO);
        }
        formatTransactions(list, addInfo);
        return new ListResult(totalCount, list);
    }

    //-------------------------流水详情-------------------------
    private void formatTransactions(Object transactions, List queryFlags) {
        List<Map<String, Object>> list = new LinkedList();
        Map<String, Object> localReceiverCache = null;
        if (transactions instanceof List) {
            list = (List) transactions;
            localReceiverCache = new HashMap<>();
        } else if (transactions instanceof Map) {
            list.add((Map<String, Object>) transactions);
        } else {
            return;
        }
        businessService.addBusinessInfoWithQueryFlag(transactions, queryFlags);
        for (Map<String, Object> transaction : list) {
            TransactionUtil.transformReflect(transaction);
            TransactionUtil.jsonFormatOrder(transaction);
            TransactionUtil.expandTransactionInfo(transaction);
            TransactionUtil.expandTransactionItemsPayments(transaction, false);
            TransactionUtil.expandTransactionItemTradeInfo(transaction);
            TransactionUtil.calculateExtendFields(transaction);
            TransactionUtils.expandMchDiscountOriginType(transaction);
            TransactionUtil.expandSharingBooks(transaction, businessService, localReceiverCache);
        }
    }

}

