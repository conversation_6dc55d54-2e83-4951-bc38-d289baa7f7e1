package com.wosai.upay.transaction.util;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

public class PartitionConcurrentLinkedQueue<T> extends ConcurrentLinkedQueue<T> {

    public List<List<T>> partition(int size) {
        List<T> result = new ArrayList(5000);
        T t = null;
        while((t = poll()) != null) {
            result.add(t);
        }
        if(result.size() != 0){
           return Lists.partition(result, size);
        }
        return null;
    }

    public List<T> getBatch() {
        List<T> result = new ArrayList(5000);
        T t = null;
        while((t = poll()) != null) {
            result.add(t);
        }
        return result;
    }
}