package com.wosai.upay.transaction.service.api;

import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.service.CashDeskService;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.TransactionGroupByKey;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.model.*;
import com.wosai.upay.transaction.model.param.AccountRecordParam;
import com.wosai.upay.transaction.service.IAccountBookService;
import com.wosai.upay.transaction.service.IAccountBookServiceOpen;
import com.wosai.upay.transaction.service.service.client.IAccountStoreService;
import com.wosai.upay.transaction.util.CommonUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
public class AccountBookServiceOpenImpl implements IAccountBookServiceOpen {

    public static final Logger logger = LoggerFactory.getLogger(AccountBookServiceOpenImpl.class);

    @Autowired
    private IAccountBookService accountBookService;

    @Autowired
    private IAccountStoreService accountStoreService;

    @Resource
    private CashDeskService cashDeskService;

    @Override
    public TResultData<List<TAccountRecordDay>> getMoreAccountBookRecords(Map<String, Object> param) {
        return accountBookService.getMoreAccountBookRecords(wrapperParam(param));
    }

    @Override
    public TAccountSumV getAccountSum(Map<String, Object> param) {
        return wrapperTAccountSumV(param, accountBookService.getAccountSum(wrapperParam(param)));
    }

    @Override
    public List<TAccountSumV> getAccountSumGroupBy(Map<String, Object> param) {
        List<TAccountSumV> tAccountSumVS = Lists.newArrayList();
        param.put(CommonConstant.GROUP_BYS, TransactionGroupByKey.payWay.name());//支付源分组汇总
        AccountRecordParam accountRecordParam = wrapperParam(param);
        List<TAccountSumV> vs = Lists.newArrayList();

        int upayQueryType = BeanUtil.getPropInt(param, CommonConstant.UPAY_QUERY_TYPE);

        if (upayQueryType == UpayQueryType.UPAY.getCode() || upayQueryType == UpayQueryType.UPAY_ALL.getCode()) {
            accountRecordParam.setUpayQueryType(UpayQueryType.UPAY.getCode());
            vs.addAll(accountBookService.getAccountSumGroupBy(accountRecordParam));
        }

        if (upayQueryType == UpayQueryType.UPAY_SWIPE.getCode() || upayQueryType == UpayQueryType.UPAY_ALL.getCode()) {
            accountRecordParam.setUpayQueryType(UpayQueryType.UPAY_SWIPE.getCode());
            vs.addAll(accountBookService.getAccountSumGroupBy(accountRecordParam));
        }

        TAccountSumV sumV = new TAccountSumV();
        TAccountSumV tempSumV;
        for (TAccountSumV tAccountSumV : vs) {
            tempSumV = wrapperTAccountSumV(param, tAccountSumV);
            tAccountSumVS.add(wrapperTAccountSumV(param, tAccountSumV));
            sumV.setDepositAmount(sumV.getDepositAmount() + tempSumV.getDepositAmount());
            sumV.setDepositCount(sumV.getDepositCount() + tempSumV.getDepositCount());
            sumV.setSalesCount(sumV.getSalesCount() + tempSumV.getSalesCount());
            sumV.setSalesAmount(sumV.getSalesAmount() + tempSumV.getSalesAmount());
            sumV.setRefundedCount(sumV.getRefundedCount() + tempSumV.getRefundedCount());
            sumV.setRefundedAmount(sumV.getRefundedAmount() + tempSumV.getRefundedAmount());
            sumV.setPaidAmount(sumV.getPaidAmount() + tempSumV.getPaidAmount());
            sumV.setPaidCount(sumV.getPaidCount() + tempSumV.getPaidCount());
            sumV.setCanceldCount(sumV.getCanceldCount() + tempSumV.getCanceldCount());
            sumV.setCanceldAmount(sumV.getCanceldAmount() + tempSumV.getCanceldAmount());
            sumV.setDepositCancelAmount(sumV.getDepositCancelAmount() + tempSumV.getDepositCancelAmount());
            sumV.setDepositCancelCount(sumV.getDepositCancelCount() + tempSumV.getDepositCancelCount());
        }
        tAccountSumVS.add(sumV);
        return tAccountSumVS;
    }

    private TAccountSumV wrapperTAccountSumV(Map<String, Object> param, TAccountSumV accountSumV) {
        accountSumV.setStoreId(BeanUtil.getPropString(param, CommonConstant.STORE_ID));
        accountSumV.setStoreName(BeanUtil.getPropString(param, CommonConstant.STORE_NAME));
        return accountSumV;
    }

    @Override
    public TAccountRecordDetail getRecordForDetail(Map<String, Object> param) {
        String transactionSn = BeanUtil.getPropString(param, CommonConstant.TRANSACTION_SN);
        String terminalSn = BeanUtil.getPropString(param, CommonConstant.TERMINAL_SN);
        String storeId = BeanUtil.getPropString(param, CommonConstant.STORE_ID);
        String merchantId = "";
        if (!StringUtils.isEmpty(storeId)) {
            Map store = accountStoreService.findStore(storeId);
            if (!CollectionUtils.isEmpty(store)) {
                merchantId = BeanUtil.getPropString(store, Transaction.MERCHANT_ID);
            }
        } else {
            Map terminal = accountStoreService.findTerminalByTerminalSn(terminalSn);
            if (!CollectionUtils.isEmpty(terminal)) {
                merchantId = BeanUtil.getPropString(terminal, CommonConstant.MERCHANT_ID);
            }
        }
        return accountBookService.getRecordForDetail(transactionSn, merchantId);
    }

    private AccountRecordParam wrapperParam(Map<String, Object> param) {
        String storeId = BeanUtil.getPropString(param, CommonConstant.STORE_ID);
        String terminalSn = BeanUtil.getPropString(param, CommonConstant.TERMINAL_SN);
        if (!StringUtils.isEmpty(storeId)) {
            Map store = accountStoreService.findStore(storeId);
            if (!CollectionUtils.isEmpty(store)) {
                String merchantId = BeanUtil.getPropString(store, Transaction.MERCHANT_ID);
                String storeName = BeanUtil.getPropString(store, CommonConstant.NAME);
                param.put(Transaction.MERCHANT_ID, merchantId);
                param.put(CommonConstant.STORE_IDS, storeId);
                param.put(CommonConstant.STORE_NAME, storeName);
            }
        } else {
            Map terminal = accountStoreService.findTerminalByTerminalSn(terminalSn);
            if (!CollectionUtils.isEmpty(terminal)) {
                String storeName = BeanUtil.getPropString(terminal, CommonConstant.STORE_NAME);
                storeId = BeanUtil.getPropString(terminal, CommonConstant.STORE_ID);
                String merchantId = BeanUtil.getPropString(terminal, CommonConstant.MERCHANT_ID);
                String terminalId = BeanUtil.getPropString(terminal, CommonConstant.TERMINAL_ID);
                param.put(Transaction.MERCHANT_ID, merchantId);
                param.put(CommonConstant.STORE_IDS, storeId);
                param.put(CommonConstant.STORE_NAME, storeName);
                param.put(CommonConstant.TERMINALS, terminalId);
            }
        }
        AccountRecordParam accountRecordParam = JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(param), AccountRecordParam.class);
        accountRecordParam.setToken(CommonConstant.OUTSIDE_CALL);
        accountRecordParam.setValidTimeSpan(false);
        List<String> terminals = CommonUtil.splitToListString(accountRecordParam.getTerminals());
        //终端账本需要查对应收银台的交易
        if (accountRecordParam.getUpayQueryType() == UpayQueryType.UPAY.getCode() 
                && !accountRecordParam.getIsStoreAccount() 
                && terminals.size() == 1
                && MapUtil.getBooleanValue(param, CommonConstant.ACCESS_CASH_DESK, false)) {
            String terminalId = terminals.get(0);
            Map<String, Object> cashDeskInfo = cashDeskService.getSimpleCashDeskByMerchantIdAndDeviceId(accountRecordParam.getMerchant_id(), terminalId);
            if (!CollectionUtils.isEmpty(cashDeskInfo)) {
                accountRecordParam.setCashDesk(MapUtils.getString(cashDeskInfo, DaoConstants.ID, Strings.EMPTY));
                accountRecordParam.setTerminals(null);
            }
        }
        return accountRecordParam;
    }
}
