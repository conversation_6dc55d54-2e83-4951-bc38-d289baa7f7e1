package com.wosai.upay.transaction.service.service.client.impl;

import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.transaction.service.service.client.IAsyncStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@Service
public class AsyncStoreServiceImpl implements IAsyncStoreService {

    @Autowired
    public StoreService storeService;

    @Async
    @Override
    public Future<Map> getStoreByStoreSn(String storeSn, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<Map>(storeService.getStoreByStoreSn(storeSn));
    }


    @Async
    @Override
    public Future<Map> getStore(String storeId, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<Map>(storeService.getStore(storeId));
    }
}
