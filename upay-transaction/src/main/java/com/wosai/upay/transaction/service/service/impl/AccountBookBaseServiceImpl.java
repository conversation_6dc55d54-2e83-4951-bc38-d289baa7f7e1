package com.wosai.upay.transaction.service.service.impl;


import com.google.common.collect.Maps;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.transaction.TransactionEnhanceFields;
import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.upay.ProfitSharing;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.upay.core.bean.request.ChangeShiftsCashierQueryInfo;
import com.wosai.upay.core.bean.request.ChangeShiftsCashierQueryRequest;
import com.wosai.upay.core.bean.response.ChangeShiftsCashierQueryResponse;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.ChangeShiftsService;
import com.wosai.upay.transaction.constant.BlendingStatusConst;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DateTimeConst;
import com.wosai.upay.transaction.constant.TransactionConstant;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.enums.LoadType;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.dao.hbase.OrderHBaseDao;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionDaySumV;
import com.wosai.upay.transaction.service.model.vo.TransactionSumV;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;
import com.wosai.upay.transaction.service.service.IAccountBookBaseService;
import com.wosai.upay.transaction.service.service.client.IAccountStoreService;
import com.wosai.upay.transaction.service.service.common.BuyerCommonService;
import com.wosai.upay.transaction.service.service.common.CashDeskInfoService;
import com.wosai.upay.transaction.service.service.common.OperatorService;
import com.wosai.upay.transaction.util.*;
import joptsimple.internal.Strings;
import org.apache.commons.collections.MapUtils;
import org.apache.hadoop.hbase.util.Bytes;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.FutureTask;

import static com.wosai.upay.core.model.TransactionParam.LIQUIDATION_NEXT_DAY;
import static com.wosai.upay.core.model.TransactionParam.PROVIDER_MCH_ID;
import static com.wosai.upay.transaction.model.Transaction.*;
import static java.util.stream.Collectors.toList;

@Service
public class AccountBookBaseServiceImpl implements IAccountBookBaseService {

    public static final Logger logger = LoggerFactory.getLogger(AccountBookBaseServiceImpl.class);

    @Autowired
    private OperatorService operatorService;

    @Autowired
    private CashDeskInfoService cashDeskInfoService;

    @Autowired
    private BuyerCommonService buyerCommonService;

    @Autowired
    private IAccountStoreService accountStoreService;

    @Autowired
    private TransactionHBaseDao transactionHbaseDao;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ChangeShiftsService changeShiftsService;

    @Autowired
    private OrderHBaseDao orderHBaseDao;

    public static final String TRADE_APP_BASIC_PAY = "1";               // 基础支付业务

    @Override
    public List<TransactionVo> queryList(TransactionQuery query) {
        FutureTask<Map<String, String>> storeStaffTask = businessService.getStoreStaffFutureTask(query);
        List<Map<String, Object>> transactionList = transactionHbaseDao.queryList(HbaseQueryConverter.convert(query));
        if (!CollectionUtils.isEmpty(transactionList)) {
            CarrierItem item = new CarrierItem(TraceContext.traceId());
            final Map<String, String> storeStaffMap = Maps.newHashMap();
            businessService.getStoreStaff(query, storeStaffTask, storeStaffMap);
            List<TransactionVo> transactionVos = new ArrayList<>(transactionList.size());
            List<FutureTask<TransactionVo>> asyncTaskList = new ArrayList<>(transactionList.size());
            FutureTask<TransactionVo> futureTask;
            for (Map<String, Object> t : transactionList) {
                futureTask = new FutureTask<>(new BaseAsyncTask<TransactionVo>() {
                    @Override
                    public TransactionVo run() {
                        TracingUtil.storeThreadLocalTraceInfo(item);
                        Map<Integer, Object> payWayNames = businessService.getAllPayWayName();
                        TransactionVo transactionVo = buildTransactionVo(t, query.getLoadTypes(), payWayNames, false);
                        if (query.getLoadTypes().contains(LoadType.TERMINAL_CODE) && query.getLoadTypes().contains(LoadType.STORE_STAFF)) {
                            operatorService.setOperator(transactionVo, storeStaffMap);
                        }
                        return transactionVo;

                    }
                });
                ExecutorServiceSupport.getDefaultExecutorService().submit(futureTask);
                asyncTaskList.add(futureTask);
            }

            for (FutureTask<TransactionVo> voFutureTask : asyncTaskList) {
                try {
                    transactionVos.add(voFutureTask.get());
                } catch (Exception e) {
                    logger.error("转换TransactionVo出错", e);
                    throw new RuntimeException(e);
                }
            }
            return transactionVos;
        }
        return Collections.EMPTY_LIST;
    }

    @Override
    public Map<String, TransactionDaySumV> summaryByDay(TransactionQuery query, Set<String> days) {

        Map<String, TransactionDaySumV> dayTransactionDaySumVMap = Maps.newConcurrentMap();
        if (CollectionUtils.isEmpty(days)) {
            return dayTransactionDaySumVMap;
        }

        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = query.getStatusTypeSubPayWayQueries().get(CommonStatus.ERROR);
        //错误状态不查询
        if (!Objects.isNull(statusTypeSubPayWayQuery)) {
            statusTypeSubPayWayQuery.setValid(false);
        }
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuerySuccess = query.getStatusTypeSubPayWayQueries().get(CommonStatus.SUCCESS);
        if (!Objects.isNull(statusTypeSubPayWayQuerySuccess)) {
            if(!statusTypeSubPayWayQuerySuccess.isValid()) {
                return dayTransactionDaySumVMap; 
            }
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuerySuccess.getTypeList())) {
                statusTypeSubPayWayQuerySuccess.getTypeList().removeAll(CommonConstant.NOT_SUMMARY_TRANSACTION_TYPES);
                if (CollectionUtils.isEmpty(statusTypeSubPayWayQuerySuccess.getTypeList())) {
                    return dayTransactionDaySumVMap;
                }
            }
        }
        CarrierItem item = new CarrierItem(TraceContext.traceId());
        List<FutureTask<Void>> futureTaskList = new ArrayList<>(days.size());
        FutureTask<Void> futureTaskTemp;
        for (String d : days) {
            if (days.size() > 1) {
                futureTaskTemp = new FutureTask<>(new BaseAsyncTask<Void>() {
                    @Override
                    public Void run() {
                        TracingUtil.storeThreadLocalTraceInfo(item);
                        summaryByDayRealize(d, query, dayTransactionDaySumVMap);
                        return null;
                    }
                });
                futureTaskList.add(futureTaskTemp);
                ExecutorServiceSupport.getDefaultExecutorService().submit(futureTaskTemp);
            } else {
                summaryByDayRealize(d, query, dayTransactionDaySumVMap);
                break;
            }
        }
        if (!CollectionUtils.isEmpty(futureTaskList)) {
            for (FutureTask<Void> futureTask : futureTaskList) {
                try {
                    futureTask.get();
                } catch (Exception e) {
                    logger.error("账本按天汇总出错", e);
                    throw new RuntimeException(e);
                }
            }
        }
        return dayTransactionDaySumVMap;
    }

    private void summaryByDayRealize(String d, TransactionQuery query, Map<String, TransactionDaySumV> dayTransactionDaySumVMap) {
        DateTime dateTime = DateTime.parse(d);
        TransactionQuery queryTemp = query.clone();
        queryTemp.setStartTime(dateTime.withTimeAtStartOfDay().getMillis() + query.getOffsetHour() * CommonConstant.MILLISECOND_OF_HOUR);
        queryTemp.setEndTime(dateTime.plusDays(1).withTimeAtStartOfDay().getMillis() + query.getOffsetHour() * CommonConstant.MILLISECOND_OF_HOUR);

        // 汇总语法已指定type，此处就不进行type排除，如果汇总语法有调整，需要将下面的代码移除(减少lindorm查询时的资源使用)
        if (queryTemp.getStatusTypeSubPayWayQueries() != null 
                && query.getStatusTypeSubPayWayQueries().get(CommonStatus.SUCCESS) != null
                && query.getStatusTypeSubPayWayQueries().get(CommonStatus.SUCCESS).getNotTypeList() != null
                ) {
            query.getStatusTypeSubPayWayQueries().get(CommonStatus.SUCCESS).setNotTypeList(null);
        }
        TransactionSummaryPo po = transactionHbaseDao.summaryTx(HbaseQueryConverter.convert(queryTemp));
        if (!TransactionSummaryPo.isEmpty(po)) {
            TransactionSumV sumV = po.buildSumV();
            dayTransactionDaySumVMap.put(d, new TransactionDaySumV(d, sumV.getSalesCount(), sumV.getSalesAmount(),sumV.getStoreInTotalCount(),sumV.getStoreInTotalAmount()));
        }
    }


    @Override
    public List<TransactionSumV> summary(TransactionQuery queryParam) {
        //克隆
        TransactionQuery query = queryParam.clone();
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = query.getStatusTypeSubPayWayQueries().get(CommonStatus.ERROR);
        //错误状态不查询
        if (Objects.nonNull(statusTypeSubPayWayQuery)) {
            statusTypeSubPayWayQuery.setValid(false);
        }
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuerySuccess = query.getStatusTypeSubPayWayQueries().get(CommonStatus.SUCCESS);
        if (Objects.nonNull(statusTypeSubPayWayQuerySuccess)) {
            if (!statusTypeSubPayWayQuerySuccess.isValid()) {
                return Collections.emptyList();
            }
            // 汇总中不包含预授权冻结和预授权撤销
            if (!CollectionUtils.isEmpty(statusTypeSubPayWayQuerySuccess.getTypeList())) {
                statusTypeSubPayWayQuerySuccess.getTypeList().removeAll(CommonConstant.NOT_SUMMARY_TRANSACTION_TYPES);
                if(statusTypeSubPayWayQuerySuccess.getTypeList().isEmpty()) {
                    return Collections.emptyList();
                }
            } else {
                // 没有指定type查询，需要在not中排除
                /*
                List<Integer> notTypeList = statusTypeSubPayWayQuerySuccess.getNotTypeList();
                if(notTypeList == null) {
                    notTypeList = new ArrayList<Integer>();
                    statusTypeSubPayWayQuerySuccess.setNotTypeList(notTypeList);
                }
                notTypeList.addAll(CommonConstant.NOT_SUMMARY_TRANSACTION_TYPES);
                */
                // 汇总语法已指定type，此处就不进行type排除，如果汇总语法有调整，需要替换为上面的语法(减少lindorm查询时的资源使用)
                statusTypeSubPayWayQuerySuccess.setNotTypeList(null);
            }
        }
        query.setCreatedAtEnd(query.getBackUpTime());
        query.getLoadTypes().clear();
        List<TransactionSummaryPo> summaryPoList;
        if (CollectionUtils.isEmpty(query.getGroupByKeys())) {
            summaryPoList = CommonUtil.newArrayList(transactionHbaseDao.summaryTx(HbaseQueryConverter.convert(query)));
        } else {
            summaryPoList = transactionHbaseDao.summaryTxGroupByKey(HbaseQueryConverter.convert(query));
        }
        return summaryPoList.stream().map(sp -> sp.buildSumV(queryParam.getSummaryExts())).collect(toList());
    }

    @Override
    public TransactionVo queryObj(TransactionQuery query) {
        Map<String, Object> transactionDetail;

        // 预热数据
        FutureTask<Map<String, String>> storeStaffTask = businessService.getStoreStaffFutureTask(query);

        // 优先根据 id 查询
        if (StringUtils.hasLength(query.getTransactionId()) && StringUtils.hasLength(query.getMerchantId())
                && query.getStartTime() != null) {
            transactionDetail = this.queryOneByTid(query.getMerchantId(), query.getStartTime(), query.getTransactionId(), query.getSimple());
        } else {
            // 否则走原来的查询方式
            transactionDetail = this.queryOne(query);
        }

        if (transactionDetail == null) {
            return null;
        }

        // 填充下其他数据
        final Map<String, String> storeStaffMap = Maps.newHashMap();
        businessService.getStoreStaff(query, storeStaffTask, storeStaffMap);
        Map<Integer, Object> payWayNames = businessService.getAllPayWayName();
        TransactionVo transactionVo = buildTransactionVo(transactionDetail, query.getLoadTypes(), payWayNames, true);
        if (query.getLoadTypes().contains(LoadType.TERMINAL_CODE) && query.getLoadTypes().contains(LoadType.STORE_STAFF)) {
            operatorService.setOperatorAndTerminalType(transactionVo, storeStaffMap);
        }
        if (query.getLoadTypes().contains(LoadType.CASH_DESK_NAME)) {
            cashDeskInfoService.setCashDeskInfo(transactionVo);
        }
        if (query.getLoadTypes().contains(LoadType.CASHIER_NAME)) {
            // 添加收银员信息
            ChangeShiftsCashierQueryRequest request = new ChangeShiftsCashierQueryRequest();
            request.setCashierQueryInfos(Arrays.asList(new ChangeShiftsCashierQueryInfo(transactionVo.getCtime(), transactionVo.getMerchantId(), transactionVo.getTerminalId(), transactionVo.getCashDeskId())));
            ChangeShiftsCashierQueryResponse cashierQueryResponse = changeShiftsService.getChangeShiftsCashier(request);
            if (cashierQueryResponse != null 
                    && cashierQueryResponse.getChangeShiftsInfos() != null 
                    && !cashierQueryResponse.getChangeShiftsInfos().isEmpty()
                    && cashierQueryResponse.getChangeShiftsInfos().get(0) != null) {
                transactionVo.setCashierName(cashierQueryResponse.getChangeShiftsInfos().get(0).getCashierName());
            }
        }
        return transactionVo;
    }



    /**
     * 主要根据 id、merchantId、ctime 直查 HBase
     */
    @Override
    public Map<String, Object> queryOneByTid(@NotNull String merchantId, @NotNull Long ctime, @NotNull String tid, Boolean isSimple) {
        return doQueryOneByTid(merchantId, ctime, tid, isSimple, false);
    }


    /**
     * 查询全部字段列
     *
     * @param merchantId
     * @param ctime
     * @param tid
     * @return
     */
    @Override
    public Map<String, Object> queryOneByTid(String merchantId, Long ctime, String tid) {
        return doQueryOneByTid(merchantId, ctime, tid, false, true);
    }

    /**
     * 按照原来的方式查
     */
    private Map<String, Object> queryOne(TransactionQuery query) {
        query.setLimit(1);
        // 将查询Solr的范围尽量缩小在一个Collection内
        if (query.getStartTime() != null && query.getEndTime() == null) {
            query.setEndTime(query.getStartTime() + 1);
        }
        List<Map<String, Object>> transactionList = transactionHbaseDao.queryList(HbaseQueryConverter.convert(query));

        return CollectionUtils.isEmpty(transactionList) ? null : transactionList.get(0);
    }

    public TransactionVo buildTransactionVo(Map<String, Object> map, Set<LoadType> loadTypeSet, Map<Integer, Object> payWayNames, Boolean needCustomerName) {
        TransactionVo vo = new TransactionVo();
        vo.setEffectiveAmount(BeanUtil.getPropLong(map, EFFECTIVE_AMOUNT));
        vo.setOriginalAmount(BeanUtil.getPropLong(map, ORIGINAL_AMOUNT));
        vo.setPaidAmount((Long) map.get(PAID_AMOUNT));
        vo.setReceivedAmount(BeanUtil.getPropLong(map, RECEIVED_AMOUNT));
        vo.setTsn(BeanUtil.getPropString(map, TSN));
        vo.setId(BeanUtil.getPropString(map, ID));
        vo.setStoreId(BeanUtil.getPropString(map, TransactionParam.STORE_ID));
        vo.setStatus(BeanUtil.getPropInt(map, STATUS));
        vo.setType(BeanUtil.getPropInt(map, TYPE));
        String merchantId = BeanUtil.getPropString(map, TransactionParam.MERCHANT_ID);
        vo.setMerchantId(merchantId);
        vo.setOperator(BeanUtil.getPropString(map, OPERATOR));
        vo.setOperatorOrigin(BeanUtil.getPropString(map, OPERATOR));
        int payWay = BeanUtil.getPropInt(map, PAYWAY);
        vo.setPayWay(payWay);
        vo.setTradeNo(BeanUtil.getPropString(map, TRADE_NO));
        vo.setSubPayWay(BeanUtil.getPropInt(map, SUB_PAYWAY));
        vo.setFinishTime(BeanUtil.getPropLong(map, FINISH_TIME));
        vo.setCtime(BeanUtil.getPropLong(map, DaoConstants.CTIME));
        vo.setOrderSn(BeanUtil.getPropString(map, ORDER_SN));
        int provider = BeanUtil.getPropInt(map, PROVIDER);
        vo.setProvider(provider > 0 ? provider : null);
        vo.setTerminalId(BeanUtil.getPropString(map, TransactionParam.TERMINAL_ID));
        String buyerUid = BeanUtil.getPropString(map, BUYER_UID);
        vo.setPayId(buyerUid);
//        Map<String, Object> buyerInfo = buyerCommonService.getBuyerInfo(buyerUid, payWay);
//        if (MapUtils.isNotEmpty(buyerInfo)) {
//            vo.setBuyerName(MapUtils.getString(buyerInfo, CommonConstant.BUYER_NAME));
//            vo.setBuyerIcon(MapUtils.getString(buyerInfo, CommonConstant.BUYER_ICON));
//        }
        vo.setProductFlag(BeanUtil.getPropString(map, PRODUCT_FLAG));

        //通用开关
        String commonSwitch = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + TransactionParam.COMMON_SWITCH);
        if (StringUtils.hasText(commonSwitch)) {
            vo.setCommonSwitch(commonSwitch);
        }

        if (!loadTypeSet.contains(LoadType.CONFIG_SNAPSHOT)) {
            map.remove(Transaction.CONFIG_SNAPSHOT);
        }

        if (!loadTypeSet.contains(LoadType.PROVIDER_ERROR_INFO)) {
            map.remove(Transaction.PROVIDER_ERROR_INFO);
        }

        if (!loadTypeSet.contains(LoadType.REFLECT)) {
            map.remove(Transaction.REFLECT);
        }

        if (!loadTypeSet.contains(LoadType.ITEMS)) {
            map.remove(Transaction.ITEMS);
        }

        //是否为勾兑成功
        boolean isFix = MapUtil.getBooleanValue(MapUtil.getMap(map, Transaction.EXTRA_OUT_FIELDS), Transaction.IS_FIX, false);
        vo.setFix(isFix);

        if (!loadTypeSet.contains(LoadType.EXTRA_OUT_FIELDS)) {
            map.remove(Transaction.EXTRA_OUT_FIELDS);
        }

        TransactionUtil.jsonFormatOrder(map);
        if (!StringUtils.hasText(vo.getPayId())) {
            String payerUid = (String) BeanUtil.getNestedProperty(map, Transaction.EXTRA_PARAMS + "." + Transaction.PAYER_UID);
            vo.setPayId(payerUid);
        }
        String terminalSn = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + TransactionParam.TERMINAL_SN);
        String terminalName = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + Transaction.TERMINAL_NAME);
        String vendorAppAppid = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + Transaction.TERMINAL_VENDOR_APP_APPID);
        String tradeApp = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + Transaction.TRADE_APP);
        if (StringUtils.hasText(terminalName)) {
            if (terminalName.startsWith("久久折")) {
                int start = terminalName.indexOf("（");
                int end = terminalName.indexOf("）");
                if (start > 0 && end > 0) {
                    terminalName = terminalName.substring(start + 1, end) + "的智慧门店小程序";
                }
            }
            if (terminalName.endsWith("收款音箱")) {
                terminalName = terminalName.replace("收款音箱", "收钱音箱");
            }
        }

        String merchantName = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + Transaction.MERCHANT_NAME);
        String storeName = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + Transaction.STORE_NAME);
        String storeSn = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + Transaction.STORE_SN);
        String storeId = (String) BeanUtil.getNestedProperty(map, Transaction.CONFIG_SNAPSHOT + "." + Transaction.STORE_ID);
        String authNo = (String) BeanUtil.getNestedProperty(map, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.AUTH_NO);
        String sysTraceNo = (String) BeanUtil.getNestedProperty(map, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.SYS_TRACE_NO);
        String batchBillNo = (String) BeanUtil.getNestedProperty(map, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.BATCH_BILL_NO);
        String refernumber = (String) BeanUtil.getNestedProperty(map, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.REFER_NUMBER);
        Map<String, Object> orderInfo = (Map<String, Object>) BeanUtil.getNestedProperty(map, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.ORDER_INFO);

        vo.setBatchBillNo(batchBillNo);
        vo.setSysTraceNo(sysTraceNo);
        vo.setAuthNo(authNo);
        vo.setRefernumber(refernumber);
        vo.setOrderInfo(orderInfo);
        //订单详情返回weixin_sub_appid
        String subAppid = Strings.EMPTY;
        if (vo.getPayWay() == Order.PAYWAY_WEIXIN) {
            Map extendedParams = MapUtil.getMap(map, EXTENDED_PARAMS);
            Map configSnapshot = MapUtil.getMap(map, CONFIG_SNAPSHOT);
            if (MapUtil.isNotEmpty(extendedParams) && extendedParams.containsKey(Transaction.SUB_APPID)) {
                //先从extended_params中获取上送的sub_appid
                subAppid = MapUtil.getString(extendedParams, Transaction.SUB_APPID);
            } else {
                //再从config_snapshot中获取
                int subPayWay = vo.getSubPayWay();
                Map<String, Object> tradeInfo = TransactionUtil.getTradeConfigInfoByConfigSnapshot(configSnapshot);
                subAppid = MapUtil.getString(tradeInfo, TransactionParam.WEIXIN_SUB_APP_ID);
                if (Order.SUB_PAYWAY_MINI == subPayWay && !StringUtils.isEmpty(MapUtils.getString(tradeInfo, TransactionParam.WEIXIN_MINI_SUB_APP_ID))) {
                    subAppid = MapUtils.getString(tradeInfo, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
                }
            }
            vo.setSubAppid(subAppid);
        }
        //获取付款人名称
        // 1、默认付款人名称为 xx用户， xx为支付源名称
        // 2、若为支付宝和云闪付交易， BUYER_LOGIN 分别存的是支付宝用户名和云闪付返回的name字段。若BUYER_LOGIN不为空是，付款人名称为BUYER_LOGIN
        // 3、若为微信交易，若该openid关注过我们的公众号，则显示昵称
        String payerAccount = MapUtil.getString(payWayNames, payWay, Strings.EMPTY).replace("记账", Strings.EMPTY) + "用户";
        if ((Order.PAYWAY_ALIPAY == payWay || Order.PAYWAY_ALIPAY2 == payWay || Order.PAYWAY_LKL_UNIONPAY == payWay) && !StringUtils.isEmpty(MapUtils.getString(map, BUYER_LOGIN))) {
            payerAccount = BeanUtil.getPropString(map, BUYER_LOGIN);
        }
        if (Boolean.TRUE.equals(needCustomerName)) {
            String customerName = accountStoreService.findPayerAccount(merchantId, storeId, buyerUid, payWay, subAppid);
            if (!StringUtils.isEmpty(customerName) && !CommonConstant.DEFAULT_CUSTOMER_NAME.equals(customerName)) {
                payerAccount = customerName;
            }
        }
        vo.setPayAccount(payerAccount);

        TransactionUtil.expandTransactionItemsPayments(map, false);
        TransactionUtil.expandTransactionItemTradeInfo(map);
        TransactionUtil.calculateExtendFields(map);
        vo.setLiquidationNextDay(BeanUtil.getPropBoolean(map, LIQUIDATION_NEXT_DAY));
        vo.setReflect(BeanUtil.getPropString(map, REFLECT));
        String bankTradeNo = BeanUtil.getPropString(map, BANK_TRADE_NO);
        //银行单号
        vo.setBankTradeNo(StringUtils.hasLength(bankTradeNo) ? bankTradeNo : vo.getTradeNo());
        String channelTradeNo = BeanUtil.getPropString(map, EXTRA_CHANNEL_TRADE_NO);
        //收单机构订单号
        vo.setChannelTradeNo(channelTradeNo);
        // 收单机构商户号
        vo.setProviderMchId(MapUtil.getString(map, PROVIDER_MCH_ID));
        //判断是否口碑商户用  不为空为口碑
        vo.setAppAuthShopId(BeanUtil.getPropString(map, Transaction.APP_AUTH_SHOP_ID, ""));
        //判断是否是花呗交易
        //老版本兼容
        setHuabeiInfo(vo, map);
        //新
        Map productFlagMap = TransactionUtils.enhanceProductFlag(BeanUtil.getPropString(map, Transaction.PRODUCT_FLAG, ""));
        if (!vo.getInstalment()) {
            vo.setInstalment(BeanUtil.getPropBoolean(productFlagMap, TransactionEnhanceFields.IS_HUA_BEI_FEN_QI.getField()));
        }

        // 实收金额
        long actualReceiveAmount = (long) map.getOrDefault(ACTUAL_RECEIVE_AMOUNT, 0L);
        //商户红包优惠金额
        vo.setHongBaoWosaiMchTotal(BeanUtil.getPropLong(map, Transaction.HONGBAO_WOSAI_MCH_AMOUNT));
        //商户立减优惠金额
        vo.setDiscountWosaiMchTotal(BeanUtil.getPropLong(map, Transaction.DISCOUNT_WOSAI_MCH_AMOUNT));
        //商户立减优惠类型
        vo.setDiscountWoSaiMchType(BeanUtil.getPropString(map, Transaction.DISCOUNT_WOSAI_MCH_TYPE));
        if (actualReceiveAmount > 0L) {
            vo.setActualReceiveAmount(actualReceiveAmount);
        }
        //收钱吧优惠金额
        long wosaiFavorableAmount = (long) map.getOrDefault(WOSAI_FAVORABLE_AMOUNT, 0L);
        vo.setWosaiFavorableAmount(wosaiFavorableAmount);

        // 收款通道商户免充值优惠
        long channelMchFavorableAmount = (long) map.getOrDefault(CHANNEL_MCH_FAVORABLE_AMOUNT, 0L);
        vo.setChannelMchFavorableAmount(channelMchFavorableAmount);

        // 收款通道机构优惠
        long channelAgentFavorableAmount = (long) map.getOrDefault(CHANNEL_AGENT_FAVORABLE_AMOUNT, 0L);
        vo.setChannelAgentFavorableAmount(channelAgentFavorableAmount);

        //  收款通道商户充值优惠
        long channelMchTopUpFavorableAmount = (long) map.getOrDefault(CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT, 0L);

        vo.setChannelMchTopUpFavorableAmount(channelMchTopUpFavorableAmount);

        // 商户优惠
        long mchFavorableAmount = (long) map.getOrDefault(MCH_FAVORABLE_AMOUNT, 0L);
        vo.setMchFavorableAmount(mchFavorableAmount);
        vo.setPayments((List<Map<String,Object>>) map.get(PAYMENTS));
        // 商户补贴的优惠
        long outerDiscount = mchFavorableAmount + channelMchFavorableAmount;
        if (outerDiscount > 0L) {
            vo.setOuterDiscount(outerDiscount);
        }
        long sqbDiscount = wosaiFavorableAmount;
        if (sqbDiscount > 0L) {
            vo.setSqbDiscount(sqbDiscount);
        }
        //结算金额
        long clearingAmount = (long) map.getOrDefault(CLEARING_AMOUNT, 0L);
        vo.setClearingAmount(clearingAmount);
        Map<String, Object> enhanceExtraParams = TransactionUtils.enhanceExtraParams((Map) map.get(Transaction.EXTRA_PARAMS));
        Integer sharingFlag = BeanUtil.getPropInt(enhanceExtraParams, TransactionEnhanceFields.SHARING_FLAG.getField(), 0);
        // 花呗商家贴息
        long hbfqMchDiscountAmount = 0L;
        boolean isHbfqMchDiscount = false;
        String productFlag = MapUtil.getString(map, Transaction.PRODUCT_FLAG);
        if(!StringUtil.empty(productFlag) && productFlag.contains(TransactionConstant.PRODUCT_FLAG_HBFQ_DISCOUNT)) {
            isHbfqMchDiscount = true;
            vo.setHbfqType(Transaction.FQ_TYPE_SELLER);
        }
        //分账标识
        vo.setSharingFlag(sharingFlag);
        if (Objects.equals(sharingFlag, 1)) {
            //分账支出金额
            int type = MapUtil.getIntValue(map, TYPE);
            long sharingAmount = 0;
            boolean useRound = UpayProfitSharingUtil.useRound(MapUtil.getInteger(map, PAYWAY), MapUtil.getInteger(map, PROVIDER));
            Map<String, Object> profitSharing = MapUtil.getMap((Map) map.get(Transaction.EXTRA_PARAMS), Transaction.PROFIT_SHARING);
            if (TransactionType.PAYMENT_TYPES.contains(type)) {
                sharingAmount = UpayProfitSharingUtil.getSharingPayAmountByProfitSharing(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), useRound);
                if(isHbfqMchDiscount) {
                    hbfqMchDiscountAmount = TransactionUtil.getProductSharingPayAmountByProfitSharing(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), useRound, TransactionConstant.PRODUCT_FLAG_HBFQ_DISCOUNT);
                    vo.setInstalmentMerchantCharge(hbfqMchDiscountAmount);
                    vo.setHbfqMchDiscount(hbfqMchDiscountAmount);
                }
            } else if (TransactionType.REFUND_TYPES.contains(type)) {
                Long payClearingAmount = MapUtils.getLong(profitSharing, ProfitSharing.SHARING_PAY_CLEARING_AMOUNT);
                if (payClearingAmount != null) {
                    sharingAmount = UpayProfitSharingUtil.getSharingRestituteAmountByProfitSharing(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), payClearingAmount, useRound);
                    if(isHbfqMchDiscount) {
                        hbfqMchDiscountAmount = TransactionUtil.getProductSharingRestituteAmountByProfitSharing(profitSharing, clearingAmount, vo.getTradeFee(), payClearingAmount, useRound, TransactionConstant.PRODUCT_FLAG_HBFQ_DISCOUNT);
                        vo.setInstalmentMerchantCharge(hbfqMchDiscountAmount);
                        vo.setHbfqMchDiscount(hbfqMchDiscountAmount);
                    }
                } else { // 兼容历史数据
                    sharingAmount = UpayProfitSharingUtil.getSharingPayAmountByProfitSharing(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), useRound);
                }
            }
            vo.setSharingAmount(sharingAmount);
        }

        //交易费率
        vo.setFeeRate(CommonUtil.formatMoney(BeanUtil.getPropString(map, TransactionParam.FEE_RATE, "0.0"), 3));

        if (vo.getStatus() == Transaction.STATUS_SUCCESS) {
            //交易手续费
            vo.setTradeFee((long) map.getOrDefault(Transaction.FEE, 0L));
        }

        if (null != BeanUtil.getPropString(map, TransactionParam.FEE_RATE_ORIGINAL)) {
            //原交易费率
            vo.setFeeRateOriginal(CommonUtil.formatMoney(BeanUtil.getPropString(map, TransactionParam.FEE_RATE_ORIGINAL, "0.0"), 3));
        }


        //门店名称
        if (loadTypeSet.contains(LoadType.STORE_NAME)) {
            if ((!StringUtils.hasText(storeName) && !StringUtils.hasText(storeSn)) || !Objects.equals(vo.getStoreId(), storeId)) {
                Map storeMap = accountStoreService.findStore(BeanUtil.getPropString(map, "store_id"));
                storeName = WosaiMapUtils.getString(storeMap, "name");
                storeSn = WosaiMapUtils.getString(storeMap, "sn");
            }
            vo.setStoreName(storeName);
            vo.setStoreSn(storeSn);
        }

        vo.setMerchantName(merchantName);
        vo.setTerminalSn(terminalSn);
        vo.setTerminalName(terminalName);
        vo.setVendorAppAppid(vendorAppAppid);
        vo.setChannelFinishTime((Long) map.get(Transaction.CHANNEL_FINISH_TIME));
        vo.setTradeApp(StringUtil.empty(tradeApp) ? TRADE_APP_BASIC_PAY : tradeApp);
        //错误信息
        if (vo.getStatus() != Transaction.STATUS_SUCCESS) {
            // 支付通道返回的错误信息
            vo.setFailText(TransactionUtil.getErrorMessage((Map<String, Object>) map.get(BIZ_ERROR_CODE)));

        }

        vo.setExtraParams((Map) map.get(Transaction.EXTRA_PARAMS));
        // 交易勾兑状态
        vo.setBlendingStatus(getBlendingStatusByCtime((Long) map.get(CommonConstant.CTIME)));
        //是否使用额度包
        vo.setUseQuota(TransactionUtil.useQuota(map));

        // 添加预授权标识
        if (vo.getType() == Transaction.TYPE_REFUND 
                && MapUtil.getBooleanValue(MapUtil.getMap(map, Transaction.EXTRA_OUT_FIELDS), Transaction.IS_DEPOIST, false)) {
            vo.setIsDepositConsumeRefund(true);
        }

        String chargeSource = (String) BeanUtil.getNestedProperty(map, Transaction.EXTRA_PARAMS + "." + Transaction.SQB_CHARGE_SOURCE);
        vo.setChargeSource(chargeSource);
        return vo;
    }

    /**
     * 通过交易 ctime 获取交易勾兑状态
     */
    private Integer getBlendingStatusByCtime(long ctime) {
        // 当前基准时间
        long currentTime = System.currentTimeMillis();

        // 今天 0 点
        Instant todayAtZero = Instant.ofEpochMilli(currentTime).atZone(ZoneOffset.of("+8")).toLocalDate().atStartOfDay(ZoneOffset.of("+8")).toInstant();
        // 当天 15 点
        long todayAt15 = todayAtZero.plus(15, ChronoUnit.HOURS).toEpochMilli();
        // 昨天 23 点
        long yesterdayAt23 = todayAtZero.plus(-1, ChronoUnit.HOURS).toEpochMilli();
        // 前天 23 点
        long theDayBeforeYesterdayAt23 = todayAtZero.plus(-1, ChronoUnit.DAYS).plus(-1, ChronoUnit.HOURS).toEpochMilli();

        // 当天 15 点前
        if (currentTime < todayAt15) {
            // 将前天23点前的交易勾兑好
            return ctime < theDayBeforeYesterdayAt23 ? BlendingStatusConst.AFTER_BLENDING : BlendingStatusConst.BEFORE_BLENDING;

        } else { // 当天 15 点及其之后
            // 将昨天23点前的交易勾兑好
            return ctime < yesterdayAt23 ? BlendingStatusConst.AFTER_BLENDING : BlendingStatusConst.BEFORE_BLENDING;
        }

    }

    private void setHuabeiInfo(TransactionVo vo, Map<String, Object> map) {
        if (vo.getPayWay() != Order.PAYWAY_ALIPAY && vo.getPayWay() != Order.PAYWAY_ALIPAY2) {
            return;
        }
        try {
            Map<String, Object> extraOutFields = (Map<String, Object>) map.get(EXTRA_OUT_FIELDS);
            if (extraOutFields == null) {
                return;
            }
            if (BeanUtil.getPropBoolean(extraOutFields, TransactionParam.HB_FQ, false)) {
                vo.setInstalment(true);
                Map<String, Object> extraParams = (Map<String, Object>) map.get(Transaction.EXTRA_PARAMS);
                if (extraParams == null) {
                    return;
                }
                vo.setInstalmentMerchantCharge(BeanUtil.getPropLong(extraParams, TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE));
                vo.setHbfqType(vo.getInstalmentMerchantCharge() > 0L ? Transaction.FQ_TYPE_SELLER : Transaction.FQ_TYPE_BUYER);
            }
        } catch (Exception e) {
            logger.error("花呗分期解析错误:" + e.getMessage(), e);
        }
    }

    /**
     * 主要根据 id、merchantId、ctime 直查 HBase
     */
    @Override
    public Map<String, Object> queryOneByOid(@NotNull String merchantId, @NotNull Long ctime, @NotNull String tid, Boolean isSimple) {
        // ctime 无效，直接返回
        if (ctime < DateTimeConst.DATA_START_TIME || ctime > System.currentTimeMillis()) {
            return null;
        }
        Set<String> filterColumns = (isSimple != null && isSimple) ? HbaseQueryConverter.ORDER_SIMPLE_FIELDS : HbaseQueryConverter.ORDER_BASIC_FIELDS;

        // 直接拼接 HBase 查询 rowKey
        byte[] rowKeyStr = Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(ctime), Bytes.toBytes(tid));
        String rowKeyHexStr = Bytes.toHex(rowKeyStr);
        List<Pair<String, Long>> idCtimes = Collections.singletonList(Pair.of(rowKeyHexStr, ctime));
        List<Map<String, Object>> orderList = orderHBaseDao.rowFilterByIds(idCtimes, filterColumns, 3000);

        return CollectionUtils.isEmpty(orderList) ? null : orderList.get(0);
    }


    private Map<String, Object> doQueryOneByTid(String merchantId, Long ctime, String tid, Boolean isSimple, Boolean isAll) {
        if (ctime < DateTimeConst.DATA_START_TIME || ctime > System.currentTimeMillis()) {
            return null;
        }
        Set<String> filterColumns = (isSimple != null && isSimple) ? HbaseQueryConverter.SIMPLE_FIELDS : HbaseQueryConverter.BASIC_FIELDS;

        // 直接拼接 HBase 查询 rowKey
        byte[] rowKeyStr = Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(ctime), Bytes.toBytes(tid));
        String rowKeyHexStr = Bytes.toHex(rowKeyStr);
        List<Pair<String, Long>> idCtimes = Collections.singletonList(Pair.of(rowKeyHexStr, ctime));
        List<Map<String, Object>> transactionList = this.transactionHbaseDao.rowFilterByIds(idCtimes, (isAll != null && isAll) ? null : filterColumns, 3000);

        return CollectionUtils.isEmpty(transactionList) ? null : transactionList.get(0);
    }
}
