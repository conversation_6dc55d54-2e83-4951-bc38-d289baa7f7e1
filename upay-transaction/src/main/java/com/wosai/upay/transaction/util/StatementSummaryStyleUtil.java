package com.wosai.upay.transaction.util;

import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.transaction.enums.StatementTaskStyle;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.export.base.RunContext;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.StatementObjectConfig;
import com.wosai.upay.transaction.model.StatementSummary;
import com.wosai.upay.transaction.model.StatementTaskLog;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.transaction.util.LanguageUtil.*;

public class StatementSummaryStyleUtil {


    /**
     * 根据样式写各类汇总值
     *
     * @param summarySheet excel的sheet
     * @param summaryInfo  汇总信息
     * @param target       目标对象基本信息
     * @param basicValues  prefix前缀column
     */
    public static void appendByStyle(SXSSFSheet summarySheet, Map summaryInfo, Map<String, Object> target, List<String> basicValues, boolean forceCurrency, boolean appendCurrency, boolean isKAStatement) {
        appendByStyle(summarySheet, summaryInfo, target, basicValues, new ArrayList<>(), forceCurrency,  appendCurrency, true, isKAStatement);
    }

    public static void appendByStyle(SXSSFSheet summarySheet, Map summaryInfo, Map<String, Object> target, List<String> basicValues, boolean forceCurrency,  boolean appendCurrency, boolean appendPayWay, boolean isKAStatement) {
        appendByStyle(summarySheet, summaryInfo, target, basicValues, new ArrayList<>(), forceCurrency, appendCurrency, appendPayWay, isKAStatement);
    }

    public static void appendByStyle(SXSSFSheet summarySheet, Map summaryInfo, Map<String, Object> target, List<String> basicValues, List<Integer> payWayList, boolean forceCurrency, boolean appendCurrency, boolean appendPayWay, boolean isKAStatement) {
        StatementTaskStyle style = style();
        if (style == StatementTaskStyle.CLASSIC) {
            statisticAmounts(basicValues, summaryInfo, "", isKAStatement);
            if (appendPayWay) {
                if (payWayList != null && !payWayList.isEmpty()){
                    statisticPayWayAmounts(basicValues, summaryInfo, payWayList, "");
                } else {
                    statisticPayWayAmounts(basicValues, summaryInfo, "");
                }
            }
            if (forceCurrency && (target != null || appendCurrency)){
                addCurrencyValue(basicValues, target);
            }
        }
        if (style == StatementTaskStyle.STORE_IN) {
            statisticAmounts(basicValues, summaryInfo, StatementSummary.PREFIX, isKAStatement);
        }
        if (style == StatementTaskStyle.MIX) {
            statisticAmounts(basicValues, summaryInfo, "", isKAStatement);
            if (appendPayWay) {
                if (payWayList != null && !payWayList.isEmpty()){
                    statisticPayWayAmounts(basicValues, summaryInfo, payWayList, "");
                } else {
                    statisticPayWayAmounts(basicValues, summaryInfo, "");
                }
            }
            if (forceCurrency && (target != null || appendCurrency)) {
                addCurrencyValue(basicValues, target);
            }
            statisticAmounts(basicValues, summaryInfo, StatementSummary.PREFIX, isKAStatement);
        }
        StatementCommonUtil.appendLine(summarySheet, basicValues);
    }

    private static void statisticPayWayAmounts(List last, Map summary, String prefix) {
        statisticPayWayAmounts(last, summary, getPayWayListByInclude(), prefix);
    }

    private static void statisticPayWayAmounts(List last, Map summary, List<Integer> payWayList, String prefix) {
        if (DBSelectContext.getContext().getSelectDb() != UpayQueryType.UPAY_SWIPE) {
            for (int payway : payWayList) {
                last.add(BeanUtil.getPropLong(summary, prefix + payway + ":incomeAmount") / 100.0);
            }
        }
    }

    private static void statisticAmounts(List last, Map summary, String prefix, boolean isKAStatement) {
        last.addAll(Arrays.asList(BeanUtil.getPropLong(summary, prefix + "tradeCount"),
                BeanUtil.getPropLong(summary, prefix + "tradeAmount") / 100.0,
                BeanUtil.getPropLong(summary, prefix + "refundCount"),
                BeanUtil.getPropLong(summary, prefix + "refundAmount") / 100.0
        ));
        if (!isKAStatement) {
            last.add((BeanUtil.getPropLong(summary, prefix + "tradeAmount") + BeanUtil.getPropLong(summary, prefix + "refundAmount")) / 100.0);
        }
        //银行卡交易的交易汇总，不包含这些
        if (!isSwipe()) {
            last.addAll(Arrays.asList(
                    BeanUtil.getPropLong(summary, prefix + "merchantDiscount") / 100.0,
                    BeanUtil.getPropLong(summary, prefix + "shouqianbaDiscount") / 100.0,
                    BeanUtil.getPropLong(summary, prefix + "channelDiscount") / 100.0,
                    BeanUtil.getPropLong(summary, prefix + "channelMchTopUpDiscount") / 100.0,
                    BeanUtil.getPropLong(summary, prefix + "channelMchDiscount") / 100.0
            ));
        }
        last.addAll(Arrays.asList(
                BeanUtil.getPropLong(summary, prefix + "receiveAmount") / 100.0,
                BeanUtil.getPropLong(summary, prefix + "tradeFee") / 100.0)
        );
        //KA账单只展示分账金额（包含技术服务费）， 非KA账单展示技术服务费和分账金额
        if (isKAStatement) {
            last.addAll(Arrays.asList((BeanUtil.getPropLong(summary, prefix + "sharingAmount") + BeanUtil.getPropLong(summary, prefix + "tradeServiceSharingAmount")) / 100.0,
                    BeanUtil.getPropLong(summary, prefix + "incomeAmount") / 100.0));
        } else {
            last.addAll(Arrays.asList(BeanUtil.getPropLong(summary, prefix + "tradeServiceSharingAmount") / 100.0,
                    BeanUtil.getPropLong(summary, prefix + "sharingAmount") / 100.0,
                    BeanUtil.getPropLong(summary, prefix + "incomeAmount") / 100.0,
                    BeanUtil.getPropLong(summary, prefix + "dealToAccountAmount") / 100.0));
        }

    }

    private static void addCurrencyValue(List<String> basicValues, Map<String, Object> object) {
        basicValues.add(BeanUtil.getPropString(object, "merchant_currency", "CNY"));
    }


    private static List<String> swipeIgnoreColumn = Arrays.asList(MERCHANT_DISCOUNT, WOSAI_DISCOUNT,
            PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE, MERCHANT_DISCOUNT_NON_PREPAID_MODE
    );


    /**
     * 根据导出的表头判断需要扩展的表头
     */
    private static List<String> rewriteCorListByStyle(List<String> originList, List<String> extendCorList) {
        StatementTaskStyle style = style();
        if (style == StatementTaskStyle.CLASSIC) {
            if (extendCorList != null && extendCorList.size() > 0) {
                originList.addAll(extendCorList);
            }
        }
        if (style == StatementTaskStyle.STORE_IN) {
            originList = originList.stream().map(r -> StatementSummary.PREFIX + r).collect(Collectors.toList());
        }
        if (style == StatementTaskStyle.MIX) {
            List<String> newCol = originList.stream().map(r -> StatementSummary.PREFIX + r).collect(Collectors.toList());
            if (extendCorList != null && extendCorList.size() > 0) {
                originList.addAll(extendCorList);
            }
            originList.addAll(newCol);
        }
        //储值卡充值 不展示分账金额
//        originList.remove("store_sharing_amount");
        return originList;
    }


    /**
     * 商户对账单(门店交易汇总) 表头 根据样式展现列
     */
    public static List<String> getMerchantStatementStoreHeaderOrColumn(List<String> configCors) {
        List<String> config = new ArrayList<>(configCors);
        List<String> appendSummary = Lists.newArrayList(ALIPAY_NEW_NO, ALIPAY_NEW_PAID_AMOUNT, ALIPAY_NEW_SETTLEMENT_AMOUNT,
                WECHAT_NO, WECHAT_PAID_AMOUNT, WECHAT_SETTLEMENT_AMOUNT,
                UNIONPAY_CLOUD_FLASH_PAYMENT_NO, UNIONPAY_CLOUD_FLASH_PAYMENT_PAID_AMOUNT, UNIONPAY_CLOUD_FLASH_PAYMENT_SETTLEMENT_AMOUNT
        );
        if (RunContext.currentContext().crossMchRefundEnable()) {
            appendSummary.add(CROSS_MERCHANT_REFUND_NO);
            appendSummary.add(CROSS_MERCHANT_REFUND_AMOUNT);
        }
        appendSummary.addAll(Lists.newArrayList(BANKCARD_PAYMENT_NO, BANKCARD_PAYMENT_PAID_AMOUNT, BANKCARD_PAYMENT_SETTLEMENT_AMOUNT));
        return StatementSummaryStyleUtil.rewriteCorListByStyle(config, appendSummary);
    }


    /**
     * 根据导出的账单类型样式返回 不同的列
     */
    public static List<String> reSetColumnsByStyle(List<String> configCors) {
        List<String> config = new ArrayList<>(configCors);
        if (isSwipe()) {
            config.removeAll(swipeIgnoreColumn);
        }
        return rewriteCorListByStyle(config, null);
    }


    /**
     * 商户对账单(商户交易汇总) 表头 根据样式展现列
     */
    public static List<String> getTerminalColumnDescByTaskStyle() {
        StatementTaskStyle style = style();
        if (isSwipe()) {
            //刷卡的都是经典类型的账单，后面其它地方就不用判断了，就不用处理了
            style = StatementTaskStyle.CLASSIC;
        }
        if (style == StatementTaskStyle.CLASSIC) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NO, MERCHANT_STORE_NO, STORE_NAME, TERMINAL_NO, MERCHANT_TERMINAL_NO, TERMINAL_NAME,
                    TERMINAL_TYPE, DEVICE_ID, OPERATOR, CASHIER, TRANSACTION_NO, TRANSACTION_AMOUNT, REFUND_NO, REFUND_AMOUNT, TRANSACTION_NET_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT,
                    PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE, MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT,
                    TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT));
            //银行卡交易的交易汇总，去掉字段
            if (isSwipe()) {
                columnDesc.removeAll(getValueList(swipeIgnoreColumn));
            } else {
                for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                    String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                    columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
                }
            }
            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.STORE_IN) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NO, MERCHANT_STORE_NO, STORE_NAME, TERMINAL_NO, MERCHANT_TERMINAL_NO, TERMINAL_NAME,
                    TERMINAL_TYPE, DEVICE_ID, OPERATOR, CASHIER, STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_TRANSACTION_NET_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT,
                    STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT));
//            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.MIX) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NO, MERCHANT_STORE_NO, STORE_NAME, TERMINAL_NO, MERCHANT_TERMINAL_NO, TERMINAL_NAME,
                    TERMINAL_TYPE, DEVICE_ID, OPERATOR, CASHIER, TRANSACTION_NO, TRANSACTION_AMOUNT, REFUND_NO, REFUND_AMOUNT, TRANSACTION_NET_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT,
                    PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE, MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT,
                    TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT));
            for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
            }
            columnDesc.add(getValue(CURRENCY));
            columnDesc.addAll(new LinkedList<>(getValues(STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_TRANSACTION_NET_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT,
                    STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT)));
            return columnDesc;
        }
        throw new RuntimeException("not support style" + style);

    }


    /**
     * 商户对账单(门店收银台交易汇总) 表头 根据样式展现列
     */
    public static List<String> getCashDeskColumnDescByTaskStyle() {
        StatementTaskStyle style = style();
        if (isSwipe()) {
            //刷卡的都是经典类型的账单，后面其它地方就不用判断了，就不用处理了
            style = StatementTaskStyle.CLASSIC;
        }
        if (style == StatementTaskStyle.CLASSIC) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASH_DESK_NAME, TRANSACTION_NO, TRANSACTION_AMOUNT,
                    REFUND_NO, REFUND_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE,
                    MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT));
            //银行卡交易的交易汇总，去掉字段
            if (isSwipe()) {
                columnDesc.removeAll(getValueList(swipeIgnoreColumn));
            } else {
                for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                    String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                    columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
                }
            }
            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.STORE_IN) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASH_DESK_NAME, STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT,
                    STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT, STORE_IN_PAYMENT_TYPE_DISCOUNT,
                    STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE,
                    STORE_IN_SHARING_AMOUNT, STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT));
//            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.MIX) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASH_DESK_NAME, TRANSACTION_NO, TRANSACTION_AMOUNT,
                    REFUND_NO, REFUND_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE,
                    MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT));
            for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
            }
            columnDesc.add(getValue(CURRENCY));
            columnDesc.addAll(new LinkedList<>(getValues(STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT,
                    STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT)));
            return columnDesc;
        }
        throw new RuntimeException("not support style" + style);

    }

    public static List<String> getCashierColumnDescByTaskStyle() {
        StatementTaskStyle style = style();
        if (isSwipe()) {
            //刷卡的都是经典类型的账单，后面其它地方就不用判断了，就不用处理了
            style = StatementTaskStyle.CLASSIC;
        }
        if (style == StatementTaskStyle.CLASSIC) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASHIER_NAME, CASHIER_PHONE, TRANSACTION_NO, TRANSACTION_AMOUNT,
                    REFUND_NO, REFUND_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE,
                    MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT));
            //银行卡交易的交易汇总，去掉字段
            if (isSwipe()) {
                columnDesc.removeAll(getValueList(swipeIgnoreColumn));
            } else {
                for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                    String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                    columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
                }
            }
            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.STORE_IN) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASHIER_NAME, CASHIER_PHONE, STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT,
                    STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT, STORE_IN_PAYMENT_TYPE_DISCOUNT,
                    STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE,
                    STORE_IN_SHARING_AMOUNT, STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT));
//            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.MIX) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASHIER_NAME, CASHIER_PHONE, TRANSACTION_NO, TRANSACTION_AMOUNT,
                    REFUND_NO, REFUND_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE,
                    MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT));
            for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
            }
            columnDesc.add(getValue(CURRENCY));
            columnDesc.addAll(new LinkedList<>(getValues(STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT,
                    STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT)));
            return columnDesc;
        }
        throw new RuntimeException("not support style" + style);

    }


    /**
     * 商户对账单(门店交接班对账) 表头 根据样式展现列
     */
    public static List<String> getCashDeskChangeShiftsColumnDescByTaskStyle() {
        StatementTaskStyle style = style();
        if (isSwipe()) {
            //刷卡的都是经典类型的账单，后面其它地方就不用判断了，就不用处理了
            style = StatementTaskStyle.CLASSIC;
        }
        if (style == StatementTaskStyle.CLASSIC) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASH_DESK_NAME, CASH_DESK_NO, CHANGE_SHIFTS_BATCH_NO, CHANGE_SHIFTS_START_DATE, CHANGE_SHIFTS_END_DATE, CASHIER_NAME, TRANSACTION_NO, TRANSACTION_AMOUNT,
                    REFUND_NO, REFUND_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE,
                    MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT));
            //银行卡交易的交易汇总，去掉字段
            if (isSwipe()) {
                columnDesc.removeAll(getValueList(swipeIgnoreColumn));
            } else {
                for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                    String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                    columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
                }
            }
            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.STORE_IN) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASH_DESK_NAME, CASH_DESK_NO, CHANGE_SHIFTS_BATCH_NO, CHANGE_SHIFTS_START_DATE, CHANGE_SHIFTS_END_DATE, CASHIER_NAME, STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT,
                    STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT, STORE_IN_PAYMENT_TYPE_DISCOUNT,
                    STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT,
                    STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT));
//            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.MIX) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_NAME, CASH_DESK_NAME, CASH_DESK_NO, CHANGE_SHIFTS_BATCH_NO, CHANGE_SHIFTS_START_DATE, CHANGE_SHIFTS_END_DATE, CASHIER_NAME, TRANSACTION_NO, TRANSACTION_AMOUNT,
                    REFUND_NO, REFUND_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE,
                    MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT));
            for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
            }
            columnDesc.add(getValue(CURRENCY));
            columnDesc.addAll(new LinkedList<>(getValues(STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT,
                    STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT)));
            return columnDesc;
        }
        throw new RuntimeException("not support style" + style);

    }

    /**
     * 集团对账单(详细版表头)
     */
    public static List<String> groupDetailHeaderByStyle(List<Integer> payWayList) {
        StatementTaskStyle style = style();
        if (isSwipe()) {
            //刷卡的都是经典类型的账单，后面其它地方就不用判断了，就不用处理了
            style = StatementTaskStyle.CLASSIC;
        }
        if (style == StatementTaskStyle.CLASSIC) {
            List<String> columnDesc = new LinkedList<>();
            //银行卡交易的交易汇总，去掉字段
            columnDesc.addAll(getValues(MERCHANT_NO, MERCHANT_NAME, TRANSACTION_NO, TRANSACTION_AMOUNT, REFUND_NO, REFUND_AMOUNT, TRANSACTION_NET_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT,
                    PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE, MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT));
            if (isSwipe()) {
                columnDesc.removeAll(getValueList(swipeIgnoreColumn));
            } else {
                for (Integer payway : payWayList) {
                    String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                    columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
                }
            }
            return columnDesc;
        } else if (style == StatementTaskStyle.STORE_IN) {
            List<String> columnDesc = new LinkedList<>(getValues(MERCHANT_NO, MERCHANT_NAME, STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_TRANSACTION_NET_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT, STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT));
//            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.MIX) {
            List<String> columnDesc = new LinkedList<>(getValues(MERCHANT_NO, MERCHANT_NAME, TRANSACTION_NO, TRANSACTION_AMOUNT, REFUND_NO, REFUND_AMOUNT, TRANSACTION_NET_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT,
                    PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE, MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT));
            for (Integer payway : payWayList) {
                String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
            }
            columnDesc.addAll(new LinkedList<>(getValues(STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_TRANSACTION_NET_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT, STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT)));
            return columnDesc;
        }
        throw new RuntimeException("not support style" + style);
    }

    /**
     * 集团对账单(详细版 支付方式表头)
     */
    public static List<String> groupPayWayHeaderByStyle() {
        List<String> columns = Lists.newArrayList(TRANSACTION_NO, TRANSACTION_AMOUNT, REFUND_NO, REFUND_AMOUNT, TRANSACTION_NET_AMOUNT,
                MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE, MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT,
                CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT);

        return reSetColumnsByStyle(columns);
    }


    /**
     * 集团对账单(门店终端交易汇总清单)
     */
    public static List<String> getGroupTerminalColumnDescByTaskStyle() {
        StatementTaskStyle style = style();
        if (isSwipe()) {
            //刷卡的都是经典类型的账单，后面其它地方就不用判断了，就不用处理了
            style = StatementTaskStyle.CLASSIC;
        }
        if (style == StatementTaskStyle.CLASSIC) {
            List<String> columnDesc = new LinkedList<>(getValues(TRANSACTION_NO, TRANSACTION_AMOUNT, REFUND_NO, REFUND_AMOUNT, TRANSACTION_NET_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT,
                    PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE, MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT));
            //银行卡交易的交易汇总，去掉字段
            if (isSwipe()) {
                columnDesc.removeAll(getValueList(swipeIgnoreColumn));
            } else {
                for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                    String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                    columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
                }
            }
            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.STORE_IN) {
            List<String> columnDesc = new LinkedList<>(getValues(STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_TRANSACTION_NET_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT, STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT));
//            columnDesc.add(getValue(CURRENCY));
            return columnDesc;
        } else if (style == StatementTaskStyle.MIX) {
            List<String> columnDesc = new LinkedList<>(getValues(TRANSACTION_NO, TRANSACTION_AMOUNT, REFUND_NO, REFUND_AMOUNT, TRANSACTION_NET_AMOUNT, MERCHANT_DISCOUNT, WOSAI_DISCOUNT,
                    PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE, MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, TRADE_SERVICE_SHARING_AMOUNT, MERCHANT_SHARING_AMOUNT, SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT));
            for (Integer payway : StatementSummaryStyleUtil.getPayWayListByInclude()) {
                String paywayString = OrderUtil.getPaywayDesc(payway, getLanguage());
                columnDesc.add(paywayString + " " + getValue(SETTLEMENT_AMOUNT));
            }
            columnDesc.add(getValue(CURRENCY));
            columnDesc.addAll(new LinkedList<>(getValues(STORE_IN_TRANSACTION_NO, STORE_IN_TRANSACTION_AMOUNT, STORE_IN_REFUND_NO, STORE_IN_REFUND_AMOUNT, STORE_IN_TRANSACTION_NET_AMOUNT, STORE_IN_MERCHANT_DISCOUNT, STORE_IN_WOSAI_DISCOUNT,
                    STORE_IN_PAYMENT_TYPE_DISCOUNT, STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE, STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE, STORE_IN_PAID_AMOUNT, STORE_IN_CHARGE, STORE_IN_SHARING_AMOUNT, STORE_IN_TRADE_SERVICE_SHARING_AMOUNT, STORE_IN_MERCHANT_SHARING_AMOUNT, STORE_IN_SETTLEMENT_AMOUNT, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT)));
            return columnDesc;
        }
        throw new RuntimeException("not support style" + style);

    }

    public static List<Integer> getPayWayListByInclude() {
        List<Integer> original = OrderUtil.paywayList;
        Set<Integer> chargePayways = OrderUtil.chargePaywayList;
        String includes = RunContext.currentContext().getExportIncludes();
        List<Integer> result = Lists.newArrayList(original);
        if (!includes.contains(StatementTaskLog.INCLUDE_CHARGE + "")) {
            result.removeAll(chargePayways);
        }
        return result;
    }


    private static StatementTaskStyle style() {
        return RunContext.currentContext().getExportStyle();
    }

    private static boolean isSwipe() {
        return DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE;
    }

    public static List<String> resetColumns(List<String> columnDesc, boolean isKAStatement) {
        //如果是ka账单，那么不需要导出技术服务费和分账金额列； 反之不需要导出分账金额（含技术服务费） 列
        if (isKAStatement) {
            columnDesc.removeAll(getValueList(Lists.newArrayList(StatementObjectConfig.TRADE_SERVICE_SHARING_AMOUNT, StatementObjectConfig.MERCHANT_SHARING_AMOUNT, StatementObjectConfig.TRANSACTION_NET_AMOUNT, StatementObjectConfig.DEAL_TO_ACCOUNT_AMOUNT,
                    StatementSummary.PREFIX + StatementObjectConfig.TRADE_SERVICE_SHARING_AMOUNT,StatementSummary.PREFIX + StatementObjectConfig.MERCHANT_SHARING_AMOUNT, StatementSummary.PREFIX + StatementObjectConfig.TRANSACTION_NET_AMOUNT, StatementSummary.PREFIX + StatementObjectConfig.DEAL_TO_ACCOUNT_AMOUNT)));
        } else {
            columnDesc.removeAll(getValueList(Lists.newArrayList(StatementObjectConfig.SHARING_AMOUNT, StatementSummary.PREFIX + StatementObjectConfig.SHARING_AMOUNT)));
        }

        return columnDesc;
    }
}
