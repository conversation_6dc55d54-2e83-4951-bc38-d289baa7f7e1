package com.wosai.upay.transaction.util;


import com.wosai.upay.transaction.enums.BaseEnum;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class EnumUtils {

   public static <M, E extends Enum & BaseEnum<M, ?>> List<M> getListCode(E... values) {
        Optional<E[]> optionalValues = Optional.ofNullable(values);
        return (List)optionalValues.map((optionalValue) -> {
            return (List)Arrays.stream(optionalValue).map((value) -> {
                return ((BaseEnum)value).getCode();
            }).collect(Collectors.toList());
        }).orElse(null);
    }

   public static <E extends Enum<E> & BaseEnum<T, ?>, T> E getByCode(Class<E> enumType, T code) {
        E[] enumConstants = (E[])enumType.getEnumConstants();
        Optional<E> enums = Arrays.stream(enumConstants).filter((entry) -> {
            return entry.getCode().equals(code);
        }).findAny();
        return enums.orElse(null);
    }


    public static <E extends Enum<E> & BaseEnum<T, ?>, T> Set<T> getSetName(Class<E> enumType, Set<String> names){
        E[] enumConstants = (E[])enumType.getEnumConstants();
        return Arrays.stream(enumConstants)
                .filter(e -> names.contains(e.name()))
                .map(entry -> entry.getCode())
                .collect(Collectors.toSet());
    }
}
