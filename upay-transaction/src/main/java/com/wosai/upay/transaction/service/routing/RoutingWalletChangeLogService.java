package com.wosai.upay.transaction.service.routing;

import com.wosai.upay.transaction.service.dao.mysql.WalletChangeLogMySQLDao;
import com.wosai.upay.transaction.service.dao.hbase.WalletChangeLogHBaseDao;
import com.wosai.upay.transaction.service.model.po.WalletChangeLogPo;
import com.wosai.upay.transaction.service.model.query.WalletChangeLogQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service that routes wallet change log queries between MySQL and HBase/Solr
 * based on feature toggle configuration
 */
@Service
public class RoutingWalletChangeLogService {

    private static final Logger logger = LoggerFactory.getLogger(RoutingWalletChangeLogService.class);

    @Autowired
    private WalletChangeLogMySQLDao walletChangeLogMySQLDao;

    @Autowired
    private WalletChangeLogHBaseDao walletChangeLogHBaseDao;

    @Autowired
    private QueryRoutingService queryRoutingService;

    public List<WalletChangeLogPo> queryForList(WalletChangeLogQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for wallet change log queryForList, merchant: {}", merchantId);
            return walletChangeLogMySQLDao.queryForList(query);
        } else {
            logger.info("Using HBase/Solr for wallet change log queryForList, merchant: {}", merchantId);
            return walletChangeLogHBaseDao.queryForList(query);
        }
    }

    public long count(WalletChangeLogQuery query) {
        String merchantId = getFirstMerchantId(query);
        if (merchantId != null && queryRoutingService.shouldUseMySQL(merchantId)) {
            logger.info("Using MySQL for wallet change log count, merchant: {}", merchantId);
            return walletChangeLogMySQLDao.count(query);
        } else {
            logger.info("Using HBase/Solr for wallet change log count, merchant: {}", merchantId);
            return walletChangeLogHBaseDao.count(query);
        }
    }

    private String getFirstMerchantId(WalletChangeLogQuery query) {
        if (query.getMerchantId() != null && !query.getMerchantId().isEmpty()) {
            return query.getMerchantId();
        }
        return null;
    }
}