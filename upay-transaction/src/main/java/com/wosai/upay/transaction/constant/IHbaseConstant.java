package com.wosai.upay.transaction.constant;

import org.apache.hadoop.hbase.util.Bytes;


/**
 * <AUTHOR>
 */
public interface IHbaseConstant {

    byte[] BUYER_UID = Bytes.toBytes("buyer_uid");

    byte[] ID = Bytes.toBytes("id");

    byte[] TSN = Bytes.toBytes("tsn");

    byte[] STORE_ID = Bytes.toBytes("store_id");

    byte[] ORDER_SN = Bytes.toBytes("order_sn");

    byte[] CLIENT_TSN = Bytes.toBytes("client_tsn");

    byte[] TYPE = Bytes.toBytes("type");

    byte[] SUBJECT = Bytes.toBytes("subject");


    byte[] BODY = Bytes.toBytes("body");

    byte[] STATUS = Bytes.toBytes("status");

    byte[] ORIGINAL_AMOUNT = Bytes.toBytes("original_amount");

    byte[] EFFECTIVE_AMOUNT = Bytes.toBytes("effective_amount");

    byte[] PAID_AMOUNT = Bytes.toBytes("paid_amount");

    byte[] RECEIVED_AMOUNT = Bytes.toBytes("received_amount");

    byte[] ITEMS = Bytes.toBytes("items");

    byte[] BUYER_LOGIN = Bytes.toBytes("buyer_login");

    byte[] MERCHANT_ID = Bytes.toBytes("merchant_id");

    byte[] TRADE_NO = Bytes.toBytes("trade_no");

    byte[] PROVIDER = Bytes.toBytes("provider");

    byte[] TERMINAL_ID = Bytes.toBytes("terminal_id");

    byte[] ORDER_ID = Bytes.toBytes("order_id");


    byte[] OPERATOR = Bytes.toBytes("operator");


    byte[] PRODUCT_FLAG = Bytes.toBytes("product_flag");

    byte[] PAY_WAY = Bytes.toBytes("payway");


    byte[] SUB_PAYWAY = Bytes.toBytes("sub_payway");

    byte[] VERSION = Bytes.toBytes("version");

    byte[] DELETED = Bytes.toBytes("deleted");

    byte[]  FINISH_TIME = Bytes.toBytes("finish_time");

    byte[] CHANNEL_FINISH_TIME = Bytes.toBytes("channel_finish_time");

    byte[] CTIME = Bytes.toBytes("ctime");

    byte[] MTIME = Bytes.toBytes("mtime");


    byte[] PROVIDER_ERROR_INFO = Bytes.toBytes("provider_error_info");

    byte[] EXTRA_PARAMS = Bytes.toBytes("extra_params");

    byte[] EXTRA_OUT_FIELDS = Bytes.toBytes("extra_out_fields");

    byte[] EXTENDED_PARAMS = Bytes.toBytes("extended_params");

    byte[] CONFIG_SNAPSHOT = Bytes.toBytes("config_snapshot");

    byte[] REFLECT = Bytes.toBytes("reflect");


    byte[] BIZ_ERROR_CODE = Bytes.toBytes("biz_error_code");

    byte[] SN = Bytes.toBytes("sn");


    byte[] CLIENT_SN = Bytes.toBytes("client_sn");


    byte[] ORIGINAL_TOTAL = Bytes.toBytes("original_total");


    byte[] NET_ORIGINAL = Bytes.toBytes("net_original");


    byte[] EFFECTIVE_TOTAL = Bytes.toBytes("effective_total");


    byte[] NET_EFFECTIVE = Bytes.toBytes("net_effective");


    byte[] TOTAL_DISCOUNT = Bytes.toBytes("total_discount");


    byte[] NET_DISCOUNT = Bytes.toBytes("net_discount");


    byte[] NFC_CARD = Bytes.toBytes("nfc_card");


    byte[] NET_ITEMS = Bytes.toBytes("net_items");

    byte[] TCP_MODIFIED = Bytes.toBytes("tcp_modified");


}
