package com.wosai.upay.transaction.service.model.query;

import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.enums.LoadType;

import java.util.*;


/**
 * <AUTHOR>
 */
public class TransactionHBaseQuery extends BaseQuery {

    private List<String> storeIds;

    private List<String> merchantIds;

    private List<String> cashDeskIds;

    private List<String> terminals;

    private List<String> notTerminals;

    private List<Integer> payWays;

    private List<String> orderSns;

    private String transactionSn;

    private List<String> transactionSns;

    private List<String> productFlags;

    private Long channelFinishStartTime;

    private Long channelFinishEndTime;

    private List<String> clientTsns;

    private List<Integer> providers;

    private Long minOriginalAmount;

    private Long maxOriginalAmount;

    private List<String> buyerUids;

    /**
     * not empty
     */
    private Boolean buyerUidNotEmpty;

    private List<String> buyerLogins;

    private boolean providerIsNull;

    private String tradeNo;

    private List<String> openId;

    private List<String> tradeMemos;

    private Set<LoadType> loadTypes = new HashSet<>();

    private Map<CommonStatus, StatusTypeSubPayWayQuery> statusTypeSubPayWayQueries = new HashMap<>();

    public List<String> getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(List<String> storeIds) {
        this.storeIds = storeIds;
    }

    public List<String> getMerchantIds() {
        return merchantIds;
    }

    public void setMerchantIds(List<String> merchantIds) {
        this.merchantIds = merchantIds;
    }

    public List<String> getCashDeskIds() {
        return cashDeskIds;
    }

    public void setCashDeskIds(List<String> cashDeskIds) {
        this.cashDeskIds = cashDeskIds;
    }

    public List<String> getTerminals() {
        return terminals;
    }

    public void setTerminals(List<String> terminals) {
        this.terminals = terminals;
    }

    public List<String> getNotTerminals() {
        return notTerminals;
    }

    public void setNotTerminals(List<String> notTerminals) {
        this.notTerminals = notTerminals;
    }

    public List<Integer> getPayWays() {
        return payWays;
    }

    public void setPayWays(List<Integer> payWays) {
        this.payWays = payWays;
    }

    public List<String> getOrderSns() {
        return orderSns;
    }

    public void setOrderSns(List<String> orderSns) {
        this.orderSns = orderSns;
    }

    public String getTransactionSn() {
        return transactionSn;
    }

    public void setTransactionSn(String transactionSn) {
        this.transactionSn = transactionSn;
    }

    public List<String> getProductFlags() {
        return productFlags;
    }

    public void setProductFlags(List<String> productFlags) {
        this.productFlags = productFlags;
    }

    public Map<CommonStatus, StatusTypeSubPayWayQuery> getStatusTypeSubPayWayQueries() {
        return statusTypeSubPayWayQueries;
    }


    public Long getChannelFinishStartTime() {
        return channelFinishStartTime;
    }

    public void setChannelFinishStartTime(Long channelFinishStartTime) {
        this.channelFinishStartTime = channelFinishStartTime;
    }

    public Long getChannelFinishEndTime() {
        return channelFinishEndTime;
    }

    public void setChannelFinishEndTime(Long channelFinishEndTime) {
        this.channelFinishEndTime = channelFinishEndTime;
    }

    public List<String> getClientTsns() {
        return clientTsns;
    }

    public void setClientTsns(List<String> clientTsns) {
        this.clientTsns = clientTsns;
    }

    public List<Integer> getProviders() {
        return providers;
    }

    public void setProviders(List<Integer> providers) {
        this.providers = providers;
    }

    public Long getMinOriginalAmount() {
        return minOriginalAmount;
    }

    public void setMinOriginalAmount(Long minOriginalAmount) {
        this.minOriginalAmount = minOriginalAmount;
    }

    public Long getMaxOriginalAmount() {
        return maxOriginalAmount;
    }

    public void setMaxOriginalAmount(Long maxOriginalAmount) {
        this.maxOriginalAmount = maxOriginalAmount;
    }

    public List<String> getBuyerUids() {
        return buyerUids;
    }

    public void setBuyerUids(List<String> buyerUids) {
        this.buyerUids = buyerUids;
    }

    public Boolean getBuyerUidNotEmpty() {
        return buyerUidNotEmpty;
    }

    public void setBuyerUidNotEmpty(Boolean buyerUidNotEmpty) {
        this.buyerUidNotEmpty = buyerUidNotEmpty;
    }

    public List<String> getBuyerLogins() {
        return buyerLogins;
    }

    public void setBuyerLogins(List<String> buyerLogins) {
        this.buyerLogins = buyerLogins;
    }

    public boolean getProviderIsNull() {
        return providerIsNull;
    }

    public void setProviderIsNull(boolean providerIsNull) {
        this.providerIsNull = providerIsNull;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public List<String> getTradeMemos() {
        return tradeMemos;
    }

    public void setTradeMemos(List<String> tradeMemos) {
        this.tradeMemos = tradeMemos;
    }

    public List<String> getTransactionSns() {
        return transactionSns;
    }

    public void setTransactionSns(List<String> transactionSns) {
        this.transactionSns = transactionSns;
    }

    public List<String> getOpenId() {
        return openId;
    }

    public void setOpenId(List<String> openId) {
        this.openId = openId;
    }

    public Set<LoadType> getLoadTypes() {
        return loadTypes;
    }

    public void setLoadTypes(Set<LoadType> loadTypes) {
        this.loadTypes = loadTypes;
    }
}