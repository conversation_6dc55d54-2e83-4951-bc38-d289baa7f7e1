package com.wosai.upay.transaction.util;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wosai.trade.service.TradeAppService;
import com.wosai.trade.service.request.TradeAppQueryRequest;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.service.result.TradeAppResult;
import com.wosai.upay.core.bean.model.MetaBizModel;
import com.wosai.upay.core.bean.model.MetaPayPath;
import com.wosai.upay.core.bean.model.MetaPaySource;
import com.wosai.upay.core.service.MetaService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class MetaCacheUtil {

    public static final Logger logger = LoggerFactory.getLogger(MetaCacheUtil.class);

    @Autowired
    private MetaService externalMetaService;

    @Autowired
    private TradeAppService tradeAppService;

    private static final String TRADE_APP_CONFIG_KEY = "trade_app_config_key";

    private static final String META_BIZ_MODEL_KEY = "meta_biz_model_key";


    private static final String META_PAY_PATH_KEY = "meta_pay_path_key";

    private static final String META_PAY_SOURCE_KEY = "meta_pay_source_key";
    public final LoadingCache<String, Map<String, String>> TRADE_APP_CONFIG_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(60, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, Map<String, String>>() {
                @Override
                public Map<String, String> load(String key) throws Exception {
                    return getTradeAppConfig();
                }
            });


    public final LoadingCache<String, Map<String, String>> META_BIZ_MODEL_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(60, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, Map<String, String>>() {
                @Override
                public Map<String, String> load(String key) throws Exception {
                    Map<String, String> bizModelMap = new HashMap<>();
                    List<MetaBizModel> allMetaBizModel = externalMetaService.getAllMetaBizModel();
                    if (CollectionUtils.isNotEmpty(allMetaBizModel)) {
                        for (MetaBizModel metaBizModel : allMetaBizModel) {
                            bizModelMap.put(metaBizModel.getId(), metaBizModel.getName());
                        }
                    }
                    return bizModelMap;
                }
            });


    public final LoadingCache<String, Map<String, String>> META_PAY_PATH_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(60, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, Map<String, String>>() {
                @Override
                public Map<String, String> load(String key) throws Exception {
                    Map<String, String> payPathMap = new HashMap<>();
                    List<MetaPayPath> allMetaPayPath = externalMetaService.getAllMetaPayPath();
                    if (CollectionUtils.isNotEmpty(allMetaPayPath)) {
                        for (MetaPayPath metaPayPath : allMetaPayPath) {
                            payPathMap.put(metaPayPath.getId(), metaPayPath.getName());
                        }
                    }
                    return payPathMap;
                }
            });


    public final LoadingCache<String, Map<String, String>> META_PAY_SOURCE_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(60, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, Map<String, String>>() {
                @Override
                public Map<String, String> load(String key) throws Exception {
                    Map<String, String> paySourceMap = new HashMap<>();
                    List<MetaPaySource> allMetaPaySource = externalMetaService.getAllMetaPaySource();
                    if (CollectionUtils.isNotEmpty(allMetaPaySource)) {
                        for (MetaPaySource metaPaySource : allMetaPaySource) {
                            paySourceMap.put(metaPaySource.getId() + "_" + metaPaySource.getPayway(), metaPaySource.getName());
                        }
                    }
                    return paySourceMap;
                }
            });


    /**
     * 获取tradeApp配置信息
     *
     * @return
     */
    private Map<String, String> getTradeAppConfig() {
        int pageNo = 1;
        int pageSize = 1000;
        Map<String, String> tradeAppMap = new HashMap<>();
        while (true) {
            TradeAppQueryRequest tradeAppQueryRequest = new TradeAppQueryRequest();
            tradeAppQueryRequest.setPage(pageNo);
            tradeAppQueryRequest.setPageSize(1000);
            ListResult<TradeAppResult> tradeAppResultListResult = tradeAppService.queryTradeApps(tradeAppQueryRequest);
            List<TradeAppResult> records = tradeAppResultListResult.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                for (TradeAppResult record : records) {
                    tradeAppMap.put(String.valueOf(record.getId()), record.getName());
                }
            }
            if (records.size() < pageSize) {
                break;
            }
            pageNo++;
        }
        return tradeAppMap;
    }


    public String getTradeAppName(String id) {
        Map<String, String> tradeAppMap = new HashMap<>();
        try {
            tradeAppMap = TRADE_APP_CONFIG_CACHE.get(TRADE_APP_CONFIG_KEY);
        } catch (Exception e) {
            logger.error("trade_app_id:{}获取名称错误", id, e);
        }
        return tradeAppMap.get(id);
    }


    public String getMetaBizModelName(String id) {
        Map<String, String> bizModelMap = new HashMap<>();
        try {
            bizModelMap = META_BIZ_MODEL_CACHE.get(META_BIZ_MODEL_KEY);
        } catch (Exception e) {
            logger.error("biz_model_id:{}获取名称错误", id, e);
        }
        return bizModelMap.get(id);
    }


    public String getMetaPayPathName(String id) {
        Map<String, String> payPathMap = new HashMap<>();
        try {
            payPathMap = META_PAY_PATH_CACHE.get(META_PAY_PATH_KEY);
        } catch (Exception e) {
            logger.error("pay_path_id:{}获取名称错误", id, e);
        }
        return payPathMap.get(id);
    }


    public String getMetaPaySourceName(String id, Integer payway) {
        Map<String, String> paySourceMap = new HashMap<>();
        try {
            paySourceMap = META_PAY_SOURCE_CACHE.get(META_PAY_SOURCE_KEY);
        } catch (Exception e) {
            logger.error("pay_source_id:{},payway:{}获取名称错误", id, payway, e);
        }
        return paySourceMap.get(id + "_" + payway);
    }


}
