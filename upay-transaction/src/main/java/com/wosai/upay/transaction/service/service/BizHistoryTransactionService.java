package com.wosai.upay.transaction.service.service;

import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.service.impl.AccountBookBaseServiceImpl;
import com.wosai.upay.transaction.util.LanguageUtil;
import com.wosai.upay.transaction.util.MetaCacheUtil;
import com.wosai.upay.transaction.util.OdpsUtil;
import com.wosai.upay.transaction.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BizHistoryTransactionService {


    @Autowired
    private MetaCacheUtil metaCacheUtil;


    @Autowired
    private OdpsUtil odpsUtil;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private AccountBookBaseServiceImpl accountBookBaseService;


    private final List<Integer> aliPayList = Arrays.asList(
            Order.PAYWAY_ALIPAY,
            Order.PAYWAY_ALIPAY2
    );


    private final static String SQB = "sqb_";

    public List<Map<String, Object>> getOdpsResult(List<String> dateList, String tradeNo, String channelTradeNo) {
        String sql = odpsUtil.getSqlByName("transaction");
        String date = " (" + dateList.stream().map(s -> "'" + s + "'").collect(Collectors.joining(",")) + ")";
        sql = sql + date;
        if (!StringUtils.isEmpty(tradeNo) && !StringUtils.isEmpty(channelTradeNo)) {
            sql = sql + " and trade_no = '" + tradeNo + "' and get_json_object(extra_out_fields, '$.trade_no')  = '" + channelTradeNo + "' order by ctime asc limit 0,1;";
        } else if (!StringUtils.isEmpty(tradeNo)) {
            sql = sql + " and trade_no = '" + tradeNo + "' order by ctime asc limit 0,1;";
        } else if (!StringUtils.isEmpty(channelTradeNo)) {
            sql = sql + " and get_json_object(extra_out_fields, '$.trade_no')  = '" + channelTradeNo + "' order by ctime asc limit 0,1;";
        }

        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> result = odpsUtil.getResult(sql);
        long endTime = System.currentTimeMillis();
        log.info("查询历史交易数据，耗时：{}ms", endTime - startTime);
        return result;
    }


    public List<Map<String, Object>> queryTransactionDetails(List<Map<String, Object>> transactions, List queryFlag, boolean historyFlag) {
        List<Map<String, Object>> transactionList = new ArrayList<>();
        for (Map<String, Object> transaction : transactions) {
            String queryMerchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
            long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
            String transactionId = MapUtil.getString(transaction, Transaction.ID);
            transaction = accountBookBaseService.queryOneByTid(queryMerchantId, ctime, transactionId);
            if (transaction != null) {
                transactionList.add(transaction);
            }
        }
        if (!CollectionUtil.isEmpty(transactionList)) {
            try {
                businessService.addBusinessInfoWithQueryFlag(transactionList, queryFlag);
                for (Map<String, Object> transaction : transactionList) {
                    if (historyFlag) {
                        transaction.put("history_flag", true);
                    }
                    TransactionUtil.transformReflect(transaction);
                    TransactionUtil.jsonFormatOrder(transaction);
                    transaction.put(LanguageUtil.PAY_FUND_CHANNEL, TransactionUtil.getPayChannel(transaction));
                    transaction.put(LanguageUtil.USE_QUOTA, TransactionUtil.useQuota(transaction));
                    TransactionUtil.expandTransactionItemsPayments(transaction, false);
                    TransactionUtil.expandTransactionInfo(transaction);
                    TransactionUtil.calculateExtendFields(transaction);
                    TransactionUtils.expandMchDiscountOriginType(transaction);
                    TransactionUtil.expandSharingBooks(transaction, businessService, null);
                    TransactionUtil.removeBlobFields(transaction);
                    expandTransactionMetaInfo(transaction);
                }
            } catch (Exception e) {
                log.error("查询流水详情信息异常", e);
            }
        }
        return transactionList;
    }


    public Map<String, Object> expandTransactionMetaInfo(Map<String, Object> transaction) {
        String tradeApp = MapUtils.getString(transaction, Transaction.TRADE_APP);
        String bizModel = MapUtils.getString(transaction, Transaction.SQB_BIZ_MODEL);
        Integer payway = MapUtils.getInteger(transaction, Transaction.PAYWAY);
        if (payway != null && aliPayList.contains(payway)) {
            payway = Order.PAYWAY_ALIPAY2;
        }
        String payPath = MapUtils.getString(transaction, Transaction.SQB_PAY_PATH);
        String paySource = MapUtils.getString(transaction, Transaction.SQB_PAY_SOURCE);
        // 如果是用户打开源是收钱吧 payway设置为0
        if (paySource != null && paySource.startsWith(SQB)) {
            payway = 0;
        }
        transaction.put(Transaction.TRADE_APP_NAME, metaCacheUtil.getTradeAppName(tradeApp));
        transaction.put(Transaction.SQB_BIZ_MODEL_NAME, metaCacheUtil.getMetaBizModelName(bizModel));
        transaction.put(Transaction.SQB_PAY_SOURCE_NAME, metaCacheUtil.getMetaPaySourceName(paySource, payway));
        transaction.put(Transaction.SQB_PAY_PATH_NAME, metaCacheUtil.getMetaPayPathName(payPath));
        return transaction;

    }
}
