package com.wosai.upay.transaction.mq;

import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.*;

/***
 * @ClassName: KafkaConsumer
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/8/27 15:04
 */
@Slf4j
@Component
public class MQConsumer {

    @Value("${tradeMemo.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${tradeMemo.kafka.consumer.registry-url}")
    private String registryUrl;

    @Value("${tradeMemo.kafka.consumer.group-id}")
    private String groupId;

    @Value("${tradeMemo.kafka.consumer.topic}")
    private String topic;

    @Value("${tradeMemo.kafka.consumer.max-poll-records}")
    private Integer maxPollRecords;

    @Value("${mq.consumer.enabled}")
    private boolean consumerEnabled;

    @Resource
    private AnalyzeFacade analyzeFacade;

    private static final ExecutorService POLL_MSG_EXECUTOR = Executors.newSingleThreadExecutor();

    @PostConstruct
    private void init() {
        if (consumerEnabled) {
            startConsume();
        }
    }

    private KafkaConsumer<?, GenericRecord> buildKafkaConsumer() {
        Properties props = new Properties();
        props.put("bootstrap.servers", bootstrapServers);
        props.put("schema.registry.url", registryUrl);
        props.put("group.id", groupId);
        props.put("enable.auto.commit", "false");
        props.put("max.poll.records", maxPollRecords);
        props.put("key.deserializer", StringDeserializer.class);
        props.put("value.deserializer", StringDeserializer.class);
//        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        KafkaConsumer<?, GenericRecord> consumer = new KafkaConsumer<>(props);
        consumer.subscribe(Collections.singletonList(topic));
        return consumer;
    }

    private void startConsume() {
        KafkaConsumer<?, GenericRecord> consumer = buildKafkaConsumer();
        POLL_MSG_EXECUTOR.execute(() -> {
            while (true) {
                try {
                    ConsumerRecords<?, GenericRecord> consumerRecords = consumer.poll(2000L);
                    if (consumerRecords == null || consumerRecords.isEmpty()) {
                        log.info("拉取消息为空");
                        continue;
                    }

                    for (ConsumerRecord<?, GenericRecord> record : consumerRecords) {
                        analyzeFacade.analyzeTradeData(record);
                    }

                    //同步等待异步处理
                    analyzeFacade.batchExecuteAndWaitExec();
                    consumer.commitSync();
                } catch (Exception e) {
                    log.error("kafka consumer error: {}", e.getMessage(), e);
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e1) {
                    }
                }
            }
        });
    }
}