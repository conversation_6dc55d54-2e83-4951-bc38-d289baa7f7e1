package com.wosai.upay.transaction.util;

import com.wosai.upay.common.log.JsonRpcCallUtil;
import org.springframework.util.CollectionUtils;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public class JsonRpcCallUtilWrapper {

    private static final String REQUEST_URI = "request_url";

    public static Map<String, String> getCallHeaderMapFromRequest(HttpServletRequest request) {
        Map<String, String> headerMap = JsonRpcCallUtil.getCallHeaderMapFromRequest(request);
        if(!CollectionUtils.isEmpty(headerMap)){
            headerMap.put(REQUEST_URI,  request.getRequestURI());
        }
        return headerMap;
    }
}
