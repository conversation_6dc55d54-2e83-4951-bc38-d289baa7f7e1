package com.wosai.upay.transaction.util;

import org.springframework.util.ReflectionUtils;
import java.lang.reflect.Field;

public abstract class BaseTask implements Runnable{

    private static final String THREAD_LOCALS = "threadLocals";

    @Override
    public void run() {
        try{
            execute();
        }finally {
            remove();
        }

    }

    public abstract void execute();



    private void remove() {
        Field field = ReflectionUtils.findField(Thread.class,THREAD_LOCALS);
        field.setAccessible(true);
        try {
            field.set(Thread.currentThread(),null);
        } catch (IllegalAccessException e) {
            //e.printStackTrace();
        }
    }

}
