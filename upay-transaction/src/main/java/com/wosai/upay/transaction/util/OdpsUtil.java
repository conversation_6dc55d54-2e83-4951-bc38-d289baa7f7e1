package com.wosai.upay.transaction.util;


import com.aliyun.odps.Column;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.wosai.middleware.aliyun.odps.DynamicAliyunAccount;
import com.wosai.middleware.vault.Vault;
import com.wosai.pantheon.util.MapUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class OdpsUtil {
    @Resource
    private Vault vault;

    public static Odps odps;

    @Value("${odps.endPoint}")
    private String endPoint;
    @Value("${odps.projectName}")
    private String projectName;

    private static final Map<String, String> ODPS_SQLS = MapUtil.hashMap("transaction", "SELECT merchant_id, id, ctime, order_sn, tsn FROM wosai_main.dwb_f_trans_transaction WHERE pt IN ",
            "transactionByOrderSn", "SELECT t.merchant_id,t.id,t.ctime FROM wosai_main.dwb_f_trans_transaction t WHERE  t.pt >= '{pt_start}' and t.pt <= '{pt_end}' and t.order_sn = '{order_sn}';"
            );

    @PostConstruct
    private void init() {
        Account account = new DynamicAliyunAccount(vault);
        odps = new Odps(account);
        odps.setEndpoint(endPoint);
        odps.setDefaultProject(projectName);
        log.info("【OdpsUtils初始化】 endPoint: {}, projectName: {}" , endPoint, projectName);
    }

    @SneakyThrows
    /**
     *  获取sql的执行结果，适用于少量数据
     */
    public List<Map<String,Object>> getResult(String sql){
        Instance i = SQLTask.run(odps, sql);
        i.waitForSuccess();
        List<Record> result = SQLTask.getResult(i);
        if(result == null){
            return Collections.emptyList();
        }
        return result.stream().map(r -> {
            Map<String,Object> map = new LinkedHashMap<>();
            Column[] columns = r.getColumns();
            for (Column column : columns) {
                String columnName = column.getName();
                map.put(columnName, parseRecordColumnValue(r.get(columnName)));
            }
            return map;
        }).collect(Collectors.toList());
    }

    public String getSqlByName(String name){
        return ODPS_SQLS.get(name);
    }

    private Object parseRecordColumnValue(Object columnValue){
        if(columnValue == null){
            return null;
        }
        Object value = columnValue;
        if (columnValue instanceof byte[]) {
            value = new String((byte[]) columnValue);
        } else if (columnValue instanceof java.sql.Date) {
            value = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(((Date) columnValue).getTime()));
        } else if(columnValue instanceof Date){
            value =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(columnValue);
        }
        return value.toString().replace(",", "");
    }
}
