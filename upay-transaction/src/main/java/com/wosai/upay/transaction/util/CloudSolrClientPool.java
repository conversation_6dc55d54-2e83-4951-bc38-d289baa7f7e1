package com.wosai.upay.transaction.util;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.solr.client.solrj.impl.CloudSolrClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class CloudSolrClientPool {

    private GenericObjectPool<CloudSolrClient> clientGenericObjectPool;

    public CloudSolrClientPool(String zkHost) {
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMinIdle(15);
        poolConfig.setMaxTotal(100);
        poolConfig.setBlockWhenExhausted(true);
        poolConfig.setMaxWaitMillis(5000);
        clientGenericObjectPool = new GenericObjectPool<>(new CloudSolrClientFactory(zkHost), poolConfig);
        Runtime.getRuntime().addShutdownHook(new Thread(()->{
            clientGenericObjectPool.clear();
        }));


    }


    @SneakyThrows
    public CloudSolrClient borrowObject(){
        return clientGenericObjectPool.borrowObject();
    }

    public void returnObject(CloudSolrClient cloudSolrClient) {
        clientGenericObjectPool.returnObject(cloudSolrClient);
    }


  
}
