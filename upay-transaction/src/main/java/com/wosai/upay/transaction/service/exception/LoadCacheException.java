package com.wosai.upay.transaction.service.exception;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 */
public class LoadCacheException extends RuntimeException {

    private final Object key;

    public LoadCacheException(Object key, Object fields, Callable<?> loader, Throwable ex) {
        super(String.format("Value for key '%s'  fields '%s' could not be loaded using '%s'", key, fields, loader), ex);
        this.key = key;
    }

    public LoadCacheException(Object key, Object fields, Throwable ex) {
        super("Value for key " + key + "  fields " + fields + " could not be get cache from Redis", ex);
        this.key = key;
    }

    public Object getKey() {
        return this.key;
    }
}
