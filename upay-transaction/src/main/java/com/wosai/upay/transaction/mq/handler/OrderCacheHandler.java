package com.wosai.upay.transaction.mq.handler;

import com.google.common.collect.Lists;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.MapUtils;
import org.apache.hadoop.hbase.util.Bytes;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@Component
@Slf4j
public class OrderCacheHandler {
    @Autowired
    RedissonClient redissonClient;

    private static final String ORDER_NOT_EXISTS_RESET_SCRIPT =
            "local isExists = redis.call('HEXISTS', KEYS[1], ARGV[1])\n" +
            "if isExists == 1 then\n" +
            "    redis.call('HDEL', KEYS[1], ARGV[1])\n" +
            "    redis.call('HSET', KEYS[1], ARGV[2], ARGV[3], ARGV[4], ARGV[5])\n" +
            "    redis.call('EXPIRE', KEYS[1], 1800)\n" +
            "end\n" +
            "return nil\n";

    public void handle(List<Map> tran) {
        List<Map> needCacheTransactions = tran.stream().filter(tr -> needCacheTransaction(tr)).collect(Collectors.toList());
        if (needCacheTransactions.size() > 0) {
            RScript script = redissonClient.getScript();
            String scriptHash = script.scriptLoad(ORDER_NOT_EXISTS_RESET_SCRIPT);
            List<List<Map>> partitions = Lists.partition(needCacheTransactions, 50);
            for (List<Map> partition : partitions) {
                try {
                    resetOrderCache(partition, scriptHash);
                } catch (Exception e) {
                    log.error("reset order cache error", e);
                }
            }
        }
    }

    private void resetOrderCache(List<Map> partition, String scriptHash) {
        RBatch batch = redissonClient.createBatch();
        for (Map transaction : partition) {
            String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
            String clientSn = MapUtil.getString(transaction, Transaction.CLIENT_TSN);
            String orderId = MapUtils.getString(transaction, Transaction.ORDER_ID);
            Long ctime = MapUtils.getLongValue(transaction, DaoConstants.CTIME);

            String key = CommonConstant.HBASE_ROWKEYS_PREFIX + "getOrderBySn:"
                    + "merchantId" + Optional.ofNullable(merchantId).orElse("")
                    + "orderSn" + Optional.ofNullable(null).orElse("")
                    + "clientSn" + Optional.ofNullable(clientSn).orElse("")
                    + "partition" + DataPartitionConst.RECENT_6M;
            String rowKey = Hex.encodeHexString(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(ctime), Bytes.toBytes(orderId)));
            batch.getScript().evalShaAsync(RScript.Mode.READ_WRITE, scriptHash, RScript.ReturnType.BOOLEAN, Lists.newArrayList(key),
                    CommonConstant.FIELD_EXIST, DaoConstants.ID, rowKey, DaoConstants.CTIME, ctime);
        }
        batch.execute();
    }

    public boolean needCacheTransaction(Map transaction) {
        // 只处理非华东订单
        String tsn = MapUtil.getString(transaction, Transaction.TSN, "");
        if (tsn.startsWith("78950") || tsn.startsWith("78952")) {
            return false;
        }
        // 只处理支付交易
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (type != Transaction.TYPE_PAYMENT) {
            return false;
        }
        // 只处理非BSC交易（目前只有门店码交易会转发到不同区域）
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY, SubPayway.BARCODE.getCode());
        if (subPayway == SubPayway.BARCODE.getCode()) {
            return false;
        }
        return true;
    }
}