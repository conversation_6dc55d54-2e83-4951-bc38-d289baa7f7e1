package com.wosai.upay.transaction.service.config;

import com.wosai.upay.transaction.util.ESUtil;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/***
 * @ClassName: ElasticsearchConfig
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/6/4 1:56 PM
 */
@Configuration
public class ElasticsearchConfig {

    @Bean(name = "esProperties")
    public ElasticsearchProperties getEsProperties(@Value("${es.instance}") String instance,
                                                  @Value("${es.hostname}") String hostname,
                                                  @Value("${es.port}") int port,
                                                  @Value("${es.connect-timeout}") int connectTimeout,
                                                  @Value("${es.socket-timeout}") int socketTimeout,
                                                  @Value("${es.connection-request-timeout}") int connectionRequestTimeout,
                                                  @Value("${es.max-connect-total}") int maxConnectTotal,
                                                  @Value("${es.max-connect-per-route}") int maxConnectPerRoute) {
        ElasticsearchProperties properties = new ElasticsearchProperties();
        properties.setInstance(instance);
        properties.setHostname(hostname);
        properties.setPort(port);
        properties.setConnectTimeout(connectTimeout);
        properties.setSocketTimeout(socketTimeout);
        properties.setConnectionRequestTimeout(connectionRequestTimeout);
        properties.setMaxConnectTotal(maxConnectTotal);
        properties.setMaxConnectPerRoute(maxConnectPerRoute);
        return properties;
    }

    @Bean(name = "tradeMemoEsProperties")
    public ElasticsearchProperties getTradeMemoEsProperties(@Value("${tradeMemo.es.instance}") String instance,
                                                   @Value("${tradeMemo.es.hostname}") String hostname,
                                                   @Value("${tradeMemo.es.port}") int port,
                                                   @Value("${tradeMemo.es.connect-timeout}") int connectTimeout,
                                                   @Value("${tradeMemo.es.socket-timeout}") int socketTimeout,
                                                   @Value("${tradeMemo.es.connection-request-timeout}") int connectionRequestTimeout,
                                                   @Value("${tradeMemo.es.max-connect-total}") int maxConnectTotal,
                                                   @Value("${tradeMemo.es.max-connect-per-route}") int maxConnectPerRoute) {
        ElasticsearchProperties properties = new ElasticsearchProperties();
        properties.setInstance(instance);
        properties.setHostname(hostname);
        properties.setPort(port);
        properties.setConnectTimeout(connectTimeout);
        properties.setSocketTimeout(socketTimeout);
        properties.setConnectionRequestTimeout(connectionRequestTimeout);
        properties.setMaxConnectTotal(maxConnectTotal);
        properties.setMaxConnectPerRoute(maxConnectPerRoute);
        return properties;
    }


    @Bean(name = "restHighLevelClient", destroyMethod = "close")
    public RestHighLevelClient restHighLevelClient(@Qualifier("esProperties") ElasticsearchProperties esProperties){
        return ESUtil.getPayGroupEsClient(esProperties);
    }

    @Bean(name = "tradeMemoRestHighLevelClient", destroyMethod = "close")
    public RestHighLevelClient tradeMemoRestHighLevelClient(@Qualifier("tradeMemoEsProperties") ElasticsearchProperties tradeMemoEsProperties){
        return ESUtil.getPayGroupEsClient(tradeMemoEsProperties);
    }
}
