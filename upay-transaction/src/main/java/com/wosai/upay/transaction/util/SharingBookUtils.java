package com.wosai.upay.transaction.util;

import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.*;
import com.wosai.profit.sharing.model.upay.Transaction;
import com.wosai.upay.transaction.constant.CommonConstant;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/11/19.
 */
public class SharingBookUtils {
    private static final Map<Integer,String> sharingTypeMap = CollectionUtil.hashMap(
            1, "分账", 2, "分账回退"
    );

    public static void appendHeaderForStatementDetailSheet(Map context, SXSSFSheet sheet) {
        addLine(sheet, "收钱吧分账明细");
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        addLine(sheet, String.format(
                "起始日期" + ":[%s]—:[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()
        ));

        addLine(sheet, "#-----------------------------------------分账明细列表----------------------------------------#");
        addLine(sheet, "分账请求时间", "分账流水号", "外部业务流水号", "收款通道订单号", "流水类型", "分账状态", "订单金额", "分账金额", "分账比例", 
                "分账应用方", "分账完成时间", "分账机构流水号", "分账模型",
                "分账支出方业务编号", "分账支出方名称", "分账支出方账号", "分账支出方账号开立机构",
                "分账收款方编号", "分账收款方名称", "分账收款方账号", "分账收款方账号开立机构", "自定义收款方编号", "分账收款方业务编号");
    }

    public static void appendSharingBookDetail(Map context, List<Map<String, Object>> sharingBookList, SXSSFSheet sheet) {
        for (Map<String, Object> map : sharingBookList) {
            Map<String,Object> applicationMap = (Map<String, Object>) map.get(com.wosai.profit.sharing.constant.CommonConstant.APPLICATION_DETAIL);
            Map<String,Object> receiverDetailMap = (Map<String, Object>) map.get(com.wosai.profit.sharing.constant.CommonConstant.RECEIVER_DETAIL);
            Map<String,Object> modelDetailMap = (Map<String, Object>) map.get(com.wosai.profit.sharing.constant.CommonConstant.MODEL_DETAIL);
            Map<String,Object> transactionDetailMap = (Map<String, Object>) map.get(com.wosai.profit.sharing.constant.CommonConstant.TRANSACTION_DETAIL);
            Map<String,Object> transactionExtraParamsMap = MapUtil.getMap(transactionDetailMap, SharingTransaction.EXTRA_PARAMS);
            Integer status = MapUtil.getInteger(map, SharingBook.STATUS);
            int sharingType = MapUtil.getIntValue(map, SharingBook.TYPE);
            int sign = sharingType == SharingTransaction.TYPE_SHARING_RESTITUTE ? -1 : 1;

            List rowValue = new ArrayList();
            rowValue.add(CommonConstant.DETAIL_SDF.get().format(map.get(DaoConstants.CTIME)));
            rowValue.add(MapUtil.getString(map, DaoConstants.ID));
            rowValue.add(MapUtil.getString(map, SharingBook.CLIENT_SN));
            rowValue.add(MapUtil.getString(transactionExtraParamsMap, SharingTransaction.TRADE_NO));
            rowValue.add(sharingTypeMap.get(sharingType));
            rowValue.add(getStatusDesc(MapUtil.getInteger(map, SharingBook.STATUS)));
            rowValue.add(StringUtils.cents2yuan(sign * MapUtil.getLongValue(map, SharingBook.ORIGINAL_AMOUNT)));
            rowValue.add(StringUtils.cents2yuan(sign * MapUtil.getLongValue(map, SharingBook.AMOUNT)));
            rowValue.add(MapUtil.getString(map, SharingBook.RATIO));
            rowValue.add(MapUtil.getString(applicationMap, Application.NAME));
            rowValue.add(status != null && status == SharingBook.STATUS_SUCC ? CommonConstant.DETAIL_SDF.get().format(map.get(DaoConstants.MTIME)): "");
            rowValue.add(MapUtil.getString(transactionDetailMap, SharingTransaction.TRADE_NO));
            rowValue.add(MapUtil.getString(modelDetailMap, Model.NAME));
            rowValue.add(MapUtil.getString(map, com.wosai.profit.sharing.constant.CommonConstant.MERCHANT_SN));
            rowValue.add(MapUtil.getString(map, com.wosai.profit.sharing.constant.CommonConstant.MERCHANT_NAME));
            rowValue.add(MapUtil.getString(map, SharingBook.PAYER_ACCOUNT_NO));
            rowValue.add(getOrganizationDesc(MapUtil.getInteger(map, SharingBook.ORGANIZATION)));
            rowValue.add(MapUtil.getString(map, SharingBook.RECEIVER_ID));
            rowValue.add(MapUtil.getString(receiverDetailMap, Receiver.NAME));
            rowValue.add(MapUtil.getString(map, SharingBook.RECEIVER_ACCOUNT_NO));
            rowValue.add(getOrganizationDesc(MapUtil.getInteger(map, SharingBook.ORGANIZATION)));
            rowValue.add(MapUtil.getString(receiverDetailMap, Receiver.CLIENT_SN));
            rowValue.add(MapUtil.getString(receiverDetailMap, Receiver.MERCHANT_SN));
            SheetHelper.appendLine(sheet, rowValue);
        }

    }


    private static final Map<Integer, String> statusMap = CollectionUtil.hashMap(
            0, "待处理", 1, "处理中", 2, "处理失败", 3, "处理成功", 4, "分账未知", 5, "处理失败已关闭"
    );
    public static String getStatusDesc(Integer status){
        return statusMap.getOrDefault(status, status + "");
    }

    private static final Map<Integer,String> organizationsMap = CollectionUtil.hashMap(
      1002, "拉卡拉", 1, "支付宝", 3, "微信"

    );

    public static String getOrganizationDesc(Integer organization){
        return organizationsMap.getOrDefault(organization, organization + "");
    }

    private static void addLine(SXSSFSheet sxssfSheet, String... s) {
        SheetHelper.appendLine(sxssfSheet, Arrays.asList(s));
    }
}
