package com.wosai.upay.transaction.service.remote;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.trace.TimedCallable;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.HttpAsyncResponseConsumerFactory;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Component
public class ElasticsearchService {

    @Resource
    private RestHighLevelClient restHighLevelClient;

    private static final Map<String, String> TABLE_MAPPING = new HashMap<>();
    private static final String ES_SEARCH =  "es@search";
    private static final String ES_COUNT =  "es@count";
    private static final String ES_METHOD_SEARCH = "com.wosai.upay.transaction.service.remote.ElasticsearchService:search(java.lang.String,org.elasticsearch.search.builder.SearchSourceBuilder)";
    private static final String ES_METHOD_COUNT = "com.wosai.upay.transaction.service.remote.ElasticsearchService:count(java.lang.String,org.elasticsearch.search.builder.SearchSourceBuilder)";

    @Value("${es.index_name}")
    private String indexName;

    @Value("${tradeMemo.es.index_name}")
    private String memoIndexName;

    private static final RequestOptions COMMON_OPTIONS;

    static {
        RequestOptions.Builder builder = RequestOptions.DEFAULT.toBuilder();
        builder.setHttpAsyncResponseConsumerFactory(
                new HttpAsyncResponseConsumerFactory
                .HeapBufferedResponseConsumerFactory(30 * 1024 * 1024));
        COMMON_OPTIONS = builder.build();
    }

    @PostConstruct
    public void init() {
        TABLE_MAPPING.put(TransactionHBaseDao.TABLE_PREFIX, indexName);
    }

    /**
     * 搜索文档
     * @param typeName
     * @param builder
     * @param merchantIds
     * @return
     * @throws Exception
     */
    public SearchResponse search(String typeName, SearchSourceBuilder builder, List<String> merchantIds) throws Exception {
        try {
            SphU.entry(ES_METHOD_SEARCH, EntryType.IN, 1);
            return TimedCallable.of(ES_SEARCH, () ->{
                try {
                    SearchRequest searchRequest = new SearchRequest(TABLE_MAPPING.get(typeName));
                    searchRequest.source(builder);
                    setRouting(searchRequest, merchantIds);
                    SearchResponse response = restHighLevelClient.search(searchRequest, COMMON_OPTIONS);
                    return response;
                }catch(Exception e){
                    ActiveSpan.error("es查询出错" + e.getMessage());
                    throw e;
                }
            }).call();
        } catch (BlockException ex) {
            throw new com.wosai.upay.transaction.exception.BlockException("请求过于频繁,请稍后重试");
        } finally {
            SphU.entryEnd(1);
        }
    }

    public SearchResponse search(RestHighLevelClient client, SearchSourceBuilder builder) throws Exception {
        try {
            SphU.entry(ES_METHOD_SEARCH, EntryType.IN, 1);
            return TimedCallable.of(ES_SEARCH, () ->{
                try {
                    SearchRequest searchRequest = new SearchRequest(memoIndexName);
                    searchRequest.source(builder);
                    return client.search(searchRequest, COMMON_OPTIONS);
                }catch(Exception e){
                    ActiveSpan.error("es查询出错" + e.getMessage());
                    throw e;
                }
            }).call();
        } catch (BlockException ex) {
            throw new com.wosai.upay.transaction.exception.BlockException("请求过于频繁,请稍后重试");
        } finally {
            SphU.entryEnd(1);
        }
    }
    

    public SearchResponse count(String typeName, SearchSourceBuilder builder, List<String> merchantIds) throws Exception {
        try {
            SphU.entry(ES_METHOD_COUNT, EntryType.IN, 1);
            return TimedCallable.of(ES_COUNT, () ->{
                try {
                    SearchRequest searchRequest = new SearchRequest(TABLE_MAPPING.get(typeName)); 
                    searchRequest.source(builder);
                    setRouting(searchRequest, merchantIds);
                    SearchResponse response = restHighLevelClient.search(searchRequest, COMMON_OPTIONS);
                    return response;
                }catch(Exception e){
                    ActiveSpan.error("es查询出错" + e.getMessage());
                    throw e;
                }
            }).call();
        } catch (BlockException ex) {
            throw new com.wosai.upay.transaction.exception.BlockException("请求过于频繁,请稍后重试");
        } finally {
            SphU.entryEnd(1);
        }
    }

    private void setRouting(SearchRequest searchRequest, List<String> merchantIds) {
        if(CollectionUtil.isNotEmpty(merchantIds)) {
            Set<String> routes = merchantIds.stream().map(mid -> {
                int shardNum = Math.abs(mid.hashCode() % CommonConstant.INDEX_SHARDS);
                // ES会使用routing的值计算出存储的分片，导致2&4都指向第3个分片上，分片1上面没有数据，需要将4转换为5，转换后会存储到分片1上
                // 详情：https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********
                if (shardNum == 4) {
                    shardNum = 5;
                }
                return shardNum + "";
            }).collect(Collectors.toSet());
            searchRequest.routing(routes.toArray(new String[routes.size()]));
        }
    }
}
