package com.wosai.upay.transaction.util;

import com.google.common.util.concurrent.MoreExecutors;
import io.netty.util.internal.PlatformDependent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ReflectionUtils;
import sun.management.ManagementFactoryHelper;

import java.lang.management.BufferPoolMXBean;
import java.lang.reflect.Field;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

public class DirectMemReporter {
    private final Logger logger = LoggerFactory.getLogger(DirectMemReporter.class);

    private AtomicLong directMem = new AtomicLong();
    private ScheduledExecutorService executor = MoreExecutors.getExitingScheduledExecutorService(
            new ScheduledThreadPoolExecutor(1), 10, TimeUnit.SECONDS);

    public DirectMemReporter() {
        Field field = ReflectionUtils.findField(PlatformDependent.class, "DIRECT_MEMORY_COUNTER");
        field.setAccessible(true);
        try {
            directMem = (AtomicLong) field.get(PlatformDependent.class);
        } catch (IllegalAccessException e) {
        }
    }

    public void startReport() {
        executor.scheduleAtFixedRate(() -> {
            logger.info("netty direct memory size:{}mb, max:{}", directMem.get() / 1000 / 1000, PlatformDependent.maxDirectMemory() / 1000 / 1000);
            logger.info("noCleaner direct memory size:{}mb, max:{}", PlatformDependent.usedDirectMemory() / 1000 / 1000, PlatformDependent.maxDirectMemory() / 1000 / 1000);

            List<BufferPoolMXBean> bufferPoolMXBeans = ManagementFactoryHelper.getBufferPoolMXBeans();
            for (BufferPoolMXBean mxBean : bufferPoolMXBeans) {
                logger.info("mxBean name:{}, count: {}, TotalCapacity: {}, MemoryUsed:{}", mxBean.getName(),
                        mxBean.getCount(), mxBean.getTotalCapacity(), mxBean.getMemoryUsed());
            }

        }, 0, 1, TimeUnit.SECONDS);
    }
}