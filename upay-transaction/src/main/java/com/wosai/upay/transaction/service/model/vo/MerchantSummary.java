package com.wosai.upay.transaction.service.model.vo;


import lombok.Data;

@Data
public  class MerchantSummary extends BaseSummary {

    //付款时间
    String paymentTime;

    /**
     * 期末充值金额负债
     */
    private long closingRechargeLiabilities;

    /**
     * 期末赠送金额负债
     */
    private long closingGiftLiabilities;

    /**
     * 期末总负债
     */
    private long closingTotalLiabilities;

    public MerchantSummary() {
    }

    public MerchantSummary(String paymentTime, Long statementId) {
        this.paymentTime = paymentTime;
        this.statementId = statementId;
    }



}