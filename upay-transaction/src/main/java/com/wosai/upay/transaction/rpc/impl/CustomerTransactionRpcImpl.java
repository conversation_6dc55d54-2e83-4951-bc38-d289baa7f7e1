package com.wosai.upay.transaction.rpc.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.DaoConstants;
import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;
import com.wosai.upay.transaction.enums.LoadType;
import com.wosai.upay.transaction.enums.TransactionCategory;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.customer.*;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.rpc.CustomerTransactionRpc;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.dao.hbase.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.po.TransactionSummaryPo;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionSumV;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;
import com.wosai.upay.transaction.service.service.common.OperatorService;
import com.wosai.upay.transaction.service.service.impl.AccountBookBaseServiceImpl;
import com.wosai.upay.transaction.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Days;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.FutureTask;
import java.util.stream.Collectors;

/**
 * CustomerTransactionRpcImpl
 *
 * <AUTHOR>
 */
@Slf4j
@AutoJsonRpcServiceImpl
@Service
public class CustomerTransactionRpcImpl implements CustomerTransactionRpc {

    /**
     * OrderByEnum 和 OrderBy 映射
     */
    private static final Map<OrderByEnum, OrderBy> ORDER_BY_ENUM_ORDER_BY_MAP = new HashMap<OrderByEnum, OrderBy>() {{
        put(OrderByEnum.CTIME_ASC, new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC));
        put(OrderByEnum.CTIME_DESC, new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC));
        put(OrderByEnum.AMOUNT_ASC, new OrderBy(Transaction.ORIGINAL_AMOUNT, OrderBy.OrderType.ASC));
        put(OrderByEnum.AMOUNT_DESC, new OrderBy(Transaction.ORIGINAL_AMOUNT, OrderBy.OrderType.DESC));
    }};
    private static final Set<LoadType> GET_ACCOUNT_BOOK_RECORDS_LOADTYPES = Sets.newHashSet(LoadType.STORE_STAFF, LoadType.CONFIG_SNAPSHOT, LoadType.TERMINAL_CODE, LoadType.ITEMS, LoadType.EXTRA_OUT_FIELDS, LoadType.STORE_NAME);

    @Autowired
    private TransactionHBaseDao transactionHBaseDao;

    @Autowired
    private AccountBookBaseServiceImpl accountBookBaseService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private OperatorService operatorService;

    @Override
    public SumCustomerDetailResponse sumCustomerDetail(String merchantId, SumCustomerDetailRequest request) {
        // 检查时间跨度
        checkRequest(request);

        TransactionHBaseQuery query = buildTransactionHBaseQuery(merchantId, request);
        TransactionSummaryPo po = transactionHBaseDao.summaryTx(query);
        if(TransactionSummaryPo.NULL_VALUE.equals(po)){
            return null;
        }
        TransactionSumV sumV = po.buildSumV();
        return new SumCustomerDetailResponse()
                .setTradCount(sumV.getSalesCount())
                .setTradeAmount(sumV.getSalesAmount());
    }

    @Override
    public List<MerchantTradesResponse> listMerchantTrades(String merchantId, ListMerchantTradesRequest request) {
        checkRequest(request);

        TransactionHBaseQuery query = buildTransactionHBaseQuery(merchantId, request);
        query.setLoadTypes(GET_ACCOUNT_BOOK_RECORDS_LOADTYPES);
        query.setSolrTimeout(5000);
        List<Map<String, Object>> mapList = transactionHBaseDao.queryList(query);

        TransactionQuery transactionQuery = HbaseQueryConverter.convert(query);
        FutureTask<Map<String, String>> storeStaffTask = businessService.getStoreStaffFutureTask(transactionQuery);
        if (!org.springframework.util.CollectionUtils.isEmpty(mapList)) {
            CarrierItem carrierItem = new CarrierItem(TraceContext.traceId());
            final Map<String, String> storeStaffMap = Maps.newHashMap();
            businessService.getStoreStaff(transactionQuery, storeStaffTask, storeStaffMap);
            List<TransactionVo> transactionVos = new ArrayList<>(mapList.size());
            List<FutureTask<TransactionVo>> asyncTaskList = new ArrayList<>(mapList.size());
            FutureTask<TransactionVo> futureTask;
            Map<Integer, Object> payWayNames = businessService.getAllPayWayName();
            for (Map<String, Object> t : mapList) {
                futureTask = new FutureTask<>(new BaseAsyncTask<TransactionVo>() {
                    @Override
                    public TransactionVo run() {
                        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
                        TransactionVo transactionVo = accountBookBaseService.buildTransactionVo(t, transactionQuery.getLoadTypes(), payWayNames, false);
                        if (transactionQuery.getLoadTypes().contains(LoadType.TERMINAL_CODE) && transactionQuery.getLoadTypes().contains(LoadType.STORE_STAFF)) {
                            operatorService.setOperator(transactionVo, storeStaffMap);
                        }
                        t.put(Transaction.OPERATOR, transactionVo.getOperator());
                        return transactionVo;

                    }
                });
                ExecutorServiceSupport.getDefaultExecutorService().submit(futureTask);
                asyncTaskList.add(futureTask);
            }

            for (FutureTask<TransactionVo> voFutureTask : asyncTaskList) {
                try {
                    transactionVos.add(voFutureTask.get());
                } catch (Exception e) {
                    log.error("转换TransactionVo出错", e);
                    throw new RuntimeException(e);
                }
            }
            Set<Integer> chargePaywayCodes = businessService.getAllChargePayWay().keySet();
            return transactionVos.stream()
                    .map(vo -> buildMerchantTradesResponse(vo, payWayNames, chargePaywayCodes))
                    .collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 商户交易返回
     *
     * @param transactionVo
     * @return
     */
    private MerchantTradesResponse buildMerchantTradesResponse(TransactionVo transactionVo, Map<Integer, Object> payWayNames, Set<Integer> chargePaywayCodes){
        MerchantTradesResponse merchantTradesResponse = new MerchantTradesResponse();
        merchantTradesResponse.setTsn(transactionVo.getTsn())
                .setMerchantId(transactionVo.getMerchantId())
                .setStoreId(transactionVo.getStoreId())
                .setOperatorName(transactionVo.getOperator())
                .setPayWay(transactionVo.getPayWay())
                .setBuyerUid(transactionVo.getPayId())
                .setAppId(getAppIdByBuyerUid(transactionVo.getPayWay(), transactionVo.getPayId()))
                .setBuyerLogin(transactionVo.getPayAccount())
                .setOriginalAmount(transactionVo.getOriginalAmount())
                .setMchFavorableAmount(transactionVo.getOuterDiscount())
                .setType(transactionVo.getType())
                .setCtime(transactionVo.getCtime());
        String productFlag = transactionVo.getProductFlag();
        int payWay = transactionVo.getPayWay();
        //记账支付方式
        merchantTradesResponse.setCategory(TransactionCategory.getByPayWayAndProductFlag(payWay, productFlag, chargePaywayCodes).getCode());
        //payway dode 和 中文名称的对应关系
        merchantTradesResponse.setTag(transactionVo.getTagName(payWayNames));
        return merchantTradesResponse;
    }

    /**
     * 根据buyerUid获取微信公众号AppId
     *
     * @param payWay
     * @param buyerUid
     * @return
     */
    private String getAppIdByBuyerUid(int payWay, String buyerUid){
        String appId = null;
        if (Order.PAYWAY_WEIXIN == payWay) {
            String appIdKey = CommonConstant.APP_ID_MAP.keySet().stream().filter(s -> buyerUid != null && buyerUid.startsWith(s)).findFirst().orElse("");
            if (StringUtils.isNotBlank(appIdKey)) {
                appId = CommonConstant.APP_ID_MAP.get(appIdKey);
            }
        }
        return appId;
    }

    /**
     * buildTransactionHBaseQuery
     *
     * @param request CustomerSummaryRequest
     * @return TransactionHBaseQuery
     */
    private TransactionHBaseQuery buildTransactionHBaseQuery(String merchantId, SumCustomerDetailRequest request) {
        TransactionHBaseQuery query = new TransactionHBaseQuery();
        query.setQueryEs(NeedESQueryUtil.byStartTimeAndEndTime(merchantId, request.getStartTime(), request.getEndTime()));
        query.setMerchantIds(Lists.newArrayList(merchantId));
        query.setStoreIds(Lists.newArrayList(request.getStoreId()));
        query.setPayWays(Lists.newArrayList(request.getPayWay()));
        query.setBuyerUids(Lists.newArrayList(request.getBuyerUid()));
        query.setStartTime(request.getStartTime());
        query.setEndTime(request.getEndTime());
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
        statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(Transaction.STATUS_SUCCESS));
        statusTypeSubPayWayQuery.setTypeList(TransactionTypeRelatedUtil.PAY_TYPE_FOR_CUSTOMER_QUERY);
        query.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
        return query;
    }

    /**
     * buildTransactionHBaseQuery
     *
     * @param merchantId merchantId
     * @param request    ListMerchantTradesRequest
     * @return TransactionHBaseQuery
     */
    private static TransactionHBaseQuery buildTransactionHBaseQuery(String merchantId, ListMerchantTradesRequest request) {
        TransactionHBaseQuery query = new TransactionHBaseQuery();
        query.setQueryEs(NeedESQueryUtil.byStartTimeAndEndTime(merchantId, request.getStartTime(), request.getEndTime()));
        query.setMerchantIds(Lists.newArrayList(merchantId));
        if (CollectionUtils.isNotEmpty(request.getStoreIds())) {
            Set<String> storeIdSet = new HashSet<>(request.getStoreIds());
            storeIdSet.remove(null);
            storeIdSet.remove("");
            if (CollectionUtils.isNotEmpty(storeIdSet)) {
                query.setStoreIds(new ArrayList<>(storeIdSet));
            }
        }
        if (request.getPayWay() != null) {
            query.setPayWays(Lists.newArrayList(request.getPayWay()));
        }
        if (StringUtils.isNotEmpty(request.getBuyerUid())) {
            query.setBuyerUids(Lists.newArrayList(request.getBuyerUid()));
        } else {
            query.setBuyerUidNotEmpty(true);
        }
        query.setStartTime(request.getStartTime());
        query.setEndTime(request.getEndTime());

        query.setOffset((request.getPageNum() - 1) * request.getPageSize());
        query.setLimit(request.getPageSize());
        if (request.getOrderBy() != null) {
            query.getOrderBys().add(ORDER_BY_ENUM_ORDER_BY_MAP.get(request.getOrderBy()));
        }

        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
        statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(Transaction.STATUS_SUCCESS));
        statusTypeSubPayWayQuery.setTypeList(TransactionTypeRelatedUtil.PAY_TYPE_FOR_CUSTOMER_QUERY);
        query.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
        return query;
    }

    private static void checkRequest(SumCustomerDetailRequest request) {
        if (request.getStartTime() >= request.getEndTime()) {
            throw ErrorMessageEnum.START_TIME_MUST_BE_EARLIER_THAN_END_TIME.getBizException();
        }

        Days days = Days.daysBetween(LocalDateTime.fromDateFields(new Date(request.getStartTime())).plusDays(92),
                LocalDateTime.fromDateFields(new Date(request.getEndTime())));
        if (days.getDays() > 0) {
            throw ErrorMessageEnum.ILLEGAL_TIME_SPAN.getBizException();
        }
    }

    private static void checkRequest(ListMerchantTradesRequest request) {
        if (request.getPageNum() == null || request.getPageNum() <= 0) {
            request.setPageNum(1);
        }
        if (request.getPageSize() == null || request.getPageSize() <= 0) {
            request.setPageSize(10);
        }
        if (request.getPageSize() > 200) {
            throw ErrorMessageEnum.PAGE_SIZE_TOO_LARGE_200.getBizException();
        }

        if (request.getEndTime() == null) {
            request.setEndTime(System.currentTimeMillis());
        }
        if (request.getStartTime() == null) {
            request.setStartTime(LocalDateTime.fromDateFields(new Date(request.getEndTime())).minusDays(1).toDate().getTime());
        }

        if (request.getStartTime() >= request.getEndTime()) {
            throw ErrorMessageEnum.START_TIME_MUST_BE_EARLIER_THAN_END_TIME.getBizException();
        }
        Days days = Days.daysBetween(LocalDateTime.fromDateFields(new Date(request.getStartTime())).plusDays(92),
                LocalDateTime.fromDateFields(new Date(request.getEndTime())));
        if (days.getDays() > 0) {
            throw ErrorMessageEnum.ILLEGAL_TIME_SPAN.getBizException();
        }
    }
}
