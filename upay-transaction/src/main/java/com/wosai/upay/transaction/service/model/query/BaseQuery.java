package com.wosai.upay.transaction.service.model.query;

import com.google.common.collect.Lists;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.transaction.util.SolrHBaseUtils.SolrPartition;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public abstract class BaseQuery {

    private SolrPartition solrPartition;

    private boolean queryEs = false;
    private boolean queryCashDesk = false;
    private Long startTime;

    private Long endTime;

    @Deprecated
    private Long createdAtStart;

    @Deprecated
    private Long createdAtEnd;

    @Deprecated
    private Long backUpTime;

    private Integer limit = 15;

    private int offset;

    private Set<String> filterColumns;

    private String groupByKey;

    private int hBaseTimeout = 1000;

    private Integer solrTimeout;

    public SolrPartition getSolrPartition() {
        return solrPartition;
    }

    public void setSolrPartition(SolrPartition solrPartition) {
        this.solrPartition = solrPartition;
    }

    @Deprecated
    private OrderBy orderBy = new OrderBy("ctime", OrderBy.OrderType.DESC);

    private List<OrderBy> orderBys = Lists.newArrayList();

    @Deprecated
    public Long getCreatedAtStart() {
        return createdAtStart;
    }

    @Deprecated
    public void setCreatedAtStart(Long createdAtStart) {
        this.createdAtStart = createdAtStart;
    }

    @Deprecated
    public Long getCreatedAtEnd() {
        return createdAtEnd;
    }

    @Deprecated
    public void setCreatedAtEnd(Long createdAtEnd) {
        this.createdAtEnd = createdAtEnd;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    @Deprecated
    public Long getBackUpTime() {
        return backUpTime;
    }

    @Deprecated
    public void setBackUpTime(Long backUpTime) {
        this.backUpTime = backUpTime;
    }

    @Deprecated
    public OrderBy getOrderBy() {
        return orderBy;
    }

    @Deprecated
    public void setOrderBy(OrderBy orderBy) {
        this.orderBy = orderBy;
    }

    public Integer getOffset() {
        return offset;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public List<OrderBy> getOrderBys() {
        return orderBys;
    }

    public Set<String> getFilterColumns() {
        return filterColumns;
    }

    public void setFilterColumns(Set<String> filterColumns) {
        this.filterColumns = filterColumns;
    }

    public String getGroupByKey() {
        return groupByKey;
    }

    public void setGroupByKey(String groupByKey) {
        this.groupByKey = groupByKey;
    }

    public int gethBaseTimeout() {
        return hBaseTimeout;
    }

    public void sethBaseTimeout(int hBaseTimeout) {
        if(hBaseTimeout <= 0){
           return;
        }
        this.hBaseTimeout = hBaseTimeout;
    }

    public Integer getSolrTimeout() {
        return solrTimeout;
    }

    public void setSolrTimeout(Integer solrTimeout) {
        if (solrTimeout != null && solrTimeout > 0) {
            this.solrTimeout = solrTimeout;
        }
    }


    public boolean isQueryEs() {
        return queryEs;
    }

    public void setQueryEs(boolean queryEs) {
        this.queryEs = queryEs;
    }

    public boolean isQueryCashDesk() {
        return queryCashDesk;
    }

    public void setQueryCashDesk(boolean queryCashDesk) {
        this.queryCashDesk = queryCashDesk;
    }

}
