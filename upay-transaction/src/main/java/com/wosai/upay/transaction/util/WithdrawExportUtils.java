package com.wosai.upay.transaction.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.clearance.service.ClearanceService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.model.Withdraw;
import com.wosai.upay.transaction.constant.CommonConstant;
import org.apache.commons.collections.MapUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class WithdrawExportUtils {

    @Resource
    ClearanceService clearanceService;

    public void appendWithdrawHeader(Map context,SXSSFSheet sheet){
        String merchantSn = BeanUtil.getPropString(context, "merchant_sn");
        String groupSn = BeanUtil.getPropString(context, "group_sn");
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        addLine(sheet,"起始日期",String.format(
                "[%s]--[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()));
        addLine(sheet, "对账单编号", String.format(
                "SQB%s-%s-%s",
                !StringUtil.empty(groupSn) ? groupSn : merchantSn,
                CommonConstant.DAY_SDF_YYYYMMDD.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DAY_SDF_YYYYMMDD.get().format(end) + CommonConstant.TIME_ZONE_TL.get()));

        addLine(sheet);
        addLine(sheet,"结算时间","结算金额","手续费","收款人","收款账户","收款账户名","商户号","商户名称","结算方式","结算状态","备注");
    }

    public void appendWithdrawDetail(List<Map<String,Object>> withdraws, SXSSFSheet sheet){
        for (Map<String, Object> map : withdraws) {
            String merchantSn = MapUtils.getString(map, ConstantUtil.KEY_MERCHANT_SN);
            List rowValue = new ArrayList();
            int op_check_status = BeanUtil.getPropInt(map, "op_check_status");
            String withdrawStatus = "";
            if (op_check_status == Withdraw.WITHDRAW_OP_CHECK_STATUS_PAY_SUCCESS || op_check_status == Withdraw.WITHDRAW_OP_CHECK_STATUS_PAY_FAILED){
                if (op_check_status == Withdraw.WITHDRAW_OP_CHECK_STATUS_PAY_SUCCESS){
                    withdrawStatus = "打款成功";
                }else if (op_check_status == Withdraw.WITHDRAW_OP_CHECK_STATUS_PAY_FAILED){
                    withdrawStatus = "打款失败";
                }
                int withdrawMode = BeanUtil.getPropInt(map,"withdraw_mode");
                //日期 D0结算模式
                if (withdrawMode == 4){
                    rowValue.add(CommonConstant.DETAIL_SDF2.get().format(map.get(DaoConstants.CTIME))+" 06:00");
                }else {
                    rowValue.add(CommonConstant.DETAIL_SDF1.get().format(map.get(DaoConstants.CTIME)));
                }
                //结算金额
                BigDecimal amount = BigDecimal.valueOf(BeanUtil.getPropInt(map, "amount", 0) / 100.0);
                String amountStr = amount.setScale(2, RoundingMode.HALF_UP).toString();
                rowValue.add(amountStr);
                //手续费
                BigDecimal fee = BigDecimal.valueOf(BeanUtil.getPropInt(map, "service_charge", 0) / 100.0);
                String feeStr = fee.setScale(2, RoundingMode.HALF_UP).toString();
                rowValue.add(feeStr);
                //收款人
                rowValue.add(BeanUtil.getPropString(map, "card_holder"));
                //收款账户
                rowValue.add(BeanUtil.getPropString(map, "card_no"));
                //收款账户名
                rowValue.add(BeanUtil.getPropString(map, "bank_name"));
                //商户号
                rowValue.add(merchantSn);
                //商户名称
                rowValue.add(BeanUtil.getPropString(map, "merchant_name"));
                //结算方式
                int type = BeanUtil.getPropInt(map, "type");
                if (0 == type){
                    rowValue.add("普通到账");
                }else if (1 == type) {
                    rowValue.add("快速到账");
                }else if (2 == type) {
                     rowValue.add("T+1到账");
                }else {
                    rowValue.add("");
                }
                //结算状态
                rowValue.add(withdrawStatus);
                SheetHelper.appendLine(sheet, rowValue);
            }
        }

    }

    public void updateWithdrawSummaryInfo(List<Map<String, Object>> withdrawList, Map summary){
        //type=0 明日到账，type=1 实时到账。
        //自动结算
        long autoWithdraw = BeanUtil.getPropInt(summary, "autoWithdraw");
        //普通到账结算
        long generalWithdraw = BeanUtil.getPropInt(summary, "generalWithdraw");
        //快速到账结算
        long fastWithdraw = BeanUtil.getPropInt(summary,"fastWithdraw");
        //结算总金额
        for (Map<String, Object> map : withdrawList) {
            int opCheckStatus = BeanUtil.getPropInt(map, "op_check_status");
            if (opCheckStatus == Withdraw.WITHDRAW_OP_CHECK_STATUS_PAY_SUCCESS || opCheckStatus == Withdraw.WITHDRAW_OP_CHECK_STATUS_PAY_FAILED){
                final long amount = BeanUtil.getPropLong(map, "amount");
                int type = BeanUtil.getPropInt(map, "type");
                int withdrawMode = BeanUtil.getPropInt(map,"withdraw_mode");
                if (type == 0 && withdrawMode == 1){
                    generalWithdraw += amount;
                }
                if (type == 1 && withdrawMode == 1) {
                    fastWithdraw += amount;
                }
                if (withdrawMode == 2 || withdrawMode == 3 || withdrawMode == 4){
                    autoWithdraw += amount;
                }
                summary.put("autoWithdraw",autoWithdraw);
                summary.put("generalWithdraw", generalWithdraw);
                summary.put("fastWithdraw", fastWithdraw);
            }
        }
        summary.put("withdrawSum", autoWithdraw + generalWithdraw + fastWithdraw);
    }

    private void addLine(SXSSFSheet sxssfSheet, String... s) {
        SheetHelper.appendLine(sxssfSheet, Arrays.asList(s));
    }

    public void appendWithdrawSummary(Map summary, SXSSFSheet sheet){
        addLine(sheet);
        BigDecimal autoWithdraw = BigDecimal.valueOf(BeanUtil.getPropLong(summary,"autoWithdraw",0l)/100.0);
        BigDecimal generalWithdraw = BigDecimal.valueOf(BeanUtil.getPropLong(summary,"generalWithdraw",0l)/100.0);
        BigDecimal fastWithdraw = BigDecimal.valueOf(BeanUtil.getPropLong(summary,"fastWithdraw",0l)/100.0);
        BigDecimal withdrawSum = BigDecimal.valueOf(BeanUtil.getPropLong(summary,"withdrawSum",0l)/100.0);
        addLine(sheet, "自动结算总金额", autoWithdraw.setScale(2, RoundingMode.HALF_UP).toString());
        addLine(sheet, "普通到账结算总金额", generalWithdraw.setScale(2, RoundingMode.HALF_UP).toString());
        addLine(sheet, "快速到账结算总金额", fastWithdraw.setScale(2, RoundingMode.HALF_UP).toString());
        addLine(sheet, "结算总金额", withdrawSum.setScale(2, RoundingMode.HALF_UP).toString());
    }

    public List<Map<String, Object>> retryGetMerchantWithdrawList(PageInfo pageInfo, Map queryFilter) throws Exception{
        int count = 10;
        Exception exception = null;
        while (count > 0) {
            count--;
            try {
                ListResult result = clearanceService.findWithdraws(pageInfo, queryFilter);
                return (List) result.getRecords();
            } catch (Exception e) {
                exception = e;
                try {
                    Thread.sleep(15 * 1000);
                } catch (InterruptedException iex) {
                }
            }
        }
        throw exception;
    }
}
