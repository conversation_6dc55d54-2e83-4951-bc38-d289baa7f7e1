package com.wosai.upay.transaction.util;

import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.constant.PageInfoConst;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;
import com.wosai.upay.transaction.exception.BizException;

import javax.annotation.Nullable;

/**
 * PageInfoChecker
 *
 * <AUTHOR>
 */
public final class PageInfoChecker {

    /**
     * 检查分页参数是否有效
     *
     * @param pageInfo {@link PageInfo}
     * @throws BizException {@link ErrorMessageEnum#PAGE_SIZE_TOO_LARGE} 业务异常
     */
    public static void check(@Nullable PageInfo pageInfo) {
        if (pageInfo == null || pageInfo.getPageSize() == null) {
            return;
        }

        if (pageInfo.getPageSize() > PageInfoConst.PAGE_SIZE_LIMIT) {
            throw ErrorMessageEnum.PAGE_SIZE_TOO_LARGE.getBizException();
        }
    }

}
