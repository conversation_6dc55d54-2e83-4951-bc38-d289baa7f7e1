package com.wosai.upay.transaction.service.model.query;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderHBaseQuery extends BaseQuery{

    private String orderSn;

    private List<String> storeIds;

    private List<String> merchantIds;

    private List<String> terminalIds;

    private String clientSn;

    private List<Integer> statusList;

    private List<Integer> providers;

    private List<Integer> payWays;

    private List<Integer> subPayWays;

    private Long minTotalAmount;

    private Long maxTotalAmount;

    private String tradeNo;

    private List<String> buyerUids;

    private List<String> buyerLogins;

    private boolean providerIsNull;
    
    private List<String> orderSns;

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public List<String> getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(List<String> storeIds) {
        this.storeIds = storeIds;
    }

    public List<String> getMerchantIds() {
        return merchantIds;
    }

    public void setMerchantIds(List<String> merchantIds) {
        this.merchantIds = merchantIds;
    }

    public List<String> getTerminalIds() {
        return terminalIds;
    }

    public void setTerminalIds(List<String> terminalIds) {
        this.terminalIds = terminalIds;
    }

    public String getClientSn() {
        return clientSn;
    }

    public void setClientSn(String clientSn) {
        this.clientSn = clientSn;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public List<Integer> getProviders() {
        return providers;
    }

    public void setProviders(List<Integer> providers) {
        this.providers = providers;
    }

    public List<Integer> getPayWays() {
        return payWays;
    }

    public void setPayWays(List<Integer> payWays) {
        this.payWays = payWays;
    }

    public List<Integer> getSubPayWays() {
        return subPayWays;
    }

    public void setSubPayWays(List<Integer> subPayWays) {
        this.subPayWays = subPayWays;
    }

    public Long getMinTotalAmount() {
        return minTotalAmount;
    }

    public void setMinTotalAmount(Long minTotalAmount) {
        this.minTotalAmount = minTotalAmount;
    }

    public Long getMaxTotalAmount() {
        return maxTotalAmount;
    }

    public void setMaxTotalAmount(Long maxTotalAmount) {
        this.maxTotalAmount = maxTotalAmount;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public List<String> getBuyerUids() {
        return buyerUids;
    }

    public void setBuyerUids(List<String> buyerUids) {
        this.buyerUids = buyerUids;
    }

    public List<String> getBuyerLogins() {
        return buyerLogins;
    }

    public void setBuyerLogins(List<String> buyerLogins) {
        this.buyerLogins = buyerLogins;
    }

    public boolean getProviderIsNull() {
        return providerIsNull;
    }

    public void setProviderIsNull(boolean providerIsNull) {
        this.providerIsNull = providerIsNull;
    }

    public List<String> getOrderSns() {
        return orderSns;
    }

    public void setOrderSns(List<String> orderSns) {
        this.orderSns = orderSns;
    }
}
