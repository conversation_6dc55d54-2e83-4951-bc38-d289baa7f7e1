package com.wosai.upay.transaction.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.model.Order;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.model.Transaction;

public class TransactionConstant {

    public static final String HONGBAO_WOSAI_AMOUNT = "hongbao_wosai_amount";
    public static final String HONGBAO_WOSAI_MCH_AMOUNT = "hongbao_wosai_mch_amount";
    public static final String DISCOUNT_WOSAI_AMOUNT = "discount_wosai_amount";
    public static final String DISCOUNT_WOSAI_MCH_AMOUNT = "discount_wosai_mch_amount";
    public static final String DISCOUNT_CHANNEL = "discount_channel";
    public static final String DISCOUNT_CHANNEL_MCH = "discount_channel_mch";
    public static final String DISCOUNT_CHANNEL_MCH_TOP_UP = "discount_channel_mch_top_up";
    public static final String HONGBAO_CHANNEL = "hongbao_channel";
    public static final String HONGBAO_CHANNEL_MCH = "hongbao_channel_mch";
    public static final String HONGBAO_CHANNEL_MCH_TOP_UP = "hongbao_channel_mch_top_up";
    public static final String DISCOUNT_CHANNEL_AMOUNT = "discount_channel_amount";
    public static final String DISCOUNT_CHANNEL_MCH_AMOUNT = "discount_channel_mch_amount";
    public static final String DISCOUNT_CHANNEL_MCH_TOP_UP_AMOUNT = "discount_channel_mch_top_up_amount";
    public static final String HONGBAO_CHANNEL_AMOUNT = "hongbao_channel_amount";
    public static final String HONGBAO_CHANNEL_MCH_AMOUNT = "hongbao_channel_mch_amount";
    public static final String HONGBAO_CHANNEL_MCH_TOP_UP_AMOUNTS = "hongbao_channel_mch_top_up_amounts";
    public static final String IS_LIQUIDATION_NEXT_DAY = "is_liquidation_next_day";
    public static final String HONGBAO_WOSAI_TOTAL = "hongbao_wosai_total";
    public static final String NET_HONGBAO_WOSAI = "net_hongbao_wosai";
    public static final Object HONGBAO_WOSAI_MCH_TOTAL = "hongbao_wosai_mch_total";
    public static final String NET_HONGBAO_WOSAI_MCH = "net_hongbao_wosai_mch";
    public static final String DISCOUNT_WOSAI_TOTAL = "discount_wosai_total";
    public static final String NET_DISCOUNT_WOSAI = "net_discount_wosai";
    public static final String DISCOUNT_WOSAI_MCH_TOTAL = "discount_wosai_mch_total";
    public static final String NET_DISCOUNT_WOSAI_MCH = "net_discount_wosai_mch";
    public static final String NET_DISCOUNT_CHANNEL = "net_discount_channel";
    public static final String NET_DISCOUNT_CHANNEL_MCH = "net_discount_channel_mch";
    public static final String NET_DISCOUNT_CHANNEL_MCH_TOP_UP = "net_discount_channel_mch_top_up";
    public static final String NET_HONGBAO_CHANNEL_MCH = "net_hongbao_channel_mch";
    public static final String NET_HONGBAO_CHANNEL_MCH_TOP_UP = "net_hongbao_channel_mch_top_up";
    public static final String DISCOUNT_CHANNEL_TOTAL = "discount_channel_total";
    public static final String DISCOUNT_CHANNEL_MCH_TOTAL = "discount_channel_mch_total";
    public static final String HONGBAO_CHANNEL_TOTAL = "hongbao_channel_total";
    public static final String HONGBAO_CHANNEL_MCH_TOTAL = "hongbao_channel_mch_total";
    public static final String HONGBAO_CHANNEL_MCH_TOP_UP_TOTAL = "hongbao_channel_mch_top_up_total";
    public static final String TRADE_FEE_RATE = "trade_fee_rate";
    public static final String REMOVE_EXTRAOUTFIELDS = "removeExtraOutFields";
    public static final String SQB_REFUND_FLAG = "sqb_refund_flag";
    public static final String SQB_CHARGE_SOURCE = "sqb_charge_source";

    public static final String APPEND_SQB_FQ_SELLER_SERVICE_CHARGE = "sqb_fq_seller_service_charge";           // 格外补充参数
    
    public static final Set<String> QUERY_QRDER_COLUMNS_PC_DESKTOP = Sets.newHashSet(
            DaoConstants.ID, DaoConstants.CTIME, DaoConstants.MTIME,
            Order.SN, Order.CLIENT_SN, Order.ORIGINAL_TOTAL, Order.NET_ORIGINAL,
            Order.STATUS, Order.PAYWAY, Order.OPERATOR
    );

    public static final Set<String> QUERY_TRANSACTION_COLUMNS_PC_DESKTOP = Sets.newHashSet(
            DaoConstants.ID, DaoConstants.CTIME, DaoConstants.MTIME, Transaction.TYPE, Transaction.STATUS, Transaction.OPERATOR,
            Transaction.ORIGINAL_AMOUNT, Transaction.EFFECTIVE_AMOUNT, Transaction.ORDER_SN, Transaction.CLIENT_TSN, Transaction.PAYWAY
    );
    
    public static final Set<String> QUERY_ADD_TRANSACTION_COLUMNS = Sets.newHashSet(Transaction.ORDER_SN, Transaction.CONFIG_SNAPSHOT, Transaction.EXTRA_OUT_FIELDS, Transaction.EXTRA_PARAMS, Transaction.BUYER_UID, Transaction.BUYER_LOGIN, Transaction.PAYWAY);

    public static final List<String> UPAY_ORDER_DEFAULT_ORDER_QUERY_FLAG = Arrays.asList(
            QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
    );
    public static final List<String> UPAY_ORDER_DEFAULT_TRANSACTION_QUERY_FLAG = Arrays.asList(
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO
        );

    // 花呗分期商户贴息（信用卡分期暂时没有商家贴息）
    public static final String PRODUCT_FLAG_HBFQ_DISCOUNT = "ad";

    // 明细查询流水类型
    public static final Set<Integer> QUERY_TRANSACTION_TYPES = new HashSet(Arrays.asList(
            TransactionType.PAYMENT.getCode(),
            TransactionType.REFUND.getCode(),
            TransactionType.DEPOSIT_CONSUME.getCode(),
            TransactionType.DEPOSIT_FREEZE.getCode(),
            TransactionType.CANCEL.getCode(),
            TransactionType.CHARGE.getCode(),
            TransactionType.CHARGE_REFUND.getCode(),
            TransactionType.ORDER_TAKE.getCode(),
            TransactionType.ORDER_TAKE_REFUND.getCode(),
            TransactionType.STORE_PAY.getCode(),
            TransactionType.STORE_REFUND.getCode(),
            TransactionType.STORE_IN.getCode(),
            TransactionType.IN_REFUND.getCode(),
            TransactionType.DEPOSIT_CONSUME_CANCEL.getCode()
     ));
}
