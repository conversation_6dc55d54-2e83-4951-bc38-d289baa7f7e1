package com.wosai.upay.transaction.util;

import lombok.SneakyThrows;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Table;

public class TablePool {

    private GenericObjectPool<Table> tableGenericObjectPool;

    public TablePool(Connection connection, TableName tableName, int minIdle, int maxTotal) {
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMinIdle(minIdle);
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setBlockWhenExhausted(true);
        poolConfig.setMaxWaitMillis(5000);
        tableGenericObjectPool = new GenericObjectPool<>(new TableFactory(connection, tableName), poolConfig);
        Runtime.getRuntime().addShutdownHook(new Thread(()->{
            tableGenericObjectPool.clear();
        }));

    }


    @SneakyThrows
    public Table borrowObject(){
        return tableGenericObjectPool.borrowObject();
    }

    public void returnObject(Table table) {
        tableGenericObjectPool.returnObject(table);
    }
}
