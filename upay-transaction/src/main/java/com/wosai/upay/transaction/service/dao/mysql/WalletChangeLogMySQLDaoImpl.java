package com.wosai.upay.transaction.service.dao.mysql;

import com.wosai.upay.transaction.service.model.po.WalletChangeLogPo;
import com.wosai.upay.transaction.service.model.query.WalletChangeLogQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Repository
public class WalletChangeLogMySQLDaoImpl implements WalletChangeLogMySQLDao {

    private static final Logger logger = LoggerFactory.getLogger(WalletChangeLogMySQLDaoImpl.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final String BASE_WALLET_CHANGE_LOG_FIELDS = 
        "id, merchant_id, store_id, terminal_id, operator, wallet_type, wallet_id, " +
        "change_type, amount, balance, reason, related_tsn, related_order_sn, " +
        "buyer_uid, buyer_login, extra, ctime, mtime";

    private final RowMapper<WalletChangeLogPo> walletChangeLogRowMapper = new WalletChangeLogRowMapper();

    @Override
    public List<WalletChangeLogPo> queryForList(WalletChangeLogQuery query) {
        StringBuilder sql = new StringBuilder("SELECT ").append(BASE_WALLET_CHANGE_LOG_FIELDS)
                .append(" FROM wallet_change_log WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        buildWhereClause(sql, params, query);
        
        if (query.getOffset() != null && query.getLimit() != null) {
            sql.append(" LIMIT ?, ?");
            params.add(query.getOffset());
            params.add(query.getLimit());
        }
        
        sql.append(" ORDER BY ctime DESC");
        
        logger.debug("Querying wallet change logs with SQL: {}", sql);
        return jdbcTemplate.query(sql.toString(), params.toArray(), walletChangeLogRowMapper);
    }

    @Override
    public long count(WalletChangeLogQuery query) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(1) FROM wallet_change_log WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        buildWhereClause(sql, params, query);
        
        logger.debug("Counting wallet change logs with SQL: {}", sql);
        return jdbcTemplate.queryForObject(sql.toString(), params.toArray(), Long.class);
    }

    private void buildWhereClause(StringBuilder sql, List<Object> params, WalletChangeLogQuery query) {


        sql.append(" AND deleted = 0");
    }

    private static class WalletChangeLogRowMapper implements RowMapper<WalletChangeLogPo> {
        @Override
        public WalletChangeLogPo mapRow(ResultSet rs, int rowNum) throws SQLException {
            WalletChangeLogPo po = new WalletChangeLogPo();
            
            po.setId(rs.getString("id"));
            po.setMerchantId(rs.getString("merchant_id"));

            po.setCtime(rs.getLong("ctime"));
            po.setMtime(rs.getLong("mtime"));
            
            return po;
        }
    }

    private static class StringUtils {
        public static boolean hasLength(String str) {
            return str != null && !str.trim().isEmpty();
        }
    }
}