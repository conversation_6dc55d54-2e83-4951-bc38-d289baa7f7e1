<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd



          http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <!-- ========================= DATASOURCE DEFINITION ========================= -->

    <!-- DataSource configuration for Apache Commons DBCP. -->
    <bean id="upayDatasource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close"
          p:maxActive="${db.maxActive}" p:minIdle="${db.minIdle}" p:maxIdle="${db.maxActive}"
          p:driverClassName="${jdbc_upay.driverClassName}" p:url="${jdbc_upay.url}"
          p:username="${jdbc_upay.username}" p:password="${jdbc_upay.password}"
          p:timeBetweenEvictionRunsMillis="${jdbc_upay.connection.eviction.interval}"/>


    <bean id="upaySwipeDatasource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close"
          p:maxActive="${db.maxActive}" p:minIdle="${db.minIdle}" p:maxIdle="${db.maxActive}"
          p:driverClassName="${upay_swipe.driverClassName}" p:url="${upay_swipe.url}"
          p:username="${upay_swipe.username}" p:password="${upay_swipe.password}"
          p:timeBetweenEvictionRunsMillis="${upay_swipe.connection.eviction.interval}"/>

    <bean id="statementDatasource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close"
          p:maxActive="${db.maxActive}" p:minIdle="${db.minIdle}" p:maxIdle="${db.maxActive}"
          p:driverClassName="${jdbc_transaction.driverClassName}" p:url="${jdbc_transaction.url}"
          p:username="${jdbc_transaction.username}" p:password="${jdbc_transaction.password}"
          p:timeBetweenEvictionRunsMillis="${jdbc_transaction.connection.eviction.interval}"/>


    <bean id="upayBackDatasource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close"
          p:maxActive="${db.maxActive}" p:minIdle="${db.minIdle}" p:maxIdle="${db.maxActive}"
          p:driverClassName="${jdbc_upay_back.driverClassName}" p:url="${jdbc_upay_back.url}"
          p:username="${jdbc_upay_back.username}" p:password="${jdbc_upay_back.password}"
          p:timeBetweenEvictionRunsMillis="${jdbc_upay_back.connection.eviction.interval}"/>

    <util:list id="initSqls">
        <value>set names utf8mb4</value>
    </util:list>

</beans>
