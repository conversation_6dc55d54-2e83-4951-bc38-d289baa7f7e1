<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- ========================= RESOURCE DEFINITIONS ========================= -->

    <!-- import the dataSource definition -->
    <import resource="datasource-config.xml"/>



    <bean id="upayJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg ref="upayDatasource"/>
    </bean>

    <bean id="upaySwipeJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg ref="upaySwipeDatasource"/>
    </bean>

    <bean id="statementJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg ref="statementDatasource"/>
    </bean>
    
    <bean id="upayBackJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg ref="upayBackDatasource"/>
    </bean>


    <!--upay-->

    <bean id="transactionDao" class="com.wosai.upay.common.dao.JsonBlobAwareDao">
        <constructor-arg index="0">
            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                <constructor-arg index="0" value="transaction"/>
                <constructor-arg index="1" ref="upayJdbcTemplate"/>
                <constructor-arg index="2">
                    <list></list>
                </constructor-arg>
                <constructor-arg index="3" value="false"></constructor-arg>
            </bean>
        </constructor-arg>
        <constructor-arg index="1">
            <set>
                <value>items</value>
                <value>extra_params</value>
                <value>extra_out_fields</value>
                <value>extended_params</value>
                <value>reflect</value>
                <value>config_snapshot</value>
                <value>biz_error_code</value>
                <value>provider_error_info</value>
            </set>
        </constructor-arg>
    </bean>

    <bean id="transactionSWipeDao" class="com.wosai.upay.common.dao.JsonBlobAwareDao">
        <constructor-arg index="0">
            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                <constructor-arg index="0" value="transaction"/>
                <constructor-arg index="1" ref="upaySwipeJdbcTemplate"/>
                <constructor-arg index="2">
                    <list></list>
                </constructor-arg>
                <constructor-arg index="3" value="false"></constructor-arg>
            </bean>
        </constructor-arg>
        <constructor-arg index="1">
            <set>
                <value>items</value>
                <value>extra_params</value>
                <value>extra_out_fields</value>
                <value>extended_params</value>
                <value>reflect</value>
                <value>config_snapshot</value>
                <value>biz_error_code</value>
                <value>provider_error_info</value>
            </set>
        </constructor-arg>
    </bean>

    <bean id="orderSWipeDao" class="com.wosai.upay.common.dao.JsonBlobAwareDao">
        <constructor-arg index="0">
            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                <constructor-arg index="0" value="order"/>
                <constructor-arg index="1" ref="upaySwipeJdbcTemplate"/>
                <constructor-arg index="2">
                    <list></list>
                </constructor-arg>
                <constructor-arg index="3" value="false"></constructor-arg>
            </bean>
        </constructor-arg>
        <constructor-arg index="1">
            <set>
                <value>items</value>
                <value>net_items</value>
                <value>reflect</value>
            </set>
        </constructor-arg>
    </bean>

    <bean id="orderDao" class="com.wosai.upay.common.dao.JsonBlobAwareDao">
        <constructor-arg index="0">
            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                <constructor-arg index="0" value="order"/>
                <constructor-arg index="1" ref="upayJdbcTemplate"/>
                <constructor-arg index="2">
                    <list></list>
                </constructor-arg>
                <constructor-arg index="3" value="false"></constructor-arg>
            </bean>
        </constructor-arg>
        <constructor-arg index="1">
            <set>
                <value>items</value>
                <value>net_items</value>
                <value>reflect</value>
            </set>
        </constructor-arg>
    </bean>

    <bean id="statementTaskLogDao" class="com.wosai.data.dao.common.TimedDaoBase">
        <constructor-arg>
            <bean class="com.wosai.upay.common.dao.JsonBlobAwareDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="statement_task_log"/>
                        <constructor-arg index="1" ref="statementJdbcTemplate"/>
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <set>
                        <value>payload</value>
                    </set>
                </constructor-arg>
            </bean>
        </constructor-arg>
    </bean>

    <bean id="statementConfigDao" class="com.wosai.data.dao.common.TimedDaoBase">
        <constructor-arg>
            <bean class="com.wosai.upay.common.dao.JsonBlobAwareDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="statement_config"/>
                        <constructor-arg index="1" ref="statementJdbcTemplate"/>
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <set>
                        <value>extra_info</value>
                    </set>
                </constructor-arg>
            </bean>
        </constructor-arg>
    </bean>



    <bean id="statementObjectConfigDao" class="com.wosai.data.dao.common.TimedDaoBase">
        <constructor-arg>
            <bean class="com.wosai.upay.common.dao.JsonBlobAwareDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="statement_object_config"/>
                        <constructor-arg index="1" ref="statementJdbcTemplate"/>
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <set>
                        <value>sheet_params</value>
                        <value>sheet_detail_params</value>
                    </set>
                </constructor-arg>
            </bean>
        </constructor-arg>
    </bean>

</beans>