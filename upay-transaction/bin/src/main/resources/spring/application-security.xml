<?xml version="1.0" encoding="UTF-8"?>

<beans:beans
        xmlns:beans="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- HTTP authentication & authorization -->
    <!--
    <http pattern="/rpc/**" security="none"/>
    -->

    <!--<beans:import resource="ldap-server-${shouqianba.flavor:default}.xml"/>-->

    <!--<beans:bean id="contextSource" class="org.springframework.ldap.core.support.LdapContextSource">-->
    <!--<beans:property name="url" value="${ldap.url}" />-->
    <!--<beans:property name="base" value="${ldap.base}" />-->
    <!--<beans:property name="userDn" value="${ldap.userDn}" />-->
    <!--<beans:property name="password" value="${ldap.password}" />-->
    <!--</beans:bean>-->

    <!--
    <beans:bean id="authenticationSource"
        class="org.springframework.ldap.authentication.DefaultValuesAuthenticationSourceDecorator">
        <beans:property name="target" ref="springSecurityAuthenticationSource" />
        <beans:property name="defaultUser" value="uid=admin,ou=system" />
        <beans:property name="defaultPassword" value="secret" />
    </beans:bean>

    <beans:bean id="springSecurityAuthenticationSource"
        class="org.springframework.security.ldap.authentication.SpringSecurityAuthenticationSource" />
    -->

    <!--<beans:bean id="ldapTemplate" class="org.springframework.ldap.core.LdapTemplate">-->
    <!--<beans:constructor-arg ref="contextSource"></beans:constructor-arg>-->
    <!--</beans:bean>  -->

    <!-- Authenticaton Manager -->
    <!--<authentication-manager alias="authenticationManager">-->
    <!--<ldap-authentication-provider user-search-base="ou=people" user-search-filter="uid={0}" group-search-base="ou=ospauthority" group-search-filter="member={0}"/>-->
    <!--</authentication-manager>-->


    <!-- Service based authorization -->
    <!--
    <global-method-security>
      <protect-pointcut expression="execution(* com.wosai.taiji.service.remote.RemoteAccountService.verify(..))" access="IS_AUTHENTICATED_ANONYMOUSLY"/>
      <protect-pointcut expression="execution(* com.wosai.taiji.service.remote.*Service.*(..))" access="IS_AUTHENTICATED_ANONYMOUSLY"/>
      <protect-pointcut expression="execution(* com.wosai.taiji.service.AccountService.getAccount(..))" access="ROLE_WEB,ROLE_DEVICE"/>
      <protect-pointcut expression="execution(* com.wosai.taiji.service.*Service.*(..))" access="ROLE_DEVICE"/>
    </global-method-security>
    -->


</beans:beans>