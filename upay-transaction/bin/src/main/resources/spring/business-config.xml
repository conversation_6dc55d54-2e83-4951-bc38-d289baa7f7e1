<?xml version="1.0" encoding="UTF-8"?>
<!--
Data and Service layers
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.1.xsd

			   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">


    <bean class="com.wosai.upay.common.helper.UpayMethodValidationPostProcessor">
        <property name="validatedAnnotationType"
                  value="com.wosai.upay.transaction.annotation.CommonTransactionValidated"/>
    </bean>


    <bean id = "dBSelectServiceMethodInterceptor" class="com.wosai.upay.transaction.helper.DBSelectServiceMethodInterceptor"></bean>

    <bean class="com.wosai.upay.transaction.helper.DBSelectPostProcessor">
        <property name="advice" ref="dBSelectServiceMethodInterceptor"/>
    </bean>
    <bean class="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping"/>

    <context:component-scan base-package="com.wosai.upay.transaction.service"/>
    <context:component-scan base-package="com.wosai.upay.transaction.controller"/>

    <bean id="myObjectMapper" class="com.wosai.upay.common.helper.MyObjectMapper"/>

    <bean id="rpcErrorResolver" class="com.googlecode.jsonrpc4j.MultipleErrorResolver">
      <constructor-arg>
          <list value-type="com.googlecode.jsonrpc4j.ErrorResolver">
            <value type="com.wosai.upay.transaction.helper.ExceptionBaseErrorResolver">INSTANCE</value>
            <value type="com.googlecode.jsonrpc4j.DefaultErrorResolver">INSTANCE</value>
          </list>
      </constructor-arg>
  </bean>
    
    <context:property-placeholder location="classpath:spring/flavor-${shouqianba.flavor:vn}.properties"/>

    <bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:spring/flavor-${shouqianba.flavor:vn}.properties</value>
                <value>classpath:spring/avatar.properties</value>
            </list>
        </property>
    </bean>

    <import resource="jdbc-dao-config.xml"/>

    <import resource="redis-config.xml"/>
	<import resource="classpath*:/wosai-tracing.xml"/>
	<import resource="classpath*:/wosai-tracing-redis.xml"/>
	
    <bean id="springContextHolder" class="com.wosai.upay.common.util.SpringContextHolder" lazy-init="false"/>

    <bean id="actionExecutor" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="20"/>
    </bean>

    <bean class="com.wosai.upay.transaction.repository.DataRepository">
        <property name="orderDao" ref="orderDao"></property>
        <property name="transactionDao" ref="transactionDao"></property>
        <property name="orderSWipeDao" ref="orderSWipeDao"></property>
        <property name="transactionSWipeDao" ref="transactionSWipeDao"></property>
        <property name="statementTaskLogDao" ref="statementTaskLogDao"></property>
        <property name="statementConfigDao" ref="statementConfigDao"></property>
        <property name="statementObjectConfigDao" ref="statementObjectConfigDao"></property>
        <property name="statementJdbcTemplate" ref="statementJdbcTemplate"></property>
        <property name="upayJdbcTemplate" ref="upayJdbcTemplate"></property>
        <property name="upayBackJdbcTemplate" ref="upayBackJdbcTemplate"></property>
        <property name="upaySwipeJdbcTemplate" ref="upaySwipeJdbcTemplate"></property>
    </bean>
    <bean id="transactionService" class="com.wosai.upay.transaction.service.TransactionServiceImpl"></bean>
    <bean id="transactionServiceV2" class="com.wosai.upay.transaction.service.TransactionServiceV2Impl"></bean>
    <bean id="orderService" class="com.wosai.upay.transaction.service.OrderServiceImpl"/>
    <bean id="statementObjectConfigService" class="com.wosai.upay.transaction.service.StatementObjectConfigServiceImpl"></bean>
    <bean id="exportService" class="com.wosai.upay.transaction.service.ExportServiceImpl">
        <property name="monitorServiceConfig" ref="monitorServiceConfig"></property>
    </bean>
    <bean id="accountBookService" class="com.wosai.upay.transaction.service.api.AccountBookServiceImpl"></bean>
    <bean id="accountBookServiceOpen" class="com.wosai.upay.transaction.service.api.AccountBookServiceOpenImpl"></bean>
    <bean id="statementTaskService" class="com.wosai.upay.transaction.service.TaskLogServiceImpl"></bean>
    <bean id="businessService" class="com.wosai.upay.transaction.service.BusinessService"></bean>
    <bean id="ossFileUploaderService" class="com.wosai.upay.transaction.util.OssFileUploader"></bean>
    <bean id="cacheService" class="com.wosai.upay.transaction.service.CacheServiceImpl"></bean>
    <bean id="timeTaskService" class="com.wosai.upay.transaction.service.TimeTaskServiceImpl">
        <property name="upayTransactionServer" value="${upay-transaction.server}"></property>
    </bean>

    <bean id="externalGroupService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${user-service.server}rpc/group"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.GroupService"></property>
        <property name="serverName" value="user-service"/>
    </bean>


    <bean id="externalMerchantService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${core-business.server}rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantService"></property>
        <property name="serverName" value="core-business"/>
    </bean>


    <bean id="externalStoreService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${core-business.server}rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.StoreService"></property>
        <property name="serverName" value="core-business"/>
    </bean>

    <bean id="storeService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${upay.server}rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.upay.service.StoreService"></property>
        <property name="serverName" value="backend-upay"/>
    </bean>

    <bean id="externalTerminalService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${core-business.server}rpc/terminal"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TerminalService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="3000"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>
    <bean id="externalUserService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${user-service.server}rpc/user"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.UserService"></property>
        <property name="serverName" value="user-service"/>
    </bean>

    <bean id="externalExportService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${upay-transaction.server}rpc/export"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.ExportService"></property>
        <property name="serverName" value="upay-transaction"/>
    </bean>

    <bean id="externalDepartmentService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${user-service.server}rpc/department"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.DepartmentService"></property>
        <property name="serverName" value="user-service"/>
    </bean>

    <bean id = "appMerchantServie" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${app-backend.server}rpc/merchantconfig"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IMerchantService"></property>
        <property name="serverName" value="app-backend-process"/>
        <property name="connectionTimeoutMillis" value="3000"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean id="appStaffService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${app-backend.server}rpc/staff"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IStaffService"></property>
        <property name="serverName" value="app-backend-service"/>
        <property name="connectionTimeoutMillis" value="3000"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>


    <bean id="nexusAccountService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV150">
        <property name="serviceUrl" value="${rpc.service.url.ads.coeus.service}rpc/nexus/account"></property>
        <property name="serviceInterface" value="com.wosai.ads.coeus.api.service.NexusAccountService"></property>
        <property name="serverName" value="ads-coeus"/>
        <property name="connectionTimeoutMillis" value="3000"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>


    <bean name="/rpc/order" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="orderService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.OrderService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>

    <bean name="/rpc/transaction" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="transactionService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TransactionService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>

	<bean name="/rpc/transaction_v2" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="transactionServiceV2"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TransactionServiceV2"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>
    
    <!--pc桌面版接口为了与收钱吧的路径一致，所以配置下面的rpc路径-->
    <bean name="/rpc/upayorder" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="orderService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.OrderService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>


    <bean name="/rpc/statementObjectConfig" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="statementObjectConfigService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.StatementObjectConfigService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>

    <bean name="/rpc/export" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="exportService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.ExportService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>

    <bean name="/rpc/account_book_v4" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="accountBookService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.IAccountBookService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>


    <bean name="/rpc/account_book_open_v4" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="accountBookServiceOpen"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.IAccountBookServiceOpen"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>

    <bean name="/rpc/statementTask" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="statementTaskService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TaskLogService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>

    <bean name="/rpc/cache" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="cacheService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.CacheService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>

    <bean name="/rpc/timeTask" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporterTracerV150">
        <property name="service" ref="timeTaskService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TimeTaskService"/>
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>

    <bean class="com.wosai.upay.transaction.job.TakeOutTaskAndSubmitToExecutor"></bean>
    <bean class="com.wosai.upay.transaction.job.MonitorExecutorStatus"></bean>
    <bean class="com.wosai.upay.transaction.util.OrderUtil"></bean>
    <bean id="statementTransactionDetailUtil" class="com.wosai.upay.transaction.util.StatementTransactionUtil"/>
    <bean id="statementWalletUtils" class="com.wosai.upay.transaction.util.StatementWalletUtils"/>
    <bean class="com.wosai.upay.transaction.export.base.ExportHandler"/>
    <bean class="com.wosai.upay.transaction.util.ApplicationContextUtil"/>
    <bean class="com.wosai.common.rateLimit.proxy.RateLimiterPostProcessor"/>

    <util:map id="monitorServiceConfig">
        <entry key="exportServiceEnable" value="${export.service.enable:true}"></entry>
        <entry key="monitorHandleHeartBeatStopTaskServiceEnable"
               value="${monitor.heartbeat.stop.service.enable:true}"></entry>
        <entry key="monitorExecutorStatusServiceEnable" value="${monitor.executor.status.service.enable:true}"></entry>
    </util:map>

    <!-- 等价于 @EnableAsync， executor指定线程池 -->
    <task:annotation-driven executor="asyncExecutor"/>
    <!-- id指定线程池产生线程名称的前缀 -->
    <task:executor
            id="asyncExecutor"
            pool-size="50-100"
            queue-capacity="100"
            keep-alive="120"
            rejection-policy="CALLER_RUNS"/>

</beans>
