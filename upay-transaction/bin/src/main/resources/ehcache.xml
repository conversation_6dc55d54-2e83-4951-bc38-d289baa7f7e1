<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="ehcache.xsd"
         updateCheck="false">
    <diskStore path="java.io.tmpdir"/>

    <!-- objects are evicted from the cache every 60 seconds -->
    <cache name="foo"
           timeToLiveSeconds="60"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>
    <cache name="RsaKeyData"
           timeToLiveSeconds="0"
           maxElementsInMemory="1000"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="5"
           memoryStoreEvictionPolicy="LRU"/>

    <cache name="getMerchantSnByWosaiStoreIdOrTerminalSn"
           timeToLiveSeconds="0"
           eternal="false"
           overflowToDisk="false"
           maxBytesLocalHeap="50m"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="5"
           memoryStoreEvictionPolicy="LRU"/>

    <cache name="Industries"
           timeToLiveSeconds="600"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>
    <cache name="Level1Industries"
           timeToLiveSeconds="600"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>
    <cache name="LevelsIndustries"
           timeToLiveSeconds="600"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>
    <cache name="industriesNameMap"
           timeToLiveSeconds="600"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>

    <!-- 10秒失效 -->
    <cache name="systemConfigContent"
           timeToLiveSeconds="10"
           maxElementsInMemory="200"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>

</ehcache>
