<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         id="WebApp_ID" version="2.5">

    <display-name>Spring MVC</display-name>
    <description>Spring MVC web application</description>

    <context-param>
        <param-name>spring.profiles.active</param-name>
        <param-value>jdbc</param-value>
        <!-- Available profiles:
         <param-value>jdbc</param-value>
         <param-value>jpa</param-value> (in the case of plain JPA)
         <param-value>spring-data-jpa</param-value> (in the case of Spring Data JPA)
        -->
    </context-param>

    <!--
        - Location of the XML file that defines the root application context.
        - Applied by ContextLoaderServlet.
    -->
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:spring/business-config.xml, classpath:spring/tools-config.xml,
            classpath:spring/application-security.xml
        </param-value>
    </context-param>

    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>

    <!--
        - Servlet that dispatches request to registered handlers (Controller implementations).
    -->
    <servlet>
        <servlet-name>dispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:spring/mvc-core-config.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>dispatcher</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>


    <!-- used so we can use forms of method type 'PUT' and 'DELETE'
         see here: http://static.springsource.org/spring/docs/current/spring-framework-reference/html/view.html#rest-method-conversion
    -->
    <filter>
        <filter-name>httpMethodFilter</filter-name>
        <filter-class>org.springframework.web.filter.HiddenHttpMethodFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>httpMethodFilter</filter-name>
        <servlet-name>dispatcher</servlet-name>
    </filter-mapping>


    <!-- Uncomment the following to enable spring security filter chain -->
    <!--
    <filter>
      <filter-name>springSecurityFilterChain</filter-name>
      <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>

    <filter-mapping>
      <filter-name>springSecurityFilterChain</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>
    -->

    <!--
    <filter>
      <filter-name>wrapResponseFilter</filter-name>
      <filter-class>com.wosai.springmvc.filter.WrapResponseFilter</filter-class>
    </filter>

    <filter-mapping>
      <filter-name>wrapResponseFilter</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>
    -->
</web-app>
