<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai.upay</groupId>
        <artifactId>upay-transaction-parent</artifactId>
        <version>1.2.38intldb</version>
    </parent>
    <artifactId>upay-transaction</artifactId>
    <version>1.2.38intldb</version>
    <packaging>war</packaging>

    <dependencies>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
            <version>*******</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-transaction-api</artifactId>
            <version>1.2.38-ka-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.common</groupId>
            <artifactId>wosai-common</artifactId>
            <version>*******-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.framework.apollo</groupId>
                    <artifactId>apollo-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>2.8.36-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>user-service-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.4</version>
        </dependency>


        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-jdbc</artifactId>
            <version>${nextgen.version}</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>springmvc-customization</artifactId>
            <version>${nextgen.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>nextgen-model</artifactId>
            <version>${nextgen.version}</version>
        </dependency>


        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>spring4-boot-starter-service-jsonrpc</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>spring4-boot-starter-jdbc</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>spring4-boot-starter-redis</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>spring4-boot-starter-aop</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>spring4-boot-starter-testing</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.github.briandilley.jsonrpc4j</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>1.5.0</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.5.0</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
        </dependency>

        <!-- support xlsx -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.12</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.2</version>
        </dependency>

		<dependency>
		 <groupId>com.wosai</groupId>
		 <artifactId>wosai-brave150-api</artifactId>
		 <version>2.5</version>
		</dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.9</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>app-backend-api</artifactId>
            <version>1.1.7-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>wosai-common</artifactId>
                    <groupId>com.wosai.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-api</artifactId>
            <version>2.22-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.3</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-database-instrumentation-spring</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.20</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.ads</groupId>
            <artifactId>coeus-api</artifactId>
            <version>2.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <warName>upay-transaction</warName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>maven-jetty-plugin</artifactId>
                <configuration>
                    <connectors>
                        <connector implementation="org.mortbay.jetty.nio.SelectChannelConnector">
                            <port>11117</port>
                            <maxIdleTime>60000</maxIdleTime>
                        </connector>
                    </connectors>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/groovy</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.5</version>
                <executions>
                    <execution>
                        <id>copy-groovy</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${project.build.sourceDirectory}</directory>
                                    <includes>
                                        <include>**/*.groovy</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>${project.basedir}/src/main/groovy</directory>
                                    <includes>
                                        <include>**/*.groovy</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>


        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.1</version>
                    <configuration>
                        <compilerId>groovy-eclipse-compiler</compilerId>
                        <compilerArguments>
                            <Xlint/>
                        </compilerArguments>
                        <verbose>false</verbose>
                        <source>1.8</source>
                        <target>1.8</target>
                        <showWarnings>true</showWarnings>
                        <useIncrementalCompilation>true</useIncrementalCompilation>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.codehaus.groovy</groupId>
                            <artifactId>groovy-eclipse-compiler</artifactId>
                            <version>2.9.1-01</version>
                        </dependency>
                        <dependency>
                            <groupId>org.codehaus.groovy</groupId>
                            <artifactId>groovy-eclipse-batch</artifactId>
                            <version>2.3.7-01</version>
                        </dependency>
                    </dependencies>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.3</version>
                    <configuration>
                        <warName>upay-transaction</warName>
                    </configuration>
                </plugin>


                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.codehaus.mojo</groupId>
                                        <artifactId>build-helper-maven-plugin</artifactId>
                                        <versionRange>[1.0.0,)</versionRange>
                                        <goals>
                                            <goal>add-source</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
