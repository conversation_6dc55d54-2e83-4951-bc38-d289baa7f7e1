# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a payment transaction processing system built with Java Spring framework. The project consists of two main modules:
- `upay-transaction`: Main web application (WAR packaging)
- `upay-transaction-api`: API library (JAR packaging)

## Build Commands

### Maven Commands
```bash
# Clean and compile all modules
mvn clean compile

# Run tests for all modules
mvn test

# Package all modules
mvn package

# Build and skip tests
mvn clean package -DskipTests

# Run specific test class
mvn test -Dtest=com.wosai.upay.transaction.service.TransactionServiceImplTest

# Run specific test method
mvn test -Dtest=com.wosai.upay.transaction.service.TransactionServiceImplTest#testMethodName

# Install to local repository
mvn install

# Deploy to remote repository
mvn deploy
```

### Development Server
```bash
# Start Jetty server for development
mvn jetty:run -pl upay-transaction
# Server will start on port 11117
```

## Architecture Overview

### Technology Stack
- **Language**: Java 8
- **Framework**: Spring 4.x, Spring Boot 1.x (via spring4-boot)
- **Build Tool**: Maven
- **Database**: MySQL 8.x, HBase, Redis
- **Search**: Solr 7.x, Elasticsearch 6.x
- **Message Queue**: Kafka with Avro serialization
- **Testing**: JUnit 4, Mockito, PowerMock

### Key Components

#### Data Layer
- **HBase DAOs**: TransactionHBaseDao, OrderHBaseDao, WalletChangeLogHBaseDao
- **Redis Caching**: RedisMapCache for distributed caching
- **MySQL**: Primary database for transactional data

#### Service Layer
- **TransactionService**: Core transaction processing
- **OrderService**: Order management and processing
- **WalletService**: Wallet operations and balance tracking
- **ExportService**: Data export functionality
- **CrossMerchantService**: Cross-merchant transaction handling

#### API Layer
- **RPC Services**: AopRpc, CustomerTransactionRpc
- **REST Controllers**: HealthyController, MetricsController
- **JSON-RPC**: Via jsonrpc4j library

#### Infrastructure
- **MQ Handlers**: TradeConsumer, AnalyzeFacade for Kafka messages
- **Scheduled Tasks**: EsIndexScheduled for Elasticsearch indexing
- **Caching**: Multi-level caching with Redis and local caches

### Directory Structure

```
upay-transaction/
├── upay-transaction/          # Main web application
│   ├── src/main/java/
│   │   └── com/wosai/upay/transaction/
│   │       ├── config/        # Spring configuration
│   │       ├── controller/    # REST controllers
│   │       ├── service/       # Business logic
│   │       ├── dao/           # Data access objects
│   │       ├── model/         # Entity classes
│   │       └── util/          # Utility classes
│   └── src/main/resources/    # Configuration files
│       ├── application.properties
│       ├── logback.xml
│       └── spring/            # Spring XML configs
├── upay-transaction-api/      # API interfaces and models
│   ├── src/main/java/
│   │   └── com/wosai/upay/transaction/
│   │       ├── api/           # API interfaces
│   │       ├── model/         # DTOs and VO
│   │       ├── enums/         # Enumerations
│   │       └── constant/      # Constants
└── doc/                       # Documentation
    ├── hbase/                 # HBase schema docs
    ├── solr/                  # Solr configuration
    └── datax/                 # Data import/export scripts
```

## Testing

### Test Structure
- Unit tests: `src/test/java/`
- Test classes follow naming convention: `*Test.java`
- Integration tests use Spring test framework with `@SpringJUnit4ClassRunner`

### Running Tests
```bash
# Run all tests
mvn test

# Run tests for specific module
mvn test -pl upay-transaction

# Run tests with coverage
mvn jacoco:prepare-agent test jacoco:report
```

## Configuration

### Environment Configuration
- **Apollo**: Centralized configuration management
- **Profiles**: Different environments via `flavor-{env}.properties`
- **Database**: Configured via `datasource-config.xml`

### Key Configuration Files
- `application.properties`: Main application properties
- `logback.xml`: Logging configuration
- `spring/business-config.xml`: Business beans
- `spring/datasource-config.xml`: Database configuration
- `spring/redis-config.xml`: Redis configuration

## Development Workflow

### Common Tasks
1. **Build**: `mvn clean package`
2. **Test**: `mvn test`
3. **Run**: `mvn jetty:run -pl upay-transaction`
4. **Debug**: Configure remote debugging on port 5005

### Code Style
- Java 8 syntax features (lambdas, streams)
- Lombok for boilerplate reduction
- Spring dependency injection patterns
- Repository pattern for data access
- Service layer with transaction management

### Important Notes
- Uses custom Spring 4.x fork (`spring4-boot`)
- HBase integration via Aliyun HBase client
- Kafka message processing with Avro serialization
- Multi-tenant architecture support
- Comprehensive logging via Logback with Logstash encoder