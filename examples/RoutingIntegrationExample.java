package com.wosai.upay.transaction.examples;

import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.routing.RoutingTransactionService;
import com.wosai.upay.transaction.service.routing.QueryRoutingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Example of how to use the new routing services for MySQL/HBase/Solr migration
 */
@Component
public class RoutingIntegrationExample {

    @Autowired
    private RoutingTransactionService routingTransactionService;

    @Autowired
    private QueryRoutingService queryRoutingService;

    /**
     * Example: Query transactions with automatic routing
     */
    public void exampleTransactionQuery() {
        TransactionHBaseQuery query = new TransactionHBaseQuery();
        query.setMerchantIds(Arrays.asList("merchant123"));
        query.setStartTime(1609459200000L); // 2021-01-01
        query.setEndTime(1640995200000L);   // 2022-01-01
        query.setLimit(100);
        query.setOffset(0);

        // The service automatically routes between MySQL and HBase/Solr
        List<Map<String, Object>> transactions = routingTransactionService.queryList(query);
        
        System.out.println("Found " + transactions.size() + " transactions");
    }

    /**
     * Example: Check routing decision for debugging
     */
    public void checkRoutingDecision() {
        String merchantId = "merchant123";
        String decision = queryRoutingService.getRoutingDecision(merchantId);
        System.out.println(decision);
        
        // Output will show:
        // Routing decision for merchant merchant123:
        // - MySQL enabled: true
        // - Percentage: 50%
        // - Whitelist: merchant123,merchant456
        // - Blacklist: 
        // - Use MySQL: true
    }

    /**
     * Example: Migration validation
     */
    public void validateMigration(String merchantId) {
        TransactionHBaseQuery query = new TransactionHBaseQuery();
        query.setMerchantIds(Arrays.asList(merchantId));
        query.setStartTime(1609459200000L);
        query.setEndTime(1640995200000L);

        // Force MySQL usage for testing
        System.setProperty("query.routing.mysql.enabled", "true");
        System.setProperty("query.routing.mysql.percentage", "100");
        
        List<Map<String, Object>> mysqlResults = routingTransactionService.queryList(query);
        
        System.out.println("MySQL results: " + mysqlResults.size());
        
        // Compare with expected results or log for manual validation
        mysqlResults.forEach(tx -> {
            System.out.println("Transaction: " + tx.get("tsn") + ", Amount: " + tx.get("original_amount"));
        });
    }
}